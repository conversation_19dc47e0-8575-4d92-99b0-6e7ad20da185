/**
 * File Helper Utilities
 * Centralized utilities for file name processing and manipulation
 */

/**
 * Strips UUID patterns from filenames to create cleaner display names
 * @param {string} text - The text that may contain UUID patterns
 * @returns {string} - Text with UUID patterns removed
 */
export function stripUuidFromText(text) {
  if (!text || typeof text !== "string") return text;

  // Remove UUID patterns (8-4-4-4-12 format) and surrounding hyphens
  // This handles cases like "filename-abc123de-f456-7890-abcd-ef1234567890.pdf"
  return text
    .replace(
      /-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi,
      ""
    )
    .replace(
      /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-?/gi,
      ""
    )
    .replace(/--+/g, "-") // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
}

/**
 * Comprehensive file name cleaning function
 * Removes UUIDs, file extensions, and other artifacts for cleaner display
 * @param {string} filename - The filename to clean
 * @param {Object} options - Configuration options
 * @param {boolean} options.removeExtension - Whether to remove file extensions (default: true)
 * @param {boolean} options.removeUuid - Whether to remove UUID patterns (default: true)
 * @returns {string} - Cleaned filename
 */
export function cleanFileName(filename, options = {}) {
  const { removeExtension = true, removeUuid = true } = options;

  if (!filename) return "";

  try {
    // Make sure filename is a string
    const filenameStr = String(filename);
    let cleanName = filenameStr;

    // Remove file extension if requested
    if (removeExtension) {
      cleanName = cleanName.replace(/\.[^/.]+$/, "");
    }

    // Remove UUID patterns if requested
    if (removeUuid) {
      cleanName = stripUuidFromText(cleanName);
    }

    return cleanName;
  } catch (error) {
    console.error("Error cleaning filename:", error, filename);
    return String(filename || "");
  }
}

/**
 * Strips UUID v4 and JSON from file names (for search compatibility)
 * @param {string} input - The input string to process
 * @returns {string} - Processed string with UUID and JSON removed
 */
export function stripUuidAndJsonFromString(input = "") {
  const uuidPattern =
    /-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/g;
  const jsonPattern = /\.json$/;

  return input
    ?.replace(uuidPattern, "") // remove v4 uuid
    ?.replace(jsonPattern, "") // remove trailing .json
    ?.replace(/-/g, " "); // turn slugged names into spaces
}

/**
 * Removes file extension from a filename
 * @param {string} filename - The filename to process
 * @returns {string} - Filename without extension
 */
export function removeFileExtension(filename) {
  if (!filename) return "";
  return filename.replace(/\.[^/.]+$/, "");
}
