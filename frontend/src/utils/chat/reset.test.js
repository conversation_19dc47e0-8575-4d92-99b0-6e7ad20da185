import { handleChatResponse } from "./index";
import showToast from "@/utils/toast";

// Mock the progress store
const mockFinishProcess = jest.fn();
const mockSetError = jest.fn();
const mockGetThreadState = jest.fn().mockReturnValue(null);

jest.mock("@/stores/progressStore", () => ({
  __esModule: true,
  default: {
    getState: jest.fn(() => ({
      finishProcess: mockFinishProcess,
      setError: mockSetError,
      getThreadState: mockGetThreadState,
    })),
  },
}));

// Mock showToast
jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock i18next
jest.mock("i18next", () => ({
  t: (key) => key,
}));

// Mock other dependencies
jest.mock("@/components/Sidebar/ActiveWorkspaces/ThreadContainer", () => ({
  THREAD_RENAME_EVENT: "thread-rename",
}));

jest.mock("@/components/contexts/TTSProvider", () => ({
  emitAssistantMessageCompleteEvent: jest.fn(),
}));

jest.mock("../tokenizer", () => ({
  tokenManager: {
    encode: jest.fn().mockReturnValue([]),
  },
}));

jest.mock("uuid", () => ({
  v4: jest.fn(() => "mock-uuid"),
}));

describe("Chat Reset Functionality", () => {
  let mockSetLoadingResponse;
  let mockSetChatHistory;
  let threadSlug;

  beforeEach(() => {
    mockSetLoadingResponse = jest.fn();
    mockSetChatHistory = jest.fn();
    threadSlug = "test-thread";

    // Reset all mocks
    jest.clearAllMocks();
    mockGetThreadState.mockReturnValue(null);
    showToast.mockClear();
  });

  describe("Reset Chat Action", () => {
    test("should handle reset_chat action correctly", () => {
      const existingChatHistory = [
        {
          uuid: "user-msg-1",
          content: "First user message",
          role: "user",
        },
        {
          uuid: "assistant-msg-1",
          content: "First assistant response",
          role: "assistant",
        },
        {
          uuid: "user-msg-2",
          content: "Second user message",
          role: "user",
        },
        {
          uuid: "assistant-msg-2",
          content: "Second assistant response",
          role: "assistant",
        },
      ];

      const resetResult = {
        uuid: "reset-uuid-123",
        type: "textResponse",
        textResponse: "show-toast.chatHistory.reset",
        action: "reset_chat",
        sources: [],
        close: true,
        error: false,
      };

      handleChatResponse(
        resetResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        existingChatHistory,
        threadSlug
      );

      // Should set loading to false
      expect(mockSetLoadingResponse).toHaveBeenCalledWith(false);

      // Should clear chat history (set to empty array)
      expect(mockSetChatHistory).toHaveBeenCalledWith([]);

      // Should show success toast
      expect(showToast).toHaveBeenCalledWith(
        "show-toast.chatHistory.reset",
        "success"
      );

      // Should finish the process
      setTimeout(() => {
        expect(mockFinishProcess).toHaveBeenCalledWith(threadSlug);
      }, 150);
    });

    test("should handle reset_chat action with empty chat history", () => {
      const resetResult = {
        uuid: "reset-empty-uuid",
        type: "textResponse",
        textResponse: "show-toast.chatHistory.reset",
        action: "reset_chat",
        sources: [],
        close: true,
        error: false,
      };

      handleChatResponse(
        resetResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [], // Already empty history
        threadSlug
      );

      // Should still clear chat history
      expect(mockSetChatHistory).toHaveBeenCalledWith([]);

      // Should show success toast
      expect(showToast).toHaveBeenCalledWith(
        "show-toast.chatHistory.reset",
        "success"
      );
    });

    test("should handle reset_chat action with error flag", () => {
      const resetResult = {
        uuid: "reset-error-uuid",
        type: "textResponse",
        textResponse: "Failed to reset chat history.",
        action: "reset_chat",
        sources: [],
        close: true,
        error: true,
      };

      handleChatResponse(
        resetResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [{ uuid: "msg-1", content: "test", role: "user" }],
        threadSlug
      );

      // When error flag is true, error handling runs instead of reset action
      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      // Should NOT show toast (error handling path doesn't process reset action)
      expect(showToast).not.toHaveBeenCalled();
    });

    test("should not clear history for other actions", () => {
      const nonResetResult = {
        uuid: "other-action-uuid",
        type: "textResponse",
        textResponse: "Some other response.",
        action: "other_action",
        sources: [],
        close: true,
        error: false,
      };

      const existingHistory = [
        { uuid: "msg-1", content: "test", role: "user" },
      ];

      handleChatResponse(
        nonResetResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        existingHistory,
        threadSlug
      );

      // Should NOT clear chat history for non-reset actions
      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      // Test the update function
      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction(existingHistory);

      // Should add the new message, not clear history
      expect(newHistory).toHaveLength(2);
      expect(newHistory[0]).toEqual(existingHistory[0]);

      // Should NOT show reset toast
      expect(showToast).not.toHaveBeenCalledWith(
        "chatHistory.reset",
        "success"
      );
    });

    test("should handle textResponse without action field", () => {
      const normalResult = {
        uuid: "normal-response-uuid",
        type: "textResponse",
        textResponse: "Normal response without action.",
        sources: [],
        close: true,
        error: false,
      };

      const existingHistory = [
        { uuid: "msg-1", content: "test", role: "user" },
      ];

      handleChatResponse(
        normalResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        existingHistory,
        threadSlug
      );

      // Should add to history normally
      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction(existingHistory);

      expect(newHistory).toHaveLength(2);
      expect(newHistory[1]).toMatchObject({
        content: "Normal response without action.",
        role: "assistant",
      });

      // Should NOT show reset toast
      expect(showToast).not.toHaveBeenCalledWith(
        "chatHistory.reset",
        "success"
      );
    });
  });

  describe("Integration with Progress System", () => {
    test("should finish progress tracking after reset", () => {
      const resetResult = {
        uuid: "reset-progress-uuid",
        type: "textResponse",
        textResponse: "Chat reset complete.",
        action: "reset_chat",
        close: true,
        error: false,
      };

      handleChatResponse(
        resetResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [{ uuid: "msg-1", content: "test", role: "user" }],
        threadSlug
      );

      // Should finish progress tracking
      setTimeout(() => {
        expect(mockFinishProcess).toHaveBeenCalledWith(threadSlug);
      }, 150);
    });

    test("should clear loading state on reset", () => {
      const resetResult = {
        uuid: "reset-loading-uuid",
        type: "textResponse",
        textResponse: "Reset successful.",
        action: "reset_chat",
        close: true,
        error: false,
      };

      handleChatResponse(
        resetResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [],
        threadSlug
      );

      // Should clear loading state
      expect(mockSetLoadingResponse).toHaveBeenCalledWith(false);
    });
  });

  describe("Reset vs Normal Document Completion", () => {
    test("should distinguish between reset and document completion", () => {
      // First test reset action
      const resetResult = {
        uuid: "reset-uuid",
        type: "textResponse",
        textResponse: "Chat reset.",
        action: "reset_chat",
        close: true,
        error: false,
      };

      handleChatResponse(
        resetResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [{ uuid: "msg-1", content: "test", role: "user" }],
        threadSlug
      );

      // Should clear history for reset
      expect(mockSetChatHistory).toHaveBeenNthCalledWith(1, []);

      // Reset mocks
      jest.clearAllMocks();

      // Then test normal document completion
      const documentResult = {
        uuid: "doc-uuid",
        type: "textResponse",
        textResponse: "# Generated Document\n\nContent here.",
        close: true,
        error: false,
        chatId: "doc-chat-id",
      };

      handleChatResponse(
        documentResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        [{ uuid: "msg-1", content: "test", role: "user" }],
        threadSlug
      );

      // Should add to history for document completion
      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction([
        { uuid: "msg-1", content: "test", role: "user" },
      ]);

      expect(newHistory).toHaveLength(2);
      expect(newHistory[1]).toMatchObject({
        content: "# Generated Document\n\nContent here.",
        chatId: "doc-chat-id",
      });
    });
  });
});
