import { THREAD_RENAME_EVENT } from "@/components/Sidebar/ActiveWorkspaces/ThreadContainer";
import { emitAssistantMessageCompleteEvent } from "@/components/contexts/TTSProvider";
import i18next from "i18next";
import showToast from "@/utils/toast";
import { tokenManager } from "../tokenizer";
import { v4 } from "uuid";
import useProgressStore from "@/stores/progressStore";

// Function to handle chat response updates
export function handleChatResponse(
  chatResult,
  setLoadingResponse,
  setChatHistory,
  _remHistory,
  threadSlug
) {
  const data = chatResult;
  const { updateProgress, finishProcess, setError, getThreadState } =
    useProgressStore.getState();

  if (data.type === "abort" || data.error) {
    setLoadingResponse(false);

    const threadState = getThreadState(threadSlug);
    const isProcessError = !!threadState;

    setError(threadSlug, data.textResponse || data.error);

    if (isProcessError) {
      setChatHistory((prev) => {
        const newHistory = [...prev];
        const lastMsgIndex = newHistory.length - 1;
        if (lastMsgIndex >= 0 && newHistory[lastMsgIndex].pending) {
          return newHistory.slice(0, lastMsgIndex);
        }
        return newHistory;
      });
    } else {
      setChatHistory((prev) => {
        const newHistory = [...prev];
        const lastMsgIndex = newHistory.length - 1;
        if (lastMsgIndex >= 0 && newHistory[lastMsgIndex].pending) {
          newHistory[lastMsgIndex] = {
            ...newHistory[lastMsgIndex],
            content:
              data.textResponse ||
              "An error occurred while streaming the response.",
            error: true,
            closed: true,
            pending: false,
          };
          return newHistory;
        }
        return [
          ...newHistory,
          {
            uuid: data.uuid || v4(),
            content:
              data.textResponse ||
              "An error occurred while streaming the response.",
            type: data.type,
            role: "assistant",
            error: true,
            closed: true,
            sources: [],
          },
        ];
      });
    }
    return;
  }

  if (data.type === "stopGeneration") {
    console.log(
      "[StreamAbort] Handling stopGeneration - finalizing current response"
    );
    setLoadingResponse(false);
    let finalChatId = null;

    setChatHistory((prev) => {
      const newHistory = [...prev];
      const lastMsgIndex = newHistory.findLastIndex(
        (msg) => msg.role === "assistant" && (msg.pending || msg.animate)
      );

      if (lastMsgIndex >= 0) {
        newHistory[lastMsgIndex] = {
          ...newHistory[lastMsgIndex],
          closed: true,
          pending: false,
          animate: false,
        };
        finalChatId = newHistory[lastMsgIndex].chatId;
      }

      return newHistory;
    });

    if (finalChatId) {
      emitAssistantMessageCompleteEvent(finalChatId);
    }
    if (threadSlug) finishProcess(threadSlug);
    return;
  }

  if (data.type === "progress") {
    const threadState = getThreadState(threadSlug);
    if (!threadState || !threadState.isActive) {
      const { startProcess } = useProgressStore.getState();
      startProcess(threadSlug, data.totalSteps, data.flowType);
    }
    updateProgress(data, threadSlug);
    return;
  }

  if (data.type === "process_complete") {
    if (threadSlug) finishProcess(threadSlug);
    return;
  }

  if (data.type === "textResponse") {
    // Handle reset_chat action
    if (data.action === "reset_chat") {
      setLoadingResponse(false);

      // Clear chat history
      setChatHistory([]);

      // Show toast message if provided
      if (data.textResponse && data.textResponse.startsWith("show-toast.")) {
        const toastKey = data.textResponse;
        showToast(i18next.t(toastKey), "success");
      }

      if (threadSlug) {
        setTimeout(() => finishProcess(threadSlug), 100);
      }
      return;
    }

    // Handle complete text responses (like from CDB flows)
    setChatHistory((prevChatHistory) => {
      return [
        ...prevChatHistory,
        {
          uuid: data.uuid || v4(),
          content: data.textResponse || "",
          type: data.type,
          role: "assistant",
          error: data.error || false,
          closed: data.close || false,
          pending: false,
          animate: !data.close,
          sources: data.sources || [],
          chatId: data.chatId,
          metrics: data.metrics || {},
        },
      ];
    });

    // Handle completion
    if (data.close) {
      setLoadingResponse(false);
      if (threadSlug) {
        setTimeout(() => finishProcess(threadSlug), 100);
      }
      emitAssistantMessageCompleteEvent(data.chatId);
    }
    return;
  }

  if (
    data.type === "textResponseChunk" ||
    data.type === "finalizeResponseStream"
  ) {
    setChatHistory((prevChatHistory) => {
      let chatIdx = prevChatHistory.findIndex(
        (chat) => chat.uuid === data.uuid
      );
      if (chatIdx === -1) {
        chatIdx = prevChatHistory.findLastIndex(
          (chat) => chat.role === "assistant" && chat.pending === true
        );
      }

      if (chatIdx === -1) {
        console.warn(
          `Stream chunk for ${data.uuid} received, but no matching or pending message found.`
        );
        return prevChatHistory;
      }

      const existingMessage = prevChatHistory[chatIdx];

      // Only concatenate content for textResponseChunk events
      // finalizeResponseStream should not add content as it contains the full response
      const newContent =
        data.type === "textResponseChunk"
          ? (existingMessage.content || "") + (data.textResponse || "")
          : existingMessage.content || "";

      const updatedMessage = {
        ...existingMessage,
        uuid: data.uuid,
        content: newContent,
        sources:
          data.sources?.length > 0 ? data.sources : existingMessage.sources,
        type: data.type,
        role: "assistant",
        error: data.error,
        closed: data.close,
        pending: false,
        animate: !data.close,
        chatId: data.chatId || existingMessage.chatId,
        metrics: data.metrics || existingMessage.metrics,
      };

      const newHistory = [...prevChatHistory];
      newHistory[chatIdx] = updatedMessage;
      return newHistory;
    });

    // Ensure progress is finished when streaming completes
    if (data.close || data.type === "finalizeResponseStream") {
      setLoadingResponse(false);
      if (threadSlug) {
        // Use a small delay to ensure the UI updates before cleaning up progress
        setTimeout(() => finishProcess(threadSlug), 100);
      }
      emitAssistantMessageCompleteEvent(data.chatId);
    }
  }
}

export function chatPrompt(workspace) {
  return (
    workspace?.openAiPrompt ??
    "Given the following conversation, relevant context, and a follow up question, reply with an answer to the current question the user is asking. Return only your response to the question given the above information following the users instructions as needed."
  );
}

export function chatQueryRefusalResponse(workspace) {
  return (
    workspace?.queryRefusalResponse ??
    "There is no relevant information in this workspace to answer your query."
  );
}

export function truncatePrompt(prompt, maxTokens = 8000) {
  return tokenManager.truncateToTokens(prompt, maxTokens);
}
