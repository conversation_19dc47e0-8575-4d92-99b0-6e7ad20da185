import { handleChatResponse } from "./index";

// Mock the progress store - define before jest.mock
const mockUpdateProgress = jest.fn();
const mockFinishProcess = jest.fn();
const mockSetError = jest.fn();
const mockGetThreadState = jest.fn().mockReturnValue(null);
const mockStartProcess = jest.fn();

jest.mock("@/stores/progressStore", () => ({
  __esModule: true,
  default: {
    getState: jest.fn(() => ({
      updateProgress: mockUpdateProgress,
      finishProcess: mockFinishProcess,
      setError: mockSetError,
      getThreadState: mockGetThreadState,
      startProcess: mockStartProcess,
    })),
  },
}));

// Mock showToast
jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock i18next
jest.mock("i18next", () => ({
  t: (key) => key,
}));

// Mock other dependencies
jest.mock("@/components/Sidebar/ActiveWorkspaces/ThreadContainer", () => ({
  THREAD_RENAME_EVENT: "thread-rename",
}));

jest.mock("@/components/contexts/TTSProvider", () => ({
  emitAssistantMessageCompleteEvent: jest.fn(),
}));

jest.mock("../tokenizer", () => ({
  tokenManager: {
    encode: jest.fn().mockReturnValue([]),
  },
}));

jest.mock("uuid", () => ({
  v4: jest.fn(() => "mock-uuid"),
}));

describe("handleChatResponse", () => {
  let mockSetLoadingResponse;
  let mockSetChatHistory;
  let mockRemHistory;
  let threadSlug;

  beforeEach(() => {
    mockSetLoadingResponse = jest.fn();
    mockSetChatHistory = jest.fn();
    mockRemHistory = [];
    threadSlug = "test-thread";

    // Reset progress store mocks
    mockUpdateProgress.mockClear();
    mockFinishProcess.mockClear();
    mockSetError.mockClear();
    mockGetThreadState.mockReturnValue(null);
    mockStartProcess.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("CDB Response Handling", () => {
    test("should handle textResponse type correctly (creates new complete message)", () => {
      const chatResult = {
        uuid: "test-uuid-1",
        type: "textResponse",
        textResponse: "Complete CDB document content here",
        sources: [],
        close: false,
        error: false,
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      // Simulate the function call with empty history
      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction([]);

      expect(newHistory).toHaveLength(1);
      expect(newHistory[0]).toMatchObject({
        uuid: "test-uuid-1",
        content: "Complete CDB document content here",
        type: "textResponse",
        role: "assistant",
        error: false,
        closed: false,
        sources: [],
      });
    });

    test("should handle textResponseChunk with existing pending message", () => {
      const existingHistory = [
        {
          uuid: "test-uuid-2",
          content: "",
          role: "assistant",
          pending: true,
          animate: true,
        },
      ];

      const chatResult = {
        uuid: "test-uuid-2",
        type: "textResponseChunk",
        textResponse: "Streaming content chunk",
        sources: [],
        close: false,
        error: false,
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      // Simulate the function call with existing pending message
      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction(existingHistory);

      expect(newHistory).toHaveLength(1);
      expect(newHistory[0]).toMatchObject({
        uuid: "test-uuid-2",
        content: "Streaming content chunk",
        role: "assistant",
        pending: false,
        animate: true,
      });
    });

    test("should fail gracefully when textResponseChunk has no pending message (CDB bug scenario)", () => {
      const consoleSpy = jest
        .spyOn(console, "warn")
        .mockImplementation(() => {});

      const chatResult = {
        uuid: "test-uuid-orphan",
        type: "textResponseChunk",
        textResponse: "This content would disappear in the old CDB bug!",
        sources: [],
        close: false,
        error: false,
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      // Simulate the function call with empty history (no pending message)
      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction([]);

      // The history should remain unchanged because there's no pending message to attach to
      expect(newHistory).toHaveLength(0);
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          "Stream chunk for test-uuid-orphan received, but no matching or pending message found."
        )
      );

      consoleSpy.mockRestore();
    });

    test("should handle finalizeResponseStream correctly", () => {
      const existingHistory = [
        {
          uuid: "test-uuid-finalize",
          content: "Existing content",
          role: "assistant",
          pending: false,
          animate: true,
        },
      ];

      const chatResult = {
        uuid: "test-uuid-finalize",
        type: "finalizeResponseStream",
        textResponse: "",
        sources: [],
        close: true,
        error: false,
        chatId: "chat-123",
        metrics: { tokens: 150 },
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      expect(mockSetLoadingResponse).toHaveBeenCalledWith(false);
      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      // Simulate the function call
      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction(existingHistory);

      expect(newHistory[0]).toMatchObject({
        uuid: "test-uuid-finalize",
        content: "Existing content", // Content should be preserved
        closed: true,
        pending: false,
        animate: false, // Animation should stop
        chatId: "chat-123",
        metrics: { tokens: 150 },
      });

      // Should finish the process
      setTimeout(() => {
        expect(mockFinishProcess).toHaveBeenCalledWith(threadSlug);
      }, 150);
    });

    test("should handle abort/error responses correctly", () => {
      const chatResult = {
        uuid: "test-uuid-error",
        type: "abort",
        textResponse: "An error occurred during processing",
        error: true,
        sources: [],
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      expect(mockSetLoadingResponse).toHaveBeenCalledWith(false);
      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction([]);

      expect(newHistory).toHaveLength(1);
      expect(newHistory[0]).toMatchObject({
        uuid: "test-uuid-error",
        content: "An error occurred during processing",
        error: true,
        closed: true,
        sources: [],
      });
    });

    test("should handle progress updates correctly", () => {
      const chatResult = {
        type: "progress",
        step: 3,
        progress: 50,
        message: "Processing documents...",
        totalSteps: 7,
        flowType: "main",
      };

      const testMockUpdateProgress = jest.fn();
      const testMockStartProcess = jest.fn();

      // Reset mock functions for this test
      mockUpdateProgress.mockImplementation(testMockUpdateProgress);
      mockStartProcess.mockImplementation(testMockStartProcess);

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      expect(testMockStartProcess).toHaveBeenCalledWith(threadSlug, 7, "main");
      expect(testMockUpdateProgress).toHaveBeenCalledWith(
        chatResult,
        threadSlug
      );
    });

    test("should handle stopGeneration correctly", () => {
      const existingHistory = [
        {
          uuid: "test-uuid-stop",
          content: "Partial content before stop",
          role: "assistant",
          pending: true,
          animate: true,
        },
      ];

      const chatResult = {
        type: "stopGeneration",
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      expect(mockSetLoadingResponse).toHaveBeenCalledWith(false);
      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction(existingHistory);

      expect(newHistory[0]).toMatchObject({
        closed: true,
        pending: false,
        animate: false,
      });
    });

    test("should handle reset_chat action correctly", () => {
      const existingHistory = [
        {
          uuid: "msg-1",
          content: "Previous message 1",
          role: "user",
        },
        {
          uuid: "msg-2",
          content: "Previous message 2",
          role: "assistant",
        },
      ];

      const chatResult = {
        uuid: "reset-uuid",
        type: "textResponse",
        action: "reset_chat",
        textResponse: "show-toast.chat-memory-reset",
        close: true,
        error: false,
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      // Should clear chat history
      expect(mockSetChatHistory).toHaveBeenCalledWith([]);

      // Should set loading to false
      expect(mockSetLoadingResponse).toHaveBeenCalledWith(false);

      // Should finish the process after timeout
      setTimeout(() => {
        expect(mockFinishProcess).toHaveBeenCalledWith(threadSlug);
      }, 150);
    });
  });

  describe("Edge Cases", () => {
    test("should handle missing textResponse gracefully", () => {
      const chatResult = {
        uuid: "test-uuid-missing",
        type: "textResponse",
        // textResponse is missing
        sources: [],
        close: false,
        error: false,
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      expect(mockSetChatHistory).toHaveBeenCalledWith(expect.any(Function));

      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction([]);

      expect(newHistory[0].content).toBe(""); // Should handle missing content gracefully
    });

    test("should handle malformed chatResult gracefully", () => {
      const chatResult = {}; // Empty object

      expect(() => {
        handleChatResponse(
          chatResult,
          mockSetLoadingResponse,
          mockSetChatHistory,
          mockRemHistory,
          threadSlug
        );
      }).not.toThrow();
    });

    test("should prioritize UUID matching over pending message search for textResponseChunk", () => {
      const existingHistory = [
        {
          uuid: "other-uuid",
          content: "",
          role: "assistant",
          pending: true,
          animate: true,
        },
        {
          uuid: "target-uuid",
          content: "Existing content",
          role: "assistant",
          pending: false,
          animate: false,
        },
      ];

      const chatResult = {
        uuid: "target-uuid",
        type: "textResponseChunk",
        textResponse: " - additional content",
        sources: [],
        close: false,
        error: false,
      };

      handleChatResponse(
        chatResult,
        mockSetLoadingResponse,
        mockSetChatHistory,
        mockRemHistory,
        threadSlug
      );

      const updateFunction = mockSetChatHistory.mock.calls[0][0];
      const newHistory = updateFunction(existingHistory);

      // Should append to the message with matching UUID, not the pending one
      expect(newHistory[1].content).toBe(
        "Existing content - additional content"
      );
      expect(newHistory[0].content).toBe(""); // Pending message should be unchanged
    });
  });
});
