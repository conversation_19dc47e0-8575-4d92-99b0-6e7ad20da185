/**
 * Progress calculation utilities
 * Utilities for calculating and managing progress states
 */

/**
 * Calculate incremental progress based on main steps and sub-tasks
 * @param {number} currentStep - Current main step (1-based)
 * @param {number} totalSteps - Total number of main steps
 * @param {Array} stepDetails - Array of step details with sub-tasks
 * @returns {number} Progress percentage (0-100)
 */
export const calculateIncrementalProgress = (
  currentStep,
  totalSteps,
  stepDetails
) => {
  if (totalSteps === 0) return 100;
  if (currentStep < 1) return 0;

  let totalProgress = 0;
  const progressPerStep = 100 / totalSteps;

  // Calculate progress for each step
  for (let step = 1; step <= totalSteps; step++) {
    const stepDetail = stepDetails.find((detail) => detail.step === step);

    if (step < currentStep) {
      // Previous steps are fully complete
      totalProgress += progressPerStep;
    } else if (step === currentStep) {
      // Current step - calculate based on sub-tasks
      if (stepDetail && stepDetail.subTasks && stepDetail.subTasks.length > 0) {
        const actualSubTasks = stepDetail.subTasks.filter(
          (st) => !st.isPlaceholder
        );
        const completedSubTasks = actualSubTasks.filter(
          (st) => st.status === "complete"
        ).length;

        // Calculate expected total sub-tasks
        let expectedTotal = stepDetail.expectedTotal || 0;

        // If we don't have an expected total, use the current actual sub-tasks count
        if (expectedTotal === 0) {
          expectedTotal = Math.max(actualSubTasks.length, 1);
        }

        // Calculate sub-task completion percentage
        const subTaskProgress =
          expectedTotal > 0
            ? Math.min(completedSubTasks / expectedTotal, 1)
            : 0;

        // Add partial progress for current step
        totalProgress += progressPerStep * subTaskProgress;
      } else {
        // No sub-tasks, assume some progress for being on this step
        totalProgress += progressPerStep * 0.1; // 10% progress for starting the step
      }
    }
    // Future steps (step > currentStep) contribute 0 progress
  }

  return Math.round(Math.min(totalProgress, 100));
};
