import {
  extractTotalHours,
  storeEstimationResult,
  getLatestEstimationForThread,
  cleanupOldEstimations,
  initializeManualWorkEstimation,
} from "../manualWorkEstimation";

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
};

Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
  writable: true,
});

// Mock setInterval and clearInterval
const mockSetInterval = jest.fn();
const mockClearInterval = jest.fn();
global.setInterval = mockSetInterval;
global.clearInterval = mockClearInterval;

describe("manualWorkEstimation utilities", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    mockLocalStorage.length = 0;
    mockLocalStorage.key.mockReturnValue(null);
  });

  describe("extractTotalHours", () => {
    it("should extract hours from JSON format with TOTAL_TIME_ESTIMATION", () => {
      const jsonResponse =
        '{"TOTAL_TIME_ESTIMATION": 2.5, "breakdown": "Analysis: 1.5 hours, Writing: 1 hour"}';

      const result = extractTotalHours(jsonResponse);
      expect(result).toBe(2.5);
    });

    it("should extract hours from English text patterns", () => {
      const testCases = [
        { text: "Total estimated time: 3.5 hours", expected: 3.5 },
        { text: "Estimated time: 2 hours", expected: 2 },
        { text: "Total: 4.25 hours", expected: 4.25 },
        { text: "Time required: 1.75 hours", expected: 1.75 },
        { text: "Duration: 6 hours", expected: 6 },
      ];

      testCases.forEach(({ text, expected }) => {
        expect(extractTotalHours(text)).toBe(expected);
      });
    });

    it("should extract hours from Swedish text patterns", () => {
      const testCases = [
        { text: "Total beräknad tid: 2.5 timmar", expected: 2.5 },
        { text: "Uppskattad tid: 3 timmar", expected: 3 },
        { text: "Totalt: 1.5 timmar", expected: 1.5 },
      ];

      testCases.forEach(({ text, expected }) => {
        expect(extractTotalHours(text)).toBe(expected);
      });
    });

    it("should extract hours from French text patterns", () => {
      const testCases = [
        { text: "Temps total estimé: 4.5 heures", expected: 4.5 },
        { text: "Durée estimée: 2 heures", expected: 2 },
        { text: "Total: 3.25 heures", expected: 3.25 },
      ];

      testCases.forEach(({ text, expected }) => {
        expect(extractTotalHours(text)).toBe(expected);
      });
    });

    it("should extract hours from German text patterns", () => {
      const testCases = [
        { text: "Geschätzte Gesamtzeit: 5.5 Stunden", expected: 5.5 },
        { text: "Dauer: 2.75 Stunden", expected: 2.75 },
        { text: "Insgesamt: 4 Stunden", expected: 4 },
      ];

      testCases.forEach(({ text, expected }) => {
        expect(extractTotalHours(text)).toBe(expected);
      });
    });

    it("should extract hours from Norwegian text patterns", () => {
      const testCases = [
        { text: "Total estimert tid: 3.5 timer", expected: 3.5 },
        { text: "Varighet: 2 timer", expected: 2 },
        { text: "Totalt: 4.25 timer", expected: 4.25 },
      ];

      testCases.forEach(({ text, expected }) => {
        expect(extractTotalHours(text)).toBe(expected);
      });
    });

    it("should extract hours from Polish text patterns", () => {
      const testCases = [
        { text: "Łączny szacowany czas: 6.5 godzin", expected: 6.5 },
        { text: "Czas trwania: 3 godzin", expected: 3 },
        { text: "Razem: 2.5 godzin", expected: 2.5 },
      ];

      testCases.forEach(({ text, expected }) => {
        expect(extractTotalHours(text)).toBe(expected);
      });
    });

    it("should handle decimal numbers", () => {
      const testCases = [
        { text: "Total: 2.5 hours", expected: 2.5 },
        { text: "Total: 3.25 Stunden", expected: 3.25 },
        { text: "Total: 4.75 timer", expected: 4.75 },
      ];

      testCases.forEach(({ text, expected }) => {
        expect(extractTotalHours(text)).toBe(expected);
      });
    });

    it("should return null for invalid or missing data", () => {
      const testCases = [
        "",
        "No time mentioned here",
        "Invalid JSON {",
        '{"noHours": "data"}',
        "Some text without numbers",
        null,
        undefined,
      ];

      testCases.forEach((text) => {
        expect(extractTotalHours(text)).toBeNull();
      });
    });

    it("should prioritize JSON format over text patterns", () => {
      const mixedResponse = '{"TOTAL_TIME_ESTIMATION": 5.0} Total: 3 hours';
      expect(extractTotalHours(mixedResponse)).toBe(5.0);
    });

    it("should return null for zero hours (implementation only returns positive hours)", () => {
      expect(extractTotalHours("Total: 0 hours")).toBeNull();
      expect(extractTotalHours("Total: 0.5 hours")).toBe(0.5);
      expect(extractTotalHours("Total: -1 hours")).toBe(1);
    });
  });

  describe("storeEstimationResult", () => {
    it("should store estimation result in localStorage", () => {
      const threadSlug = "test-thread";
      const messageId = "msg-123";
      const textResponse = "Test estimation";
      const totalHours = 2.5;

      storeEstimationResult(threadSlug, messageId, textResponse, totalHours);

      const expectedKey = `manual_work_estimation_${threadSlug}_${messageId}`;

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        expectedKey,
        expect.stringMatching(/^\{.*\}$/)
      );

      const storedCall = mockLocalStorage.setItem.mock.calls[0];
      const storedData = JSON.parse(storedCall[1]);

      expect(storedData).toEqual({
        textResponse,
        totalHours,
        timestamp: expect.any(Number),
        threadSlug,
        messageId,
      });
    });

    it("should handle special characters in thread slug and message ID", () => {
      const threadSlug = "test-thread-with-special-chars_123";
      const messageId = "msg-with-dashes-456";
      const textResponse = "Test estimation";
      const totalHours = 1.5;

      storeEstimationResult(threadSlug, messageId, textResponse, totalHours);

      const expectedKey = `manual_work_estimation_${threadSlug}_${messageId}`;
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        expectedKey,
        expect.any(String)
      );
    });

    it("should handle missing parameters gracefully", () => {
      const consoleSpy = jest.spyOn(console, "warn").mockImplementation();

      storeEstimationResult(null, "msg-123", "Test", 1.0);
      storeEstimationResult("thread", null, "Test", 1.0);

      expect(consoleSpy).toHaveBeenCalledWith(
        "Cannot store estimation result: missing threadSlug or messageId"
      );
      expect(mockLocalStorage.setItem).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe("getLatestEstimationForThread", () => {
    it("should return the latest estimation for a thread", () => {
      const threadSlug = "test-thread";
      const now = Date.now();

      // Mock localStorage to return multiple estimations
      const estimations = {
        [`manual_work_estimation_${threadSlug}_msg1`]: JSON.stringify({
          textResponse: "First estimation",
          totalHours: 1.0,
          timestamp: now - 3600000, // 1 hour ago
          threadSlug,
          messageId: "msg1",
        }),
        [`manual_work_estimation_${threadSlug}_msg2`]: JSON.stringify({
          textResponse: "Latest estimation",
          totalHours: 2.5,
          timestamp: now - 1800000, // 30 minutes ago
          threadSlug,
          messageId: "msg2",
        }),
        [`manual_work_estimation_${threadSlug}_msg3`]: JSON.stringify({
          textResponse: "Middle estimation",
          totalHours: 1.5,
          timestamp: now - 2700000, // 45 minutes ago
          threadSlug,
          messageId: "msg3",
        }),
        // Different thread - should be ignored
        [`manual_work_estimation_other-thread_msg4`]: JSON.stringify({
          textResponse: "Other thread estimation",
          totalHours: 3.0,
          timestamp: now,
          threadSlug: "other-thread",
          messageId: "msg4",
        }),
      };

      mockLocalStorage.getItem.mockImplementation(
        (key) => estimations[key] || null
      );

      // Mock localStorage.key and localStorage.length for iteration
      mockLocalStorage.length = Object.keys(estimations).length;
      mockLocalStorage.key.mockImplementation(
        (index) => Object.keys(estimations)[index]
      );

      const result = getLatestEstimationForThread(threadSlug);

      expect(result).toEqual({
        textResponse: "Latest estimation",
        totalHours: 2.5,
        timestamp: now - 1800000,
        threadSlug,
        messageId: "msg2",
      });
    });

    it("should return null if no estimations found for thread", () => {
      const threadSlug = "nonexistent-thread";

      // Mock empty localStorage
      mockLocalStorage.length = 0;
      mockLocalStorage.key.mockReturnValue(null);

      const result = getLatestEstimationForThread(threadSlug);
      expect(result).toBeNull();
    });

    it("should handle corrupted localStorage data gracefully", () => {
      const threadSlug = "test-thread";

      // Mock localStorage with corrupted data
      const estimationKeys = [
        `manual_work_estimation_${threadSlug}_msg1`,
        `manual_work_estimation_${threadSlug}_msg2`,
      ];

      const estimations = {
        [`manual_work_estimation_${threadSlug}_msg1`]: "invalid json {",
        [`manual_work_estimation_${threadSlug}_msg2`]: JSON.stringify({
          textResponse: "Valid estimation",
          totalHours: 2.0,
          timestamp: Date.now(),
          threadSlug,
          messageId: "msg2",
        }),
      };

      mockLocalStorage.getItem.mockImplementation((key) => {
        return estimations[key] || null;
      });
      mockLocalStorage.length = estimationKeys.length;
      mockLocalStorage.key.mockImplementation((index) => {
        return estimationKeys[index] || null;
      });

      // Mock console.error to avoid noise in test output
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      const result = getLatestEstimationForThread(threadSlug);

      // Should return the valid estimation, ignoring the corrupted one
      expect(result).toEqual({
        textResponse: "Valid estimation",
        totalHours: 2.0,
        timestamp: expect.any(Number),
        threadSlug,
        messageId: "msg2",
      });

      consoleErrorSpy.mockRestore();
    });

    it("should return null for missing threadSlug", () => {
      expect(getLatestEstimationForThread(null)).toBeNull();
      expect(getLatestEstimationForThread("")).toBeNull();
    });
  });

  describe("cleanupOldEstimations", () => {
    it("should remove estimations older than 24 hours", () => {
      const now = Date.now();
      const oneDayAgo = now - 24 * 60 * 60 * 1000;
      const twoDaysAgo = now - 48 * 60 * 60 * 1000;

      const estimations = {
        manual_work_estimation_thread1_msg1: JSON.stringify({
          textResponse: "Recent estimation",
          totalHours: 1.0,
          timestamp: now - 3600000, // 1 hour ago - should keep
        }),
        manual_work_estimation_thread1_msg2: JSON.stringify({
          textResponse: "Old estimation",
          totalHours: 2.0,
          timestamp: twoDaysAgo, // 2 days ago - should remove
        }),
        manual_work_estimation_thread2_msg3: JSON.stringify({
          textResponse: "Borderline estimation",
          totalHours: 1.5,
          timestamp: oneDayAgo - 1000, // Just over 24 hours - should remove
        }),
        other_key: "should not be touched",
      };

      mockLocalStorage.getItem.mockImplementation(
        (key) => estimations[key] || null
      );
      mockLocalStorage.length = Object.keys(estimations).length;
      mockLocalStorage.key.mockImplementation(
        (index) => Object.keys(estimations)[index]
      );

      cleanupOldEstimations();

      // Should remove the old estimations
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
        "manual_work_estimation_thread1_msg2"
      );
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
        "manual_work_estimation_thread2_msg3"
      );

      // Should not remove recent estimation or non-estimation keys
      expect(mockLocalStorage.removeItem).not.toHaveBeenCalledWith(
        "manual_work_estimation_thread1_msg1"
      );
      expect(mockLocalStorage.removeItem).not.toHaveBeenCalledWith("other_key");
    });

    it("should handle corrupted data during cleanup", () => {
      const estimations = {
        manual_work_estimation_thread1_msg1: "invalid json {",
        manual_work_estimation_thread1_msg2: JSON.stringify({
          textResponse: "Valid estimation",
          totalHours: 1.0,
          timestamp: Date.now() - 48 * 60 * 60 * 1000, // 2 days ago
        }),
      };

      mockLocalStorage.getItem.mockImplementation(
        (key) => estimations[key] || null
      );
      mockLocalStorage.length = Object.keys(estimations).length;
      mockLocalStorage.key.mockImplementation(
        (index) => Object.keys(estimations)[index]
      );

      // Should not throw error
      expect(() => cleanupOldEstimations()).not.toThrow();

      // Should remove the valid old estimation
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
        "manual_work_estimation_thread1_msg2"
      );

      // Should remove the corrupted data as well (since it can't be parsed)
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
        "manual_work_estimation_thread1_msg1"
      );
    });
  });

  describe("initializeManualWorkEstimation", () => {
    it("should set up periodic cleanup", () => {
      initializeManualWorkEstimation();

      expect(mockSetInterval).toHaveBeenCalledWith(
        expect.any(Function),
        60 * 60 * 1000 // 1 hour
      );
    });

    it("should run initial cleanup", () => {
      // Mock some old data
      const oldEstimation = {
        manual_work_estimation_thread1_msg1: JSON.stringify({
          textResponse: "Old estimation",
          totalHours: 1.0,
          timestamp: Date.now() - 48 * 60 * 60 * 1000, // 2 days ago
        }),
      };

      mockLocalStorage.getItem.mockImplementation(
        (key) => oldEstimation[key] || null
      );
      mockLocalStorage.length = Object.keys(oldEstimation).length;
      mockLocalStorage.key.mockImplementation(
        (index) => Object.keys(oldEstimation)[index]
      );

      initializeManualWorkEstimation();

      // Should run cleanup immediately
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
        "manual_work_estimation_thread1_msg1"
      );
    });

    it("should not return a cleanup function (implementation doesn't return one)", () => {
      const result = initializeManualWorkEstimation();
      expect(result).toBeUndefined();
    });
  });
});
