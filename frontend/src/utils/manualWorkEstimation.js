/**
 * Utility functions for manual work estimation
 */

/**
 * Extracts the total time estimation from the manual work estimation response
 * @param {string} textResponse - The text response from the manual work estimation
 * @returns {number|null} - The total hours as a number, or null if not found
 */
export function extractTotalHours(textResponse) {
  if (!textResponse || typeof textResponse !== "string") {
    return null;
  }

  try {
    // Look for JSON formatted output with TOTAL_TIME_ESTIMATION
    const jsonMatch = textResponse.match(
      /\{[^}]*"TOTAL_TIME_ESTIMATION"[^}]*\}/
    );
    if (jsonMatch) {
      const jsonStr = jsonMatch[0];
      const parsed = JSON.parse(jsonStr);
      if (
        parsed.TOTAL_TIME_ESTIMATION &&
        typeof parsed.TOTAL_TIME_ESTIMATION === "number"
      ) {
        return parsed.TOTAL_TIME_ESTIMATION;
      }
    }

    // Fallback: Look for patterns like "4.5 hours" or "2 hours"
    const hourPatterns = [
      /(\d+(?:\.\d+)?)\s*hours?/i,
      /(\d+(?:\.\d+)?)\s*timmar/i, // Swedish
      /(\d+(?:\.\d+)?)\s*heures?/i, // French
      /(\d+(?:\.\d+)?)\s*stunden?/i, // German
      /(\d+(?:\.\d+)?)\s*timer/i, // Norwegian
      /(\d+(?:\.\d+)?)\s*godzin/i, // Polish
      /(\d+(?:\.\d+)?)\s*amasaha/i, // Kinyarwanda
    ];

    for (const pattern of hourPatterns) {
      const match = textResponse.match(pattern);
      if (match && match[1]) {
        const hours = parseFloat(match[1]);
        if (!isNaN(hours) && hours > 0) {
          return hours;
        }
      }
    }

    return null;
  } catch (error) {
    console.error("Error extracting total hours from estimation:", error);
    return null;
  }
}

/**
 * Stores manual work estimation result in localStorage
 * @param {string} threadSlug - The thread slug
 * @param {string} messageId - The message ID
 * @param {string} textResponse - The full text response
 * @param {number} totalHours - The extracted total hours
 */
export function storeEstimationResult(
  threadSlug,
  messageId,
  textResponse,
  totalHours
) {
  if (!threadSlug || !messageId) {
    console.warn(
      "Cannot store estimation result: missing threadSlug or messageId"
    );
    return;
  }

  const key = `manual_work_estimation_${threadSlug}_${messageId}`;
  const data = {
    textResponse,
    totalHours,
    timestamp: Date.now(),
    threadSlug,
    messageId,
  };

  try {
    localStorage.setItem(key, JSON.stringify(data));
    console.log("Stored manual work estimation:", key, data);
  } catch (error) {
    console.error("Error storing estimation result:", error);
  }
}

/**
 * Retrieves the latest manual work estimation for a thread
 * @param {string} threadSlug - The thread slug
 * @returns {object|null} - The estimation data or null if not found
 */
export function getLatestEstimationForThread(threadSlug) {
  if (!threadSlug) {
    return null;
  }

  try {
    const prefix = `manual_work_estimation_${threadSlug}_`;
    const estimations = [];

    // Find all estimations for this thread
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        try {
          const data = JSON.parse(localStorage.getItem(key));
          if (data && data.timestamp) {
            estimations.push(data);
          }
        } catch (parseError) {
          // Skip corrupted data and continue with next item
          console.error(
            `Error parsing estimation data for key ${key}:`,
            parseError
          );
          continue;
        }
      }
    }

    // Return the most recent estimation
    if (estimations.length > 0) {
      estimations.sort((a, b) => b.timestamp - a.timestamp);
      return estimations[0];
    }

    return null;
  } catch (error) {
    console.error("Error retrieving estimation for thread:", error);
    return null;
  }
}

/**
 * Cleans up old estimation results (older than 24 hours)
 */
export function cleanupOldEstimations() {
  try {
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago
    const keysToRemove = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith("manual_work_estimation_")) {
        try {
          const data = JSON.parse(localStorage.getItem(key));
          if (data && data.timestamp && data.timestamp < cutoffTime) {
            keysToRemove.push(key);
          }
        } catch (error) {
          // If we can't parse the data, it's probably corrupted, so remove it
          keysToRemove.push(key);
        }
      }
    }

    keysToRemove.forEach((key) => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error(`Error removing estimation key ${key}:`, error);
      }
    });

    if (keysToRemove.length > 0) {
      console.log(`Cleaned up ${keysToRemove.length} old estimation results`);
    }
  } catch (error) {
    console.error("Error cleaning up old estimations:", error);
  }
}

/**
 * Initializes the manual work estimation system
 * Should be called when the app starts
 */
// Global reference to prevent multiple intervals
let cleanupInterval = null;

export function initializeManualWorkEstimation() {
  try {
    // Clear existing interval if any
    if (cleanupInterval) {
      clearInterval(cleanupInterval);
    }

    // Clean up old estimations on initialization
    cleanupOldEstimations();

    // Set up periodic cleanup (every hour)
    cleanupInterval = setInterval(cleanupOldEstimations, 60 * 60 * 1000);

    console.log("Manual work estimation system initialized");
  } catch (error) {
    console.error("Error initializing manual work estimation system:", error);
  }
}
