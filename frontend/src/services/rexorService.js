import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import { t } from "i18next";
import { getLatestEstimationForThread } from "@/utils/manualWorkEstimation";
import useSystemSettingsStore from "@/stores/settingsStore";

export const loginToRexor = async (username, password) => {
  try {
    const body = new URLSearchParams({
      username,
      password,
    }).toString();

    const response = await fetch(`${API_BASE}/system/rexor/auth`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.error || "Failed to login to Rexor");
    }

    const data = await response.json();
    if (!data.access_token) {
      throw new Error("No access token in response");
    }

    return {
      success: true,
      access_token: data.access_token,
    };
  } catch (error) {
    console.error("Error logging into Rexor:", error);
    throw error;
  }
};

export const registerProject = async (projectData, accessToken) => {
  try {
    const response = await fetch(`${API_BASE}/system/rexor/projects`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        access_token: accessToken,
        projectData: {
          ApiTable: "TProjectAll p",
          ApiSelect: `
            p.UID AS 'ProjectUID', p.ID AS 'ProjectID', p.Description AS 'ProjectDescription',
            r.*,
            pa.*,
            a.*,
            p.Status AS 'ProjectStatus'
          `
            .trim()
            .replace(/\s+/g, " "),
          ApiJoin: `
            CROSS JOIN (
                SELECT TOP 1 r.[UID] AS 'ResourceUID', r.[ID] AS 'ResourceID', r.[Name] AS 'ResourceName'
                FROM TResource r
                WHERE r.[ID] = '${projectData.ResourceID}'
            ) r
            CROSS APPLY (
                SELECT TOP 1 pa.[UID] AS 'ProjectActivityUID', pa.[ID] AS 'ProjectActivityID', pa.[Description] AS 'ProjectActivityDescription'
                FROM TProjectActivity pa
                INNER JOIN TProjectActivityMember pam ON pam.[ProjectUID] = p.[UID]
                WHERE pa.[ID] = dbo.GetValueFromPropertyBag('Api', 'Foynet', 'ProjectActivityID')
            ) pa
            CROSS APPLY (
                SELECT TOP 1 a.[UID] AS 'ArticleUID', a.[ID] AS 'ArticleID', a.[Description] AS 'ArticleDescription'
                FROM TArticle a
                WHERE a.[ID] = dbo.GetValueFromPropertyBag('Api', 'Foynet', 'ArticleID')
            ) a
          `
            .trim()
            .replace(/\s+/g, " "),
          ApiWhere: `ID = '${projectData.ProjectID}'`,
        },
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error("Error response:", error);

      // Create error with status code for better error handling
      const errorMessage = error.error || "Failed to register project";
      const apiError = new Error(errorMessage);
      apiError.status = response.status;

      throw apiError;
    }

    return await response.json();
  } catch (error) {
    console.error("Error registering project:", error.message);
    throw error;
  }
};

export const getInvoiceStatus = async (transactionUID, accessToken) => {
  try {
    const response = await fetch(
      `${API_BASE}/system/rexor/transaction-status/${transactionUID}`,
      {
        method: "GET",
        headers: {
          ...baseHeaders(),
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = errorData.error || "Failed to get invoice status";
      const apiError = new Error(errorMessage);
      apiError.status = response.status;
      throw apiError;
    }

    return await response.json();
  } catch (error) {
    console.error("Error getting invoice status:", error.message);
    throw error;
  }
};

export const writeArticleTransaction = async (transactionData, accessToken) => {
  try {
    const response = await fetch(
      `${API_BASE}/system/rexor/article-expense-transaction`,
      {
        method: "POST",
        headers: {
          ...baseHeaders(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          access_token: accessToken,
          transactionData: {
            ...transactionData,
            RegistrationDate: new Date().toISOString().split("T")[0],
            Origin: 0,
            Number: Number(transactionData.Number || 1),
            InvoicedNumber: Number(transactionData.InvoicedNumber || 1),
            Invoiceable: Number(transactionData.Invoiceable || 1),
            InvoiceStatus: "ReadyForInvoiceBasis",
            Status: 2,
          },
        }),
      }
    );

    const responseData = await response.json();

    if (!response.ok) {
      const errorMessage =
        responseData.error || "Failed to write article transaction";
      const apiError = new Error(errorMessage);
      apiError.status = response.status;
      throw apiError;
    }
    return responseData;
  } catch (error) {
    console.error("Error writing time transaction:", error.message);
    throw error;
  }
};
