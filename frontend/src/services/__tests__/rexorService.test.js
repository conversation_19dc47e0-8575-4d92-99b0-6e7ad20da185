import { writeArticleTransaction } from "../rexorService";
import i18n from "i18next";

// No need to mock this anymore as the logic was removed from the service
// jest.mock("@/utils/manualWorkEstimation");

jest.mock("i18next", () => ({
  t: (key) => key,
}));

describe("rexorService - writeArticleTransaction", () => {
  beforeEach(() => {
    // jest.clearAllMocks() is safer than resetting a specific mock
    jest.clearAllMocks();
  });

  it("should send the transaction data to the API endpoint", async () => {
    const transactionData = {
      InvoiceText: "Test transaction",
      Number: 1,
      InvoicedNumber: 1,
      Invoiceable: 1,
    };
    const accessToken = "test-access-token";
    // Mock the global fetch
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ UID: "12345" }),
      })
    );

    await writeArticleTransaction(transactionData, accessToken);

    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining("/system/rexor/article-expense-transaction"),
      expect.objectContaining({
        method: "POST",
        body: expect.stringContaining(
          `"InvoiceText":"${transactionData.InvoiceText}"`
        ),
      })
    );
  });

  it("should use default invoice text if none is provided", async () => {
    const transactionData = {
      Number: 1,
      InvoicedNumber: 1,
      Invoiceable: 1,
    };
    const accessToken = "test-access-token";
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ UID: "12345" }),
      })
    );

    await writeArticleTransaction(transactionData, accessToken);

    expect(fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        body: expect.stringContaining('"InvoiceText":"rexor.invoice-text"'),
      })
    );
  });

  it("should return the response data on success", async () => {
    const transactionData = { InvoiceText: "Successful transaction" };
    const accessToken = "test-access-token";
    const mockResponse = { UID: "67890", status: "success" };
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      })
    );

    const result = await writeArticleTransaction(transactionData, accessToken);

    expect(result).toEqual(mockResponse);
  });

  it("should throw an error on API failure", async () => {
    const transactionData = { InvoiceText: "Failing transaction" };
    const accessToken = "test-access-token";
    const mockError = { error: "API is down" };
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: false,
        json: () => Promise.resolve(mockError),
      })
    );

    await expect(
      writeArticleTransaction(transactionData, accessToken)
    ).rejects.toThrow("API is down");
  });
});
