import { writeArticleTransaction } from "../rexorService";
import { getLatestEstimationForThread } from "@/utils/manualWorkEstimation";

// Mock dependencies
jest.mock("@/utils/manualWorkEstimation");
jest.mock("i18next", () => ({
  t: (key, options) => {
    if (key === "rexor.estimated-manual-time" && options?.hours) {
      return ` * Estimated manual time: ${options.hours} hours`;
    }
    if (key === "rexor.invoice-text") {
      return "Foynet number of lookups";
    }
    return key;
  },
}));

// Mock fetch
global.fetch = jest.fn();

// Mock window.location
const mockLocation = {
  pathname: "/workspace/test-workspace/thread/test-thread",
};
Object.defineProperty(window, "location", {
  value: mockLocation,
  writable: true,
});

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
  writable: true,
});

describe("rexorService - writeArticleTransaction", () => {
  const mockGetLatestEstimationForThread = getLatestEstimationForThread;

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset the mock function properly
    mockGetLatestEstimationForThread.mockReset();

    // Mock localStorage for Rexor data
    mockLocalStorage.getItem.mockImplementation((key) => {
      switch (key) {
        case "rexor_registered_project":
          return JSON.stringify({
            projectId: "test-project-123",
            projectName: "Test Legal Project",
          });
        default:
          return null;
      }
    });

    // Mock successful fetch response
    fetch.mockResolvedValue({
      ok: true,
      json: () =>
        Promise.resolve({
          success: true,
          UID: "txn-123",
        }),
    });

    // Reset location to default
    mockLocation.pathname = "/workspace/test-workspace/thread/test-thread";
  });

  it("should include estimation in invoice text when estimation exists", async () => {
    // Mock estimation exists
    mockGetLatestEstimationForThread.mockReturnValue({
      estimationText: "Test estimation response",
      totalHours: 2.5,
      timestamp: Date.now(),
    });

    const transactionData = {
      InvoiceText: "Foynet number of lookups",
      Number: 1,
      InvoicedNumber: 1,
      Invoiceable: 1,
    };

    await writeArticleTransaction(transactionData, "test-access-token");

    expect(mockGetLatestEstimationForThread).toHaveBeenCalledWith(
      "test-thread"
    );

    // Check that fetch was called with modified text
    expect(fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        method: "POST",
        headers: expect.objectContaining({
          "Content-Type": "application/json",
        }),
        body: expect.stringContaining(
          `"InvoiceText":"${transactionData.InvoiceText} * Estimated manual time: 2.5 hours"`
        ),
      })
    );
  });

  it("should use original text when no estimation is found", async () => {
    // No estimation found
    mockGetLatestEstimationForThread.mockReturnValue(null);

    const transactionData = {
      InvoiceText: "Foynet number of lookups",
      Number: 1,
      InvoicedNumber: 1,
      Invoiceable: 1,
    };

    await writeArticleTransaction(transactionData, "test-access-token");

    expect(mockGetLatestEstimationForThread).toHaveBeenCalledWith(
      "test-thread"
    );

    // Check that fetch was called with original text
    expect(fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        body: expect.stringContaining(
          `"InvoiceText":"${transactionData.InvoiceText}"`
        ),
      })
    );
  });

  it("should handle estimation retrieval errors gracefully", async () => {
    // Mock estimation retrieval error
    mockGetLatestEstimationForThread.mockImplementation(() => {
      throw new Error("Estimation retrieval failed");
    });

    const transactionData = {
      InvoiceText: "Foynet number of lookups",
      Number: 1,
      InvoicedNumber: 1,
      Invoiceable: 1,
    };

    await writeArticleTransaction(transactionData, "test-access-token");

    expect(mockGetLatestEstimationForThread).toHaveBeenCalledWith(
      "test-thread"
    );

    // Should continue with original text despite error
    expect(fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        body: expect.stringContaining(
          `"InvoiceText":"${transactionData.InvoiceText}"`
        ),
      })
    );
  });

  it("should extract thread slug correctly from different URL patterns", async () => {
    const testCases = [
      {
        pathname: "/workspace/my-workspace/thread/my-thread-123",
        expectedThreadSlug: "my-thread-123",
      },
      {
        pathname: "/workspace/test-workspace/thread/thread-with-dashes",
        expectedThreadSlug: "thread-with-dashes",
      },
      {
        pathname:
          "/workspace/workspace_with_underscores/thread/thread_with_underscores",
        expectedThreadSlug: "thread_with_underscores",
      },
    ];

    for (const { pathname, expectedThreadSlug } of testCases) {
      // Reset only the specific mocks we need to reset
      mockGetLatestEstimationForThread.mockClear();
      mockGetLatestEstimationForThread.mockReturnValue({
        estimationText: "Test estimation",
        totalHours: 1.5,
        timestamp: Date.now(),
      });

      mockLocation.pathname = pathname;

      await writeArticleTransaction(
        { InvoiceText: "Test text" },
        "test-access-token"
      );

      expect(mockGetLatestEstimationForThread).toHaveBeenCalledWith(
        expectedThreadSlug
      );
    }
  });

  it("should handle URLs that don't match thread pattern", async () => {
    // URL without thread
    mockLocation.pathname = "/workspace/test-workspace";

    const transactionData = {
      InvoiceText: "Foynet number of lookups",
      Number: 1,
      InvoicedNumber: 1,
      Invoiceable: 1,
    };

    await writeArticleTransaction(transactionData, "test-access-token");

    // Should not try to get estimation
    expect(mockGetLatestEstimationForThread).not.toHaveBeenCalled();

    // Check that fetch was called with original text
    expect(fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        body: expect.stringContaining(
          `"InvoiceText":"${transactionData.InvoiceText}"`
        ),
      })
    );
  });

  it("should handle different hour formats in estimation", async () => {
    const testCases = [
      { hours: 1, expected: " * Estimated manual time: 1 hours" },
      { hours: 2.5, expected: " * Estimated manual time: 2.5 hours" },
      { hours: 0.25, expected: " * Estimated manual time: 0.25 hours" },
      { hours: 10.75, expected: " * Estimated manual time: 10.75 hours" },
    ];

    for (const { hours, expected } of testCases) {
      // Reset only fetch mock, not all mocks
      fetch.mockClear();
      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, UID: "txn-123" }),
      });

      mockGetLatestEstimationForThread.mockReturnValue({
        estimationText: "Test estimation",
        totalHours: hours,
        timestamp: Date.now(),
      });

      const transactionData = {
        InvoiceText: "Foynet number of lookups",
        Number: 1,
        InvoicedNumber: 1,
        Invoiceable: 1,
      };

      await writeArticleTransaction(transactionData, "test-access-token");

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: expect.stringContaining(
            `"InvoiceText":"${transactionData.InvoiceText}${expected}"`
          ),
        })
      );
    }
  });

  it("should handle special characters in original text and estimation", async () => {
    mockGetLatestEstimationForThread.mockReturnValue({
      estimationText: 'Test estimation with special chars: åäö & quotes "test"',
      totalHours: 2,
      timestamp: Date.now(),
    });

    const transactionData = {
      InvoiceText: 'Foynet with special chars: åäö & quotes "test"',
      Number: 1,
      InvoicedNumber: 1,
      Invoiceable: 1,
    };

    await writeArticleTransaction(transactionData, "test-access-token");

    // Should handle special characters properly in JSON
    // Note: quotes get escaped in JSON, so "test" becomes \"test\" in the JSON string
    expect(fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        method: "POST",
        body: expect.stringContaining(
          `"InvoiceText":"Foynet with special chars: åäö & quotes \\"test\\" * Estimated manual time: 2 hours"`
        ),
      })
    );
  });

  it("should maintain all other request parameters when adding estimation", async () => {
    mockGetLatestEstimationForThread.mockReturnValue({
      estimationText: "Test estimation",
      totalHours: 3,
      timestamp: Date.now(),
    });

    const transactionData = {
      InvoiceText: "Foynet number of lookups",
      Number: 1,
      InvoicedNumber: 1,
      Invoiceable: 1,
    };

    await writeArticleTransaction(transactionData, "test-access-token");

    // Verify all expected request parameters are present
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining("/api/system/rexor/article-expense-transaction"),
      expect.objectContaining({
        method: "POST",
        headers: expect.objectContaining({
          "Content-Type": "application/json",
        }),
        body: expect.stringContaining('"access_token":"test-access-token"'),
      })
    );

    // Verify the transaction data structure
    const fetchCall = fetch.mock.calls[0];
    const requestBody = JSON.parse(fetchCall[1].body);
    expect(requestBody.transactionData).toEqual(
      expect.objectContaining({
        InvoiceText: `${transactionData.InvoiceText} * Estimated manual time: 3 hours`,
        Number: 1,
        InvoicedNumber: 1,
        Invoiceable: 1,
        RegistrationDate: expect.any(String),
        Origin: 0,
        InvoiceStatus: "ReadyForInvoiceBasis",
        Status: 2,
      })
    );
  });
});
