import { create } from "zustand";
import { persist } from "zustand/middleware";

const ESTIMATION_LIFETIME = 24 * 60 * 60 * 1000; // 24 hours

export function extractTotalHours(textResponse) {
  if (!textResponse || typeof textResponse !== "string") {
    return null;
  }

  try {
    const jsonMatch = textResponse.match(
      /\{[^}]*"TOTAL_TIME_ESTIMATION"[^}]*\}/
    );
    if (jsonMatch) {
      const jsonStr = jsonMatch[0];
      const parsed = JSON.parse(jsonStr);
      if (
        parsed.TOTAL_TIME_ESTIMATION &&
        typeof parsed.TOTAL_TIME_ESTIMATION === "number"
      ) {
        return parsed.TOTAL_TIME_ESTIMATION;
      }
    }

    const hourPatterns = [
      /(\d+(?:\.\d+)?)\s*hours?/i,
      /(\d+(?:\.\d+)?)\s*timmar/i,
      /(\d+(?:\.\d+)?)\s*heures?/i,
      /(\d+(?:\.\d+)?)\s*stunden?/i,
      /(\d+(?:\.\d+)?)\s*timer/i,
      /(\d+(?:\.\d+)?)\s*godzin/i,
      /(\d+(?:\.\d+)?)\s*amasaha/i,
    ];

    for (const pattern of hourPatterns) {
      const match = textResponse.match(pattern);
      if (match && match[1]) {
        const hours = parseFloat(match[1]);
        if (!isNaN(hours) && hours > 0) {
          return hours;
        }
      }
    }

    return null;
  } catch (error) {
    console.error("Error extracting total hours from estimation:", error);
    return null;
  }
}

const useEstimationStore = create(
  persist(
    (set, get) => ({
      estimations: {}, // { [key]: { textResponse, totalHours, timestamp, ... } }

      storeEstimationResult: (
        threadSlug,
        messageId,
        textResponse,
        totalHours
      ) => {
        if (!threadSlug || !messageId) {
          console.warn(
            "Cannot store estimation result: missing threadSlug or messageId"
          );
          return;
        }
        const key = `manual_work_estimation_${threadSlug}_${messageId}`;
        const newEstimation = {
          textResponse,
          totalHours,
          timestamp: Date.now(),
          threadSlug,
          messageId,
        };
        set((state) => ({
          estimations: { ...state.estimations, [key]: newEstimation },
        }));
      },

      cleanupOldEstimations: () => {
        const now = Date.now();
        const currentEstimations = get().estimations;
        const freshEstimations = {};
        let cleanedCount = 0;

        Object.keys(currentEstimations).forEach((key) => {
          const estimation = currentEstimations[key];
          if (
            estimation &&
            estimation.timestamp &&
            now - estimation.timestamp < ESTIMATION_LIFETIME
          ) {
            freshEstimations[key] = estimation;
          } else {
            cleanedCount++;
          }
        });

        if (cleanedCount > 0) {
          set({ estimations: freshEstimations });
        }
      },
    }),
    {
      name: "manual-work-estimation-store",
    }
  )
);

export function getLatestEstimationForThread(threadSlug) {
  if (!threadSlug) {
    return null;
  }
  const { estimations } = useEstimationStore.getState();
  const threadEstimations = Object.values(estimations || {}).filter(
    (e) => e.threadSlug === threadSlug
  );

  if (threadEstimations.length > 0) {
    threadEstimations.sort((a, b) => b.timestamp - a.timestamp);
    return threadEstimations[0];
  }

  return null;
}

let cleanupInterval = null;
export function initializeManualWorkEstimation() {
  try {
    if (cleanupInterval) {
      clearInterval(cleanupInterval);
    }
    useEstimationStore.getState().cleanupOldEstimations();
    cleanupInterval = setInterval(
      () => useEstimationStore.getState().cleanupOldEstimations(),
      60 * 60 * 1000
    );
  } catch (error) {
    console.error(
      "Error initializing manual work estimation system with store:",
      error
    );
  }
}

export default useEstimationStore;

export const useStoreEstimationResult = () =>
  useEstimationStore((state) => state.storeEstimationResult);
