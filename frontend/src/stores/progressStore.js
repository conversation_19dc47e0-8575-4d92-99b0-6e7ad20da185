import { create } from "zustand";

const createThreadState = () => ({
  isActive: false,
  currentStep: 1,
  totalSteps: 7,
  startTime: null,
  flowType: null,
  currentSubStep: null,
  totalSubSteps: null,
  stepStatus: "pending", // "pending", "starting", "in_progress", "complete", "error"
  stepMessage: null,
  stepDetails: [],
  // Each thread state gets its own AbortController instance to ensure proper isolation.
  // This prevents shared references between threads and allows independent cancellation.
  abortController: new AbortController(),
  error: null,
  isCompleted: false, // New flag to track completion before cleanup
  completionTime: null, // When the process completed
});

const useProgressStore = create((set, get) => ({
  // Map of thread states: Map<threadSlug, ThreadState>
  threads: new Map(),

  startProcess: (threadSlug, totalSteps = 7, flowType = null) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      // If thread already exists, abort its current process
      if (state.threads.has(threadSlug)) {
        const existingState = state.threads.get(threadSlug);
        if (existingState?.abortController) {
          existingState.abortController.abort();
        }
      }

      // Create new thread state with fresh abort controller
      // This clears any previous error state and resets all thread state
      const newThreadState = {
        ...createThreadState(),
        isActive: true,
        totalSteps: Math.max(1, totalSteps),
        startTime: Date.now(),
        flowType,
        // Explicitly clear step details to prevent stale data
        stepDetails: [],
      };

      // Create new Map and set the thread
      const newThreads = new Map(state.threads);
      newThreads.set(threadSlug, newThreadState);

      return { threads: newThreads };
    });
  },

  updateProgress: (event, threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string" || !event) return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState || !threadState.isActive) return state;

      const { step, subStep, status, message, label } = event;

      if (typeof step !== "number") {
        // Ignore malformed event
        return state;
      }

      // Update step details array for tracking step history
      const updatedStepDetails = [...(threadState.stepDetails || [])];

      // Find or create step detail entry
      let stepDetailIndex = updatedStepDetails.findIndex(
        (detail) => detail.step === step
      );
      if (stepDetailIndex === -1) {
        updatedStepDetails.push({
          step,
          status: status || "pending",
          message: message || null,
          subTasks: [],
          expectedTotal: null,
          startTime: Date.now(),
        });
        stepDetailIndex = updatedStepDetails.length - 1;
      } else {
        // Update existing step
        updatedStepDetails[stepDetailIndex] = {
          ...updatedStepDetails[stepDetailIndex],
          status: status || updatedStepDetails[stepDetailIndex].status,
          message: message || updatedStepDetails[stepDetailIndex].message,
        };
      }

      // Store expected total for this step if provided
      if (event.total && typeof event.total === "number") {
        const stepDetail = updatedStepDetails[stepDetailIndex];
        stepDetail.expectedTotal = event.total;
      }

      // Handle sub-tasks
      if (subStep !== undefined && subStep !== null) {
        const stepDetail = updatedStepDetails[stepDetailIndex];

        let subTaskIndex = stepDetail.subTasks.findIndex(
          (subTask) => subTask.subStep === subStep
        );

        if (subTaskIndex === -1) {
          stepDetail.subTasks.push({
            subStep,
            status: status || "in_progress", // Default to in_progress when first created
            message: message || null,
            label: label || null,
            startTime: Date.now(),
          });
          // Sort sub-tasks by subStep to maintain order after adding new ones
          stepDetail.subTasks.sort((a, b) => a.subStep - b.subStep);
        } else {
          // Determine status based on progress and existing status
          let newStatus = status;

          // If no explicit status provided, infer from progress or other indicators
          if (!newStatus) {
            const currentStatus = stepDetail.subTasks[subTaskIndex].status;
            const currentSubTask = stepDetail.subTasks[subTaskIndex];

            // If progress is 100, mark as complete
            if (event.progress === 100) {
              newStatus = "complete";
            } else if (event.progress === -2 || event.error) {
              newStatus = "error";
            } else if (currentStatus === "pending") {
              newStatus = "in_progress"; // Start processing if was pending
            } else if (
              currentStatus === "in_progress" &&
              currentSubTask.message &&
              currentSubTask.label &&
              event.label &&
              !event.message &&
              !event.progress
            ) {
              // This looks like a completion event: sub-task is in_progress,
              // has existing message/label, and new event only has label (no message/progress)
              newStatus = "complete";
            } else {
              newStatus = currentStatus; // Keep existing status
            }
          }

          stepDetail.subTasks[subTaskIndex] = {
            ...stepDetail.subTasks[subTaskIndex],
            status: newStatus,
            message: message || stepDetail.subTasks[subTaskIndex].message,
            label: label || stepDetail.subTasks[subTaskIndex].label,
            startTime:
              stepDetail.subTasks[subTaskIndex].startTime || Date.now(),
          };
        }
      }

      newThreads.set(threadSlug, {
        ...threadState,
        currentStep: step || threadState.currentStep,
        currentSubStep: subStep,
        stepStatus: status || threadState.stepStatus,
        stepMessage: message || threadState.stepMessage,
        stepDetails: updatedStepDetails,
        lastUpdate: Date.now(),
      });

      return { threads: newThreads };
    });
  },

  finishProcess: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState) return state;

      // Mark as completed and inactive instead of deleting immediately
      newThreads.set(threadSlug, {
        ...threadState,
        isActive: false,
        isCompleted: true,
        completionTime: Date.now(),
        stepStatus: "complete",
        currentStep: threadState.totalSteps, // Ensure we're on the last step
      });

      return { threads: newThreads };
    });

    // Schedule cleanup after a delay to give user time to see completion
    setTimeout(() => {
      set((state) => {
        const newThreads = new Map(state.threads);
        const threadState = newThreads.get(threadSlug);

        // Only delete if still marked as completed (not restarted)
        if (threadState?.isCompleted) {
          newThreads.delete(threadSlug);
          return { threads: newThreads };
        }
        return state;
      });
    }, 5000); // 5 second delay before cleanup
  },

  cancelProcess: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState) return state;

      if (threadState.abortController) {
        threadState.abortController.abort();
      }

      newThreads.set(threadSlug, {
        ...threadState,
        isActive: false,
        stepStatus: "error",
      });

      return { threads: newThreads };
    });
  },

  getAbortController: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return null;

    const state = get();
    const threadState = state.threads.get(threadSlug);
    return threadState?.abortController || null;
  },

  getThreadState: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return null;

    const state = get();
    return state.threads.get(threadSlug) || null;
  },

  setError: (threadSlug, errorMessage) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState) return state;

      newThreads.set(threadSlug, {
        ...threadState,
        isActive: false,
        stepStatus: "error",
        error: errorMessage,
      });

      return { threads: newThreads };
    });
  },

  clearError: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      if (!newThreads.has(threadSlug)) return state;

      // Remove the thread state completely when clearing error
      newThreads.delete(threadSlug);
      return { threads: newThreads };
    });
  },

  // Auto-cleanup method to remove stale progress states
  cleanupStaleProcesses: (maxAgeMinutes = 10) => {
    const now = Date.now();
    const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds

    set((state) => {
      const newThreads = new Map(state.threads);
      let hasChanges = false;

      for (const [threadSlug, threadState] of newThreads.entries()) {
        // Use lastUpdate if available, otherwise fall back to startTime
        // This ensures active processes with recent updates don't get cleaned up
        const lastActivityTime =
          threadState.lastUpdate || threadState.startTime || now;
        const age = now - lastActivityTime;

        if (age > maxAge) {
          console.log(
            `[ProgressStore] Cleaning up stale progress for thread: ${threadSlug} (age: ${Math.round(age / 1000)}s)`
          );
          newThreads.delete(threadSlug);
          hasChanges = true;
        }
      }

      return hasChanges ? { threads: newThreads } : state;
    });
  },

  // Force cleanup a specific thread (useful for debugging or manual cleanup)
  forceCleanup: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      if (newThreads.has(threadSlug)) {
        // Abort any ongoing process before cleanup
        const threadState = newThreads.get(threadSlug);
        if (threadState?.abortController) {
          threadState.abortController.abort();
        }
        newThreads.delete(threadSlug);
        return { threads: newThreads };
      }
      return state;
    });
  },

  // Clear all stale progress data for a thread before starting a new process
  clearStaleProgress: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (threadState) {
        // Abort any ongoing process
        if (threadState.abortController) {
          threadState.abortController.abort();
        }

        // Reset to clean state but keep the thread entry
        newThreads.set(threadSlug, {
          ...createThreadState(),
          // Keep the abort controller for potential reuse
          abortController: new AbortController(),
        });

        return { threads: newThreads };
      }

      return state;
    });
  },
}));

// Auto-cleanup stale processes every 5 minutes
setInterval(
  () => {
    useProgressStore.getState().cleanupStaleProcesses();
  },
  5 * 60 * 1000
);

export default useProgressStore;
