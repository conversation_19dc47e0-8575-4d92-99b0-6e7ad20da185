import React from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";

export default function OverrideNotice({
  isActive,
  functionName,
  settingsPath,
}) {
  const { t } = useTranslation();

  if (!isActive) {
    return null;
  }

  return (
    <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <ExclamationTriangleIcon
            className="h-5 w-5 text-amber-400"
            aria-hidden="true"
          />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-amber-800">
            {t("override-notice.title")}
          </h3>
          <div className="mt-2 text-sm text-amber-700">
            <p>{t("override-notice.message", { functionName })}</p>
          </div>
          <div className="mt-3">
            <Link
              to={settingsPath}
              className="text-sm font-medium text-amber-600 hover:text-amber-500"
            >
              {t("override-notice.manage-link")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
