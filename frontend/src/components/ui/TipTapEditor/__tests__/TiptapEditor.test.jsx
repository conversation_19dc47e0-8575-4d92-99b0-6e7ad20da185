import React from "react";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import "@testing-library/jest-dom";
import TipTapEditor from "../index";

// Mock the tiptap dependencies
jest.mock("@tiptap/react", () => ({
  useEditor: jest.fn(() => ({
    commands: {
      focus: jest.fn(),
      blur: jest.fn(),
      setContent: jest.fn(),
      clearContent: jest.fn(),
    },
    chain: jest.fn(() => ({
      focus: jest.fn(() => ({
        toggleBold: jest.fn(() => ({ run: jest.fn() })),
        toggleItalic: jest.fn(() => ({ run: jest.fn() })),
        toggleStrike: jest.fn(() => ({ run: jest.fn() })),
        toggleCode: jest.fn(() => ({ run: jest.fn() })),
        toggleHeading: jest.fn(() => ({ run: jest.fn() })),
        toggleBulletList: jest.fn(() => ({ run: jest.fn() })),
        toggleOrderedList: jest.fn(() => ({ run: jest.fn() })),
        toggleBlockquote: jest.fn(() => ({ run: jest.fn() })),
        setHorizontalRule: jest.fn(() => ({ run: jest.fn() })),
        undo: jest.fn(() => ({ run: jest.fn() })),
        redo: jest.fn(() => ({ run: jest.fn() })),
      })),
    })),
    isActive: jest.fn(() => false),
    can: jest.fn(() => ({
      undo: jest.fn(() => true),
      redo: jest.fn(() => true),
    })),
    setEditable: jest.fn(),
    storage: {
      markdown: {
        getMarkdown: jest.fn(() => "test markdown"),
      },
    },
    view: {
      dom: {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      },
    },
    destroy: jest.fn(),
  })),
  EditorContent: ({ editor, className }) => (
    <div data-testid="editor-content" className={className}>
      Editor Content
    </div>
  ),
}));

jest.mock("@tiptap/starter-kit", () => ({
  configure: jest.fn(() => ({})),
}));

jest.mock("tiptap-markdown", () => ({
  Markdown: {
    configure: jest.fn(() => ({})),
  },
}));

jest.mock("@tiptap/extension-placeholder", () => ({
  configure: jest.fn(() => ({})),
}));

jest.mock("@tiptap/extension-focus", () => ({
  configure: jest.fn(() => ({})),
}));

jest.mock("@tiptap/extension-typography", () => ({}));

jest.mock("@tiptap/extension-history", () => ({
  configure: jest.fn(() => ({})),
}));

// Mock react-icons
jest.mock("react-icons/fa", () => ({
  FaBold: () => <div data-testid="icon-bold">Bold</div>,
  FaItalic: () => <div data-testid="icon-italic">Italic</div>,
  FaStrikethrough: () => <div data-testid="icon-strike">Strike</div>,
  FaCode: () => <div data-testid="icon-code">Code</div>,
  FaListUl: () => <div data-testid="icon-bullet">Bullet</div>,
  FaListOl: () => <div data-testid="icon-ordered">Ordered</div>,
  FaQuoteLeft: () => <div data-testid="icon-quote">Quote</div>,
  FaUndo: () => <div data-testid="icon-undo">Undo</div>,
  FaRedo: () => <div data-testid="icon-redo">Redo</div>,
}));

jest.mock("react-icons/hi", () => ({
  HiOutlineMinus: () => <div data-testid="icon-hr">HR</div>,
}));

describe("TipTapEditor", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders without crashing", () => {
    render(<TipTapEditor content="Test content" />);
    expect(screen.getByTestId("editor-content")).toBeInTheDocument();
  });

  test("renders toolbar by default", () => {
    render(<TipTapEditor content="Test content" />);

    // Check for toolbar buttons
    expect(screen.getByTestId("icon-bold")).toBeInTheDocument();
    expect(screen.getByTestId("icon-italic")).toBeInTheDocument();
    expect(screen.getByTestId("icon-strike")).toBeInTheDocument();
  });

  test("hides toolbar when showToolbar is false", () => {
    render(<TipTapEditor content="Test content" showToolbar={false} />);

    // Toolbar buttons should not be present
    expect(screen.queryByTestId("icon-bold")).not.toBeInTheDocument();
    expect(screen.queryByTestId("icon-italic")).not.toBeInTheDocument();
  });

  test("calls onChange when content changes", async () => {
    const mockOnChange = jest.fn();
    let capturedConfig = null;

    // Create a mock editor object
    const mockEditor = {
      commands: {
        focus: jest.fn(),
        blur: jest.fn(),
        setContent: jest.fn(),
        clearContent: jest.fn(),
      },
      chain: jest.fn(() => ({
        focus: jest.fn(() => ({
          toggleBold: jest.fn(() => ({ run: jest.fn() })),
          toggleItalic: jest.fn(() => ({ run: jest.fn() })),
        })),
      })),
      isActive: jest.fn(() => false),
      can: jest.fn(() => ({
        undo: jest.fn(() => true),
        redo: jest.fn(() => true),
      })),
      setEditable: jest.fn(),
      storage: {
        markdown: {
          getMarkdown: jest.fn(() => "updated content"),
        },
      },
      view: {
        dom: {
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
        },
      },
      destroy: jest.fn(),
    };

    const { useEditor } = require("@tiptap/react");

    // Mock useEditor to capture the config and return the mock editor
    useEditor.mockImplementation((config) => {
      capturedConfig = config;
      return mockEditor;
    });

    render(<TipTapEditor content="Test content" onChange={mockOnChange} />);

    // Wait a bit to ensure the component is fully initialized
    await waitFor(() => {
      expect(capturedConfig).not.toBeNull();
    });

    // Simulate editor content change by calling the onUpdate callback
    act(() => {
      if (capturedConfig && capturedConfig.onUpdate) {
        capturedConfig.onUpdate({ editor: mockEditor });
      }
    });

    // Verify onChange was called with the expected content
    expect(mockOnChange).toHaveBeenCalledWith("updated content");
  });

  test("applies disabled state correctly", () => {
    render(<TipTapEditor content="Test content" disabled={true} />);

    const container = screen.getByTestId("editor-content").parentElement;
    expect(container).toHaveClass("cursor-not-allowed", "opacity-50");
  });

  test("applies custom className", () => {
    render(<TipTapEditor content="Test content" className="custom-class" />);

    const container = screen.getByTestId("editor-content").parentElement;
    expect(container).toHaveClass("custom-class");
  });

  test("toolbar buttons have correct titles", () => {
    render(<TipTapEditor content="Test content" />);

    const boldButton = screen.getByTestId("icon-bold").closest("button");
    const italicButton = screen.getByTestId("icon-italic").closest("button");

    expect(boldButton).toHaveAttribute("title", "Bold (Ctrl+B)");
    expect(italicButton).toHaveAttribute("title", "Italic (Ctrl+I)");
  });
});
