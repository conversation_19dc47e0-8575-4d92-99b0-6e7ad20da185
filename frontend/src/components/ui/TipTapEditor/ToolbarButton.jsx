import React from "react";
import { cn } from "@/utils/classes";

export default function ToolbarButton({
  onClick,
  isActive = false,
  disabled = false,
  children,
  title,
  className = "",
}) {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={cn(
        "flex items-center justify-center w-8 h-8 rounded border border-border hover:bg-elevated transition-colors",
        isActive && "bg-primary text-background border-primary",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
    >
      {children}
    </button>
  );
}
