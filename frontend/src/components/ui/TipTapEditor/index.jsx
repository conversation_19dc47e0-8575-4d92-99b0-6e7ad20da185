import React, {
  useEffect,
  useImperativeHandle,
  forwardRef,
  useCallback,
  useRef,
  useMemo,
} from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Markdown } from "tiptap-markdown";
import Placeholder from "@tiptap/extension-placeholder";
import Focus from "@tiptap/extension-focus";
import Typography from "@tiptap/extension-typography";
import History from "@tiptap/extension-history";
import { cn } from "@/utils/classes";
import Toolbar from "./Toolbar";

const TipTapEditor = forwardRef(
  (
    {
      content = "",
      placeholder = "",
      onChange,
      onUpdate,
      className = "",
      disabled = false,
      autoFocus = false,
      onKeyDown,
      showToolbar = true,
      toolbarClassName = "",
      ...props
    },
    ref
  ) => {
    // Use ref to track the last content to prevent infinite re-renders
    const lastContentRef = useRef(content);
    const isUpdatingRef = useRef(false);
    const lastNormalizedContentRef = useRef("");

    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          // Enable history for undo/redo functionality
          history: false, // We'll use our own History extension
        }),
        History.configure({
          depth: 10,
          newGroupDelay: 500,
        }),
        Markdown.configure({
          html: false, // Disable HTML parsing to prevent XSS vulnerabilities
          tightLists: true,
          bulletListMarker: "-",
          linkify: false,
          breaks: false,
          transformPastedText: true,
          transformCopiedText: false,
        }),
        Placeholder.configure({
          placeholder: placeholder || "",
        }),
        Focus.configure({
          className: "has-focus",
        }),
        Typography,
      ],
      content: content,
      editable: !disabled,
      onUpdate: ({ editor }) => {
        // Prevent triggering onChange during programmatic content updates
        if (isUpdatingRef.current) {
          return;
        }

        const markdown = editor.storage.markdown.getMarkdown();
        lastContentRef.current = markdown;

        if (onChange) {
          onChange(markdown);
        }
        if (onUpdate) {
          onUpdate({ editor, markdown });
        }
      },
      onCreate: ({ editor }) => {
        // Initialize the last content ref with the initial content
        lastContentRef.current = content;

        if (autoFocus) {
          editor.commands.focus();
        }
      },
    });

    // Enhanced normalize content function for more stable comparison
    const normalizeContent = useCallback((text) => {
      if (typeof text !== "string") return "";

      // Helper function to normalize multiple spaces while preserving code blocks
      const normalizeSpaces = (str) => {
        let result = "";
        let inFencedCodeBlock = false;
        let inInlineCode = false;
        let i = 0;

        while (i < str.length) {
          const char = str[i];

          if (char === "`") {
            // Check for triple backtick fenced code blocks
            if (
              i + 2 < str.length &&
              str[i + 1] === "`" &&
              str[i + 2] === "`"
            ) {
              // Found triple backticks - toggle fenced code block state
              inFencedCodeBlock = !inFencedCodeBlock;
              result += "```";
              i += 2; // Skip the next two backticks since we've processed all three
            } else if (!inFencedCodeBlock) {
              // Single backtick outside fenced code blocks - toggle inline code state
              inInlineCode = !inInlineCode;
              result += char;
            } else {
              // Single backtick inside fenced code block - preserve as-is
              result += char;
            }
          } else if (!inFencedCodeBlock && !inInlineCode && char === " ") {
            // Outside any code blocks, normalize multiple spaces to single space
            result += char;
            // Skip additional consecutive spaces
            while (i + 1 < str.length && str[i + 1] === " ") {
              i++;
            }
          } else {
            // Inside code blocks or non-space characters, preserve as-is
            result += char;
          }

          i++;
        }

        return result;
      };

      return (
        text
          .trim()
          // Normalize line endings
          .replace(/\r\n/g, "\n")
          .replace(/\r/g, "\n")
          // Normalize multiple consecutive newlines to single newlines
          .replace(/\n{3,}/g, "\n\n")
          // Remove trailing spaces from each line
          .replace(/[ \t]+$/gm, "")
          // Normalize multiple spaces to single spaces (except in code blocks)
          .split("\n")
          .map((line) => normalizeSpaces(line))
          .join("\n")
          // Ensure consistent spacing around markdown elements
          .replace(/([#*_`])\s+/g, "$1 ")
          .replace(/\s+([#*_`])/g, " $1")
          // Remove leading/trailing whitespace
          .trim()
      );
    }, []);

    // Memoize the normalized content to prevent unnecessary recalculations
    const normalizedContent = useMemo(() => {
      return normalizeContent(content);
    }, [content, normalizeContent]);

    // Update content when prop changes
    useEffect(() => {
      if (!editor || content === undefined || content === null) {
        return;
      }

      // Use the memoized normalized content
      const normalizedNewContent = normalizedContent;
      const normalizedLastContent = lastNormalizedContentRef.current;

      // Only update if the normalized content is actually different
      if (normalizedNewContent !== normalizedLastContent) {
        isUpdatingRef.current = true;

        try {
          // Store current cursor position before updating content (if editor state is available)
          let currentPos = 0;
          if (editor.state && editor.state.selection) {
            const currentSelection = editor.state.selection;
            currentPos = currentSelection.anchor;
          }

          editor.commands.setContent(content);

          // Restore cursor position after content update (if editor state is available)
          if (editor.state && editor.state.doc && currentPos > 0) {
            // Ensure the position is within the new content bounds
            const newDocSize = editor.state.doc.content.size;
            const safePos = Math.min(currentPos, newDocSize);

            // Only restore position if the editor is focused and position is valid
            if (editor.isFocused && safePos > 0 && safePos <= newDocSize) {
              editor.commands.setTextSelection(safePos);
            }
          }

          lastContentRef.current = content;
          lastNormalizedContentRef.current = normalizedNewContent;
        } finally {
          // Use setTimeout to ensure the update flag is reset after the editor processes the change
          setTimeout(() => {
            isUpdatingRef.current = false;
          }, 0);
        }
      }
    }, [normalizedContent, editor]);

    // Update editable state when disabled prop changes
    useEffect(() => {
      if (editor) {
        editor.setEditable(!disabled);
      }
    }, [disabled, editor]);

    // Memoize the keydown handler to prevent memory leaks
    const handleKeyDown = useCallback(
      (event) => {
        if (onKeyDown) {
          onKeyDown(event);
        }
        return false; // Let other handlers process the event
      },
      [onKeyDown]
    );

    // Handle keyboard events
    useEffect(() => {
      if (editor && onKeyDown) {
        editor.view.dom.addEventListener("keydown", handleKeyDown);

        return () => {
          if (editor.view?.dom) {
            editor.view.dom.removeEventListener("keydown", handleKeyDown);
          }
        };
      }
    }, [editor, onKeyDown, handleKeyDown]);

    // Expose editor methods via ref
    useImperativeHandle(ref, () => ({
      focus: () => editor?.commands.focus(),
      blur: () => editor?.commands.blur(),
      getMarkdown: () => editor?.storage.markdown.getMarkdown(),
      setContent: (newContent) => {
        if (editor) {
          isUpdatingRef.current = true;
          try {
            // Store current cursor position before updating content (if editor state is available)
            let currentPos = 0;
            if (editor.state && editor.state.selection) {
              const currentSelection = editor.state.selection;
              currentPos = currentSelection.anchor;
            }

            editor.commands.setContent(newContent);

            // Restore cursor position after content update (if editor state is available)
            if (editor.state && editor.state.doc && currentPos > 0) {
              // Ensure the position is within the new content bounds
              const newDocSize = editor.state.doc.content.size;
              const safePos = Math.min(currentPos, newDocSize);

              // Only restore position if the editor is focused and position is valid
              if (editor.isFocused && safePos > 0 && safePos <= newDocSize) {
                editor.commands.setTextSelection(safePos);
              }
            }

            lastContentRef.current = newContent;
            lastNormalizedContentRef.current = normalizeContent(newContent);
          } finally {
            setTimeout(() => {
              isUpdatingRef.current = false;
            }, 0);
          }
        }
      },
      clear: () => {
        if (editor) {
          isUpdatingRef.current = true;
          try {
            editor.commands.clearContent();
            lastContentRef.current = "";
            lastNormalizedContentRef.current = "";
          } finally {
            setTimeout(() => {
              isUpdatingRef.current = false;
            }, 0);
          }
        }
      },
      destroy: () => editor?.destroy(),
      editor,
    }));

    if (!editor) {
      return null;
    }

    return (
      <div
        className={cn(
          "relative flex flex-col w-full rounded-md bg-elevated text-sm text-foreground",
          disabled && "cursor-not-allowed opacity-50",
          className
        )}
        {...props}
      >
        {showToolbar && (
          <div className="sticky top-0 z-10">
            <Toolbar editor={editor} className={toolbarClassName} />
          </div>
        )}
        <EditorContent
          editor={editor}
          className="flex-1 px-3 py-2 border-x border-b [&_.ProseMirror]:min-h-[60px] [&_.ProseMirror]:outline-none [&_.ProseMirror]:focus:outline-none [&_.ProseMirror-focused]:outline-none [&_.ProseMirror_h1]:text-2xl [&_.ProseMirror_h1]:font-bold [&_.ProseMirror_h1]:mb-4 [&_.ProseMirror_h2]:text-xl [&_.ProseMirror_h2]:font-bold [&_.ProseMirror_h2]:mb-3 [&_.ProseMirror_h3]:text-lg [&_.ProseMirror_h3]:font-bold [&_.ProseMirror_h3]:mb-2 [&_.ProseMirror_p]:mb-2 [&_.ProseMirror_ul]:list-disc [&_.ProseMirror_ul]:ml-6 [&_.ProseMirror_ol]:list-decimal [&_.ProseMirror_ol]:ml-6 [&_.ProseMirror_li]:mb-1 [&_.ProseMirror_strong]:font-bold [&_.ProseMirror_em]:italic [&_.ProseMirror_code]:bg-muted [&_.ProseMirror_code]:px-1 [&_.ProseMirror_code]:rounded [&_.ProseMirror_blockquote]:border-l-4 [&_.ProseMirror_blockquote]:border [&_.ProseMirror_blockquote]:pl-4 [&_.ProseMirror_blockquote]:italic"
        />
      </div>
    );
  }
);

TipTapEditor.displayName = "TipTapEditor";

export default TipTapEditor;
