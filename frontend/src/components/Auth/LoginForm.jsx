import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import System from "@/models/system";

export default function LoginForm({
  onSubmit,
  loading,
  setLoading,
  error,
  showError = true,
  onForgotPassword,
  isRwandaStyle = false,
}) {
  const { t } = useTranslation();
  const [customLoginText, setCustomLoginText] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    onSubmit(formData);
  };

  useEffect(() => {
    const fetchCustomLoginText = async () => {
      const { paragraphText } = await System.fetchCustomParagraph();
      setCustomLoginText(paragraphText || t("login.password.contact"));
      if (setLoading) {
        setLoading(false);
      }
    };
    fetchCustomLoginText();
  }, [t, setLoading]);

  const formGroupClass = isRwandaStyle ? "form-group-rw" : "form-group";
  const inputClass = `dark-input-mdl w-full text-foreground tracking-wide ${isRwandaStyle ? "normal-text" : "text-white opacity-70 focus:opacity-100"} rounded-md p-3 h-[35px]`;
  const forgotPasswordClass = isRwandaStyle
    ? "forgot-password pt-4 pb-5"
    : "text-white pt-2 pb-5";

  return (
    <form
      className=" w-full flex flex-col items-center justify-center mt-3"
      onSubmit={handleSubmit}
      aria-label={t("login.form-title", { defaultValue: "Login form" })}
    >
      <div className={`${formGroupClass} w-[80vw] md:w-full`}>
        <label htmlFor="username-input" className="sr-only">
          {t("login.multi-user.placeholder-username")}
        </label>
        <input
          id="username-input"
          className={inputClass}
          name="username"
          type="text"
          placeholder={t("login.multi-user.placeholder-username")}
          required={true}
          autoComplete="username"
          aria-required="true"
          aria-invalid={error ? "true" : "false"}
          aria-describedby={error ? "login-error" : undefined}
        />
      </div>
      <div className={`${formGroupClass} w-[80vw] md:w-full mt-4`}>
        <label htmlFor="password-input" className="sr-only">
          {t("login.multi-user.placeholder-password")}
        </label>
        <input
          id="password-input"
          className={inputClass}
          name="password"
          type="password"
          placeholder={t("login.multi-user.placeholder-password")}
          required={true}
          autoComplete="current-password"
          aria-required="true"
          aria-invalid={error ? "true" : "false"}
          aria-describedby={error ? "login-error" : undefined}
        />
      </div>

      {showError && error && (
        <div
          id="login-error"
          className="text-[11px] error-alert-box simple-alert-box-error mt-4 box error-appear flex flex-row justify-between items-center w-full"
          role="alert"
        >
          <div>{error}</div>
        </div>
      )}

      <div className="w-full flex flex-col items-center justify-center mt-4">
        <Button
          type="submit"
          className={`hover:opacity-80 px-6 py-3 rounded-3xl font-semibold w-[80%] ${
            isRwandaStyle ? "submit-btn-rw" : "bg-white text-foreground"
          }`}
          disabled={loading}
          aria-label={loading ? t("login.logging") : t("login.button")}
        >
          {loading ? t("login.logging") : t("login.button")}
        </Button>
      </div>
    </form>
  );
}
