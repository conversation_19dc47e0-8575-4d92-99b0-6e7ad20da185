import React, { useState, useRef, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { TbPencilMinus } from "react-icons/tb";
import { Tooltip } from "react-tooltip";
import Modal from "@/components/ui/Modal";
import TipTapEditor from "@/components/ui/TipTapEditor";
import { Button } from "@/components/Button";
import showToast from "@/utils/toast";
import Workspace from "@/models/workspace";
import { useParams } from "react-router-dom";

export default function TextEditingTool({
  message,
  chatId,
  role,
  slug,
  updateHistory,
}) {
  const { t } = useTranslation();
  const { slug: urlSlug, threadSlug } = useParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editedContent, setEditedContent] = useState("");
  const [editedMarkdown, setEditedMarkdown] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const editorRef = useRef(null);

  // Use slug from props, fallback to URL slug if props slug is null
  const workspaceSlug = slug || urlSlug;

  const handleOpenEditor = useCallback(() => {
    setEditedContent(message || "");
    setEditedMarkdown(message || "");
    setIsModalOpen(true);
  }, [message]);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setEditedContent("");
    setEditedMarkdown("");
  }, []);

  const handleContentChange = useCallback((markdown) => {
    // TipTapEditor only passes markdown content as a single parameter
    setEditedMarkdown(markdown || "");
    // For consistency, also update editedContent with the same markdown
    setEditedContent(markdown || "");
  }, []);

  const handleSave = useCallback(async () => {
    if (!chatId || !(editedMarkdown || "").trim()) {
      showToast(t("text-editing.error-empty-content"), "error");
      return;
    }

    if (!workspaceSlug) {
      console.error("TextEditingTool - No workspace slug available!");
      showToast("No workspace slug available", "error");
      return;
    }

    setIsLoading(true);
    try {
      // Update the message content using the existing updateChatResponse method
      // Pass threadSlug (could be undefined for main workspace chat)
      const response = await Workspace.updateChatResponse(
        workspaceSlug,
        threadSlug || "", // threadSlug - empty string for main workspace chat
        chatId,
        (editedMarkdown || "").trim()
      );

      if (response) {
        showToast(t("text-editing.save-success"), "success");

        // Update the chat history if updateHistory function is provided
        if (updateHistory) {
          updateHistory((prevHistory) =>
            prevHistory.map((item) =>
              item.chatId === chatId
                ? {
                    ...item,
                    content: (editedMarkdown || "").trim(),
                    message: (editedMarkdown || "").trim(),
                    edited: true, // Mark as edited to match basic edit behavior
                  }
                : item
            )
          );
        }

        handleCloseModal();
      } else {
        throw new Error("Failed to update message");
      }
    } catch (error) {
      console.error("Error saving edited content:", error);
      showToast(error.message || t("text-editing.save-error"), "error");
    } finally {
      setIsLoading(false);
    }
  }, [
    chatId,
    editedMarkdown,
    role,
    workspaceSlug,
    threadSlug,
    urlSlug,
    t,
    updateHistory,
  ]);

  const handleCancel = useCallback(() => {
    handleCloseModal();
  }, [handleCloseModal]);

  // Don't show for messages without content or chatId
  if (!message || !chatId) {
    return null;
  }

  return (
    <>
      <Button
        variant="outline"
        size="icon"
        onClick={handleOpenEditor}
        data-tooltip-id="text-editing-tool"
        data-tooltip-content={t("text-editing.button-tooltip")}
        aria-label={t("text-editing.button-label")}
      >
        <TbPencilMinus />
      </Button>
      <Tooltip id="text-editing-tool" delayShow={300} className="tooltip" />

      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={t("text-editing.modal-title")}
        description={t("text-editing.modal-description")}
        className="max-w-[95%] md:max-w-[85vw] lg:max-w-[80vw] max-h-[98vh] md:max-h-[95vh] lg:max-h-[95vh]"
        footer={
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              {t("button.cancel")}
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading || !editedMarkdown || !editedMarkdown.trim()}
            >
              {isLoading ? t("text-editing.saving") : t("button.save")}
            </Button>
          </div>
        }
      >
        <TipTapEditor
          ref={editorRef}
          content={editedContent}
          onChange={handleContentChange}
          placeholder={t("text-editing.editor-placeholder")}
          className="h-[60vh] min-h-[400px]"
        />
      </Modal>
    </>
  );
}
