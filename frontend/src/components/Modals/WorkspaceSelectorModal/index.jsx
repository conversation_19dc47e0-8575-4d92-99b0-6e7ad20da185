import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState, useEffect } from "react";
import Label from "@/components/ui/Label";
import Input from "@/components/ui/Input";
import FormItem from "@/components/ui/FormItem";
import { cn } from "@/utils/classes";
import { FaRegCircleDot, FaRegCircle } from "react-icons/fa6";
import Select from "@/components/Select";
import Workspace from "@/models/workspace";
import paths from "@/utils/paths";
import {
  useSetSelectedModule,
  useSelectedFeatureCard,
} from "@/stores/userStore";
import { useModuleWorkspaces } from "@/stores/workspaceStore";
import { LuArrowRight } from "react-icons/lu";
import {
  useDocumentDraftingEnabled,
  useTabNames,
} from "@/stores/settingsStore";
import FeatureUnavailableModal from "./FeatureUnavailableModal";
import System from "@/models/system";
import { userFromStorage } from "@/utils/request";
import useNavigationWithInvoiceCheck from "@/hooks/useNavigationWithInvoiceCheck";
import InvoiceReferenceNavigationModal from "@/components/Modals/InvoiceReferenceNavigationModal";

const AiTypeEnum = z.enum(["cloud", "local"]);

const WorkspaceSelectorModal = ({
  isOpen,
  onClose,
  onOpenUploadModalForWorkspace,
}) => {
  const { t } = useTranslation();
  const moduleWorkspaces = useModuleWorkspaces();
  const tabNames = useTabNames();
  const setSelectedModule = useSetSelectedModule();
  const documentDraftingSetting = useDocumentDraftingEnabled();
  const isDocumentDraftingEnabledFull =
    documentDraftingSetting?.isDocumentDrafting;
  const selectedFeatureCard = useSelectedFeatureCard();
  const user = userFromStorage();

  const [selectedAiTypeFromRadio, setSelectedAiTypeFromRadio] =
    useState("cloud");
  const [workspaces, setWorkspaces] = useState([]);
  const [navigating, setNavigating] = useState(false);
  const [loadingSettings, setLoadingSettings] = useState(true);
  const [performLegalTaskAccess, setPerformLegalTaskAccess] = useState({
    systemEnabled: false,
    userAllowed: false,
    canAccess: false,
  });

  const {
    checkAndNavigate,
    showModal,
    destinationType,
    handleClearAndContinue,
    handleKeepAndContinue,
    handleCloseModal,
  } = useNavigationWithInvoiceCheck();

  const formSchema = z
    .object({
      aiType: AiTypeEnum,
      selectedWorkspace: z.string().optional(),
      workspaceName: z.string().optional(),
    })
    .superRefine((data, ctx) => {
      console.log("Validating form data:", data);
      console.log("Selected feature card:", selectedFeatureCard);

      if (data.aiType === "cloud") {
        if (!data.selectedWorkspace || data.selectedWorkspace.trim() === "") {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t("workspaceSelector.pleaseSelectWorkspace"),
            path: ["selectedWorkspace"],
          });
        }
      } else if (data.aiType === "local") {
        const isDraftingFlow =
          selectedFeatureCard === "draft-from-template" ||
          selectedFeatureCard === "complex-document-builder";

        if (isDraftingFlow) {
          const hasSelectedWorkspace =
            data.selectedWorkspace && data.selectedWorkspace.trim() !== "";
          const hasWorkspaceName =
            data.workspaceName && data.workspaceName.trim() !== "";

          if (!hasSelectedWorkspace && !hasWorkspaceName) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t(
                "workspaceSelector.workspaceNameOrExistingWorkspaceRequired"
              ),
              path: ["workspaceName"],
            });
          } else if (
            hasWorkspaceName &&
            !hasSelectedWorkspace &&
            data.workspaceName.trim().length <= 1
          ) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t(
                "workspaceSelector.workspaceNameMustBeMoreThanOneCharacter"
              ),
              path: ["workspaceName"],
            });
          }
        } else {
          // Local AI, but not a specific drafting flow
          if (!data.workspaceName || data.workspaceName.trim() === "") {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t("workspaceSelector.workspaceNameRequired"),
              path: ["workspaceName"],
            });
          } else if (data.workspaceName.trim().length <= 1) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t(
                "workspaceSelector.workspaceNameMustBeMoreThanOneCharacter"
              ),
              path: ["workspaceName"],
            });
          }
        }
      }
    });

  const {
    control,
    register,
    watch,
    setValue,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      aiType: "cloud",
      selectedWorkspace: "",
      workspaceName: "",
    },
  });

  const formAiType = watch("aiType");
  const watchedSelectedWorkspace = watch("selectedWorkspace");
  const watchedWorkspaceName = watch("workspaceName");

  const isLocalAiDraftingFlow =
    formAiType === "local" &&
    (selectedFeatureCard === "draft-from-template" ||
      selectedFeatureCard === "complex-document-builder");

  const isDraftingFeatureSelected =
    selectedFeatureCard === "draft-from-template" ||
    selectedFeatureCard === "complex-document-builder";

  const getAiTypeDescriptionKey = (aiType, featureCard) => {
    if (featureCard === "draft-from-template") {
      return aiType === "cloud"
        ? "workspaceSelector.cloudAiDescriptionTemplateFeature"
        : "workspaceSelector.localAiDescriptionTemplateFeature";
    } else if (featureCard === "complex-document-builder") {
      return aiType === "cloud"
        ? "workspaceSelector.cloudAiDescriptionComplexFeature"
        : "workspaceSelector.localAiDescriptionComplexFeature";
    }
    // Default keys if no specific feature card matches
    return aiType === "cloud"
      ? "workspaceSelector.cloudAiDescription"
      : "workspaceSelector.localAiDescription";
  };

  const allAiTypeOptions = [
    {
      id: "cloud-ai",
      value: "cloud",
      title:
        tabNames?.tabName1 && tabNames.tabName1.trim() !== ""
          ? tabNames.tabName1
          : t("module.legal-qa"),
      description: t(getAiTypeDescriptionKey("cloud", selectedFeatureCard)),
    },
    {
      id: "local-ai",
      value: "local",
      title:
        tabNames?.tabName2 && tabNames.tabName2.trim() !== ""
          ? tabNames.tabName2
          : t("module.document-drafting"),
      description: t(getAiTypeDescriptionKey("local", selectedFeatureCard)),
    },
  ];

  const aiTypeOptionsList = allAiTypeOptions.filter((option) => {
    if (option.id === "local-ai") {
      // This is the Document Drafting option
      // Only include this option if Document Drafting is actually enabled system-wide
      if (!isDocumentDraftingEnabledFull) {
        return false; // Exclude this option if system setting for DD is off
      }
    }
    return true; // Include cloud-ai options, or local-ai if DD is enabled
  });

  // Effect to fetch "Perform Legal Task" settings
  useEffect(() => {
    if (!isOpen) {
      setLoadingSettings(true); // Reset loading state when modal closes
      return;
    }

    // Define the async function at the root of the useEffect body
    async function fetchPerformLegalTaskSettings() {
      setLoadingSettings(true);
      try {
        const settings = await System.getPerformLegalTask();
        const systemEnabledFetched = settings?.enabled || false;
        const userAccessAllowedBySystemFetched =
          settings?.allowUserAccess || false;

        let canAccessCalculated = false;
        if (!systemEnabledFetched) {
          canAccessCalculated = false; // Feature disabled globally
        } else {
          if (user?.role === "user") {
            canAccessCalculated = userAccessAllowedBySystemFetched; // Regular user access depends on the sub-setting
          } else if (
            user?.role === "admin" ||
            user?.role === "manager" ||
            user?.role === "superuser"
          ) {
            canAccessCalculated = true; // Admins/Managers/Superusers always access if systemEnabledFetched
          }
        }
        setPerformLegalTaskAccess({
          systemEnabled: systemEnabledFetched,
          userAllowed: userAccessAllowedBySystemFetched,
          canAccess: canAccessCalculated,
        });
      } catch (error) {
        console.error("Error fetching perform legal task settings:", error);
        setPerformLegalTaskAccess({
          systemEnabled: false,
          userAllowed: false,
          canAccess: false,
        });
      } finally {
        setLoadingSettings(false);
      }
    }

    // Only call fetch these settings if the relevant feature card is selected
    if (selectedFeatureCard === "complex-document-builder") {
      fetchPerformLegalTaskSettings();
    } else {
      // If not the complex-document-builder card, no need to load these specific settings
      setLoadingSettings(false);
      // Reset to a state that wouldn't block other flows if they don't use this specific access check
      // Ensuring canAccess is true here prevents incorrectly blocking other flows
      setPerformLegalTaskAccess({
        systemEnabled: false,
        userAllowed: false,
        canAccess: true,
      });
    }
  }, [isOpen, selectedFeatureCard, user?.role]);

  useEffect(() => {
    const defaultAiType =
      selectedFeatureCard === "complex-document-builder" ? "local" : "cloud";
    reset({
      aiType: defaultAiType,
      selectedWorkspace: "",
      workspaceName: "",
    });
    setSelectedAiTypeFromRadio(defaultAiType);
    setValue("aiType", defaultAiType, { shouldValidate: false });
  }, [isOpen, selectedFeatureCard, reset, setValue]);

  useEffect(() => {
    if (isLocalAiDraftingFlow && watchedSelectedWorkspace) {
      setValue("workspaceName", "", { shouldValidate: true });
    }
  }, [watchedSelectedWorkspace, isLocalAiDraftingFlow, setValue]);

  useEffect(() => {
    if (isLocalAiDraftingFlow && watchedWorkspaceName) {
      setValue("selectedWorkspace", "", { shouldValidate: true });
    }
  }, [watchedWorkspaceName, isLocalAiDraftingFlow, setValue]);

  const handleAiTypeChange = (event) => {
    const newAiType = event.target.value;
    setValue("aiType", newAiType);
    setSelectedAiTypeFromRadio(newAiType);
  };

  const handleClose = () => {
    reset();
    setNavigating(false);
    setSelectedAiTypeFromRadio(
      selectedFeatureCard === "complex-document-builder" ? "local" : "cloud"
    );
    onClose();
  };

  const createThreadAndNavigate = async (data) => {
    try {
      console.log(data);
      const { thread, error } = await Workspace.threads.new(
        data.selectedWorkspace
      );

      if (error) {
        throw new Error(error);
      }

      const redirectUrl = paths.workspace.thread(
        data.selectedWorkspace,
        thread.slug
      );

      window.location.replace(redirectUrl);
    } catch (error) {
      console.error("Failed to create thread:", error);
      setNavigating(false);
    }
  };

  const onSubmit = async (data) => {
    setNavigating(true);

    if (selectedFeatureCard === "draft-from-template") {
      const module = data.aiType === "cloud" ? "legal-qa" : "document-drafting";
      setSelectedModule(module);
      if (data.workspaceName) {
        const { workspace } = await Workspace.new({
          name: data.workspaceName,
          type: module,
        });
        window.location.href = paths.workspace.chat(workspace.slug);
      } else {
        // Use invoice reference check for thread creation
        checkAndNavigate(() => createThreadAndNavigate(data), "thread");
        return; // Exit early to prevent setNavigating(false) at the end
      }
    }

    if (selectedFeatureCard === "complex-document-builder") {
      setSelectedModule("document-drafting");
      if (data.workspaceName) {
        try {
          const { workspace } = await Workspace.new({
            name: data.workspaceName,
            type: "document-drafting",
          });

          onOpenUploadModalForWorkspace(workspace.slug, true);
        } catch (error) {
          console.error("Failed to create workspace:", error);
          setNavigating(false);
        }
      } else {
        onOpenUploadModalForWorkspace(data.selectedWorkspace, false);
      }
      handleClose();
    }
  };

  useEffect(() => {
    const legalQaOptions = moduleWorkspaces["legal-qa"]?.map((workspace) => ({
      value: workspace.slug,
      label: workspace.name,
    }));

    const draftingOptions = moduleWorkspaces["document-drafting"]?.map(
      (workspace) => ({
        value: workspace.slug,
        label: workspace.name,
      })
    );

    if (formAiType === "cloud") {
      setWorkspaces(legalQaOptions || []);
    } else {
      setWorkspaces(draftingOptions || []);
    }
  }, [moduleWorkspaces, formAiType]);

  const cloudAiDisabled = selectedFeatureCard === "complex-document-builder";

  if (loadingSettings && selectedFeatureCard === "complex-document-builder") {
    return null;
  }

  if (selectedFeatureCard === "complex-document-builder") {
    if (!isDocumentDraftingEnabledFull || !performLegalTaskAccess.canAccess) {
      return <FeatureUnavailableModal isOpen={isOpen} onClose={handleClose} />;
    }
  }

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title={t("workspaceSelector.chooseWorkspace")}
        footer={
          <Button
            type="submit"
            form="workspace-form"
            isLoading={navigating}
            disabled={workspaces?.length === 0 && formAiType === "cloud"}
            onClick={() => {
              // This is a backup to ensure the form is submitted
              if (!navigating) {
                handleSubmit(onSubmit)();
              }
            }}
          >
            {t("workspaceSelector.next")}
            <LuArrowRight />
          </Button>
        }
        className="max-w-3xl p-8"
      >
        <form id="workspace-form" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-6">
            <FormItem>
              <Label className="text-base">
                {t("workspaceSelector.selectAiType")}
              </Label>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                {aiTypeOptionsList.map((option) => (
                  <label
                    key={option.id}
                    htmlFor={option.id}
                    className={cn(
                      "relative flex cursor-pointer items-start rounded-xl border-2 p-4 focus:outline-none",
                      selectedAiTypeFromRadio === option.value
                        ? "border-primary bg-secondary"
                        : "bg-elevated"
                    )}
                  >
                    <input
                      type="radio"
                      id={option.id}
                      value={option.value}
                      {...register("aiType")}
                      className="sr-only"
                      aria-labelledby={`${option.id}-title`}
                      aria-describedby={`${option.id}-description`}
                      onChange={handleAiTypeChange}
                      checked={selectedAiTypeFromRadio === option.value}
                      disabled={option.value === "cloud" && cloudAiDisabled}
                    />
                    {selectedAiTypeFromRadio === option.value ? (
                      <FaRegCircleDot
                        className={cn(
                          "size-5 mt-0.5 mr-3 shrink-0",
                          option.value === "cloud" && cloudAiDisabled
                            ? "text-muted cursor-not-allowed"
                            : "text-primary"
                        )}
                      />
                    ) : (
                      <FaRegCircle
                        className={cn(
                          "size-5 text-muted mt-0.5 mr-3 shrink-0",
                          option.value === "cloud" && cloudAiDisabled
                            ? "cursor-not-allowed"
                            : ""
                        )}
                      />
                    )}
                    <div
                      className={cn(
                        "flex flex-1 flex-col",
                        option.value === "cloud" && cloudAiDisabled
                          ? "cursor-not-allowed"
                          : ""
                      )}
                    >
                      <h4
                        id={`${option.id}-title`}
                        className={cn(
                          "text-lg font-medium",
                          option.value === "cloud" && cloudAiDisabled
                            ? "text-muted"
                            : ""
                        )}
                      >
                        {option.title}
                      </h4>
                      <p
                        id={`${option.id}-description`}
                        className={cn(
                          "mt-1 text-muted",
                          option.value === "cloud" && cloudAiDisabled
                            ? "text-muted"
                            : ""
                        )}
                      >
                        {option.description}
                      </p>
                    </div>
                  </label>
                ))}
              </div>
              {errors.aiType && (
                <p className="mt-2 text-xs text-red-600">
                  {errors.aiType.message}
                </p>
              )}
            </FormItem>

            <FormItem>
              <Label htmlFor="selectedWorkspace">
                {formAiType === "local" && isDraftingFeatureSelected
                  ? t("workspaceSelector.selectExistingWorkspace")
                  : t("workspaceSelector.selectExistingWorkspace")}
              </Label>
              <Controller
                name="selectedWorkspace"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    id="selectedWorkspace"
                    options={workspaces}
                    placeholder={t(
                      "workspaceSelector.selectWorkspacePlaceholder"
                    )}
                    value={field.value}
                    disabled={workspaces?.length === 0}
                  />
                )}
              />
              {errors.selectedWorkspace && (
                <p className="mt-2 text-xs text-red-600">
                  {errors.selectedWorkspace.message}
                </p>
              )}
              {formAiType === "local" && isDraftingFeatureSelected && (
                <p className="mt-2 text-sm text-muted">
                  {t("workspaceSelector.orCreateNewBelow")}
                </p>
              )}
            </FormItem>

            {formAiType === "local" &&
              (selectedFeatureCard === "draft-from-template" ||
                selectedFeatureCard === "complex-document-builder") && (
                <FormItem>
                  <Label htmlFor="workspaceName">
                    {selectedFeatureCard === "draft-from-template" ||
                    selectedFeatureCard === "complex-document-builder"
                      ? t("workspaceSelector.newWorkspaceNameOptional")
                      : t("workspaceSelector.newWorkspaceName")}
                  </Label>
                  <Input
                    id="workspaceName"
                    {...register("workspaceName")}
                    placeholder={t(
                      "workspaceSelector.workspaceNamePlaceholder"
                    )}
                  />
                  {errors.workspaceName && (
                    <p className="mt-2 text-xs text-red-600">
                      {errors.workspaceName.message}
                    </p>
                  )}
                  {formAiType === "local" &&
                    selectedFeatureCard === "complex-document-builder" &&
                    !watchedSelectedWorkspace && (
                      <p className="mt-2 text-sm text-muted">
                        {t("workspaceSelector.newWorkspaceComplexTaskInfo")}
                      </p>
                    )}
                </FormItem>
              )}
          </div>
        </form>
      </Modal>

      <InvoiceReferenceNavigationModal
        isOpen={showModal}
        onClose={handleCloseModal}
        onClearAndContinue={handleClearAndContinue}
        onKeepAndContinue={handleKeepAndContinue}
        destinationType={destinationType}
      />
    </>
  );
};

export default WorkspaceSelectorModal;
