import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import ProgressList from "../index";

// Mock the useThreadProgress hook
jest.mock("@/hooks/useThreadProgress", () => {
  return jest.fn(() => ({
    isActive: false,
    currentStep: 1,
    totalSteps: 7,
    flowType: null,
    stepDetails: [],
  }));
});

// Mock translation
const mockT = jest.fn((key, options) => {
  const translations = {
    "chatProgress.step": "Step",
    "chatProgress.noThreadSelected": "No thread selected",
    "chatProgress.noActiveProgress": "No active progress",
    "chatProgress.processing": "Processing",
    "chatProgress.types.cdb.step1.label": "Step 1: Document Analysis",
    "chatProgress.types.cdb.step1.labelNeutral": "Step 1: Analyze Documents",
    "chatProgress.types.cdb.step1.labelFinished": "Step 1: Analyzed Documents",
    "chatProgress.types.cdb.step1.desc": "Analyzing uploaded documents",
    "chatProgress.types.cdb.step2.label": "Step 2: Content Processing",
    "chatProgress.types.cdb.step2.labelNeutral": "Step 2: Process Content",
    "chatProgress.types.cdb.step2.labelFinished": "Step 2: Processed Content",
    "chatProgress.types.cdb.step2.desc": "Processing document content",
    "cdbProgress.general.placeholderSubTask": "Sub-task {{index}}",
    "cdbProgress.general.placeholderSubTaskOngoing":
      "Processing sub-task {{index}}",
    "cdbProgress.subTasks.mappingSections":
      "Mapping sections for: {{filename}}...",
    "cdbProgress.subTasks.mappingSectionsNeutral":
      "Map sections for: {{filename}}...",
    "cdbProgress.subTasks.mappingSectionsFinished":
      "Mapped sections for: {{filename}}",
    "cdbProgress.subTasks.identifyingIssues":
      "Identifying issues for Section {{sectionNumber}}: {{sectionTitle}}...",
    "cdbProgress.subTasks.identifyingIssuesNeutral":
      "Identify issues for Section {{sectionNumber}}: {{sectionTitle}}...",
    "cdbProgress.subTasks.identifyingIssuesFinished":
      "Identified issues for Section {{sectionNumber}}: {{sectionTitle}}",
    "cdbProgress.subTasks.processingDocument":
      "Processing: {{filename}} - {{action}}...",
    "cdbProgress.subTasks.processingDocumentNeutral":
      "Process: {{filename}} - {{action}}...",
    "cdbProgress.subTasks.processingDocumentFinished":
      "Processed: {{filename}} - {{action}}",
    "cdbProgress.subTasks.actions.generatingDescription":
      "Generating description",
    "cdbProgress.subTasks.generatingMemo":
      "Generating memo for: {{issueText}}...",
    "cdbProgress.subTasks.generatingMemoNeutral":
      "Generate memo for: {{issueText}}...",
    "cdbProgress.subTasks.generatingMemoFinished":
      "Generated memo for: {{issueText}}",
    "cdbProgress.subTasks.finishedMemo": "Finished memo for: {{issueText}}",
    "cdbProgress.subTasks.resolvingWorkspace":
      "Resolving workspace for: {{issueText}}...",
    "cdbProgress.subTasks.resolvingWorkspaceNeutral":
      "Resolve workspace for: {{issueText}}...",
    "cdbProgress.subTasks.resolvingWorkspaceFinished":
      "Resolved workspace for: {{issueText}}",
    "cdbProgress.subTasks.memoFor":
      "Memo for: {{issueText}} (ws: {{workspaceSlug}})",
    "cdbProgress.subTasks.memoForNeutral":
      "Generate memo for: {{issueText}} (ws: {{workspaceSlug}})",
    "cdbProgress.subTasks.memoForFinished":
      "Generated memo for: {{issueText}} (ws: {{workspaceSlug}})",
    "cdbProgress.subTasks.errorNoWorkspace":
      "Error - No workspace for: {{issueText}}...",
    // StreamDD translation keys
    "streamdd_progress_modal.sub_step_chunk_label":
      "Processing document group {{index}}",
    "streamdd_progress_modal.sub_step_chunk_label_neutral":
      "Process document group {{index}}",
    "streamdd_progress_modal.sub_step_chunk_label_finished":
      "Processed document group {{index}}",
    "streamdd_progress_modal.sub_step_memo_label":
      "Fetched legal data from {{workspaceSlug}}",
    "streamdd_progress_modal.sub_step_memo_label_neutral":
      "Fetch legal data from {{workspaceSlug}}",
    "streamdd_progress_modal.sub_step_memo_label_finished":
      "Fetched legal data from {{workspaceSlug}}",
  };

  if (options && typeof options === "object" && !Array.isArray(options)) {
    let result = translations[key] || key;
    Object.keys(options).forEach((optionKey) => {
      result = result.replace(`{{${optionKey}}}`, options[optionKey]);
    });
    return result;
  }

  return translations[key] || key;
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

const useThreadProgress = require("@/hooks/useThreadProgress");

describe("ProgressList", () => {
  const MOCK_THREAD_ID = "thread-123";

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Reset the mock implementation
    useThreadProgress.mockReturnValue({
      isActive: false,
      currentStep: 1,
      totalSteps: 7,
      flowType: null,
      stepDetails: [],
    });
  });

  describe("empty states", () => {
    it("should show no thread selected message when no threadSlug provided", () => {
      render(<ProgressList />);
      expect(screen.getByText("No thread selected")).toBeInTheDocument();
    });

    it("should show no thread selected message when threadSlug is null", () => {
      render(<ProgressList threadSlug={null} />);
      expect(screen.getByText("No thread selected")).toBeInTheDocument();
    });

    it("should show no active progress message when process is not active", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);
      expect(screen.getByText("No active progress")).toBeInTheDocument();
    });
  });

  describe("step rendering", () => {
    it("should render basic steps with default labels", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByText("Step 1")).toBeInTheDocument();
      expect(screen.getByText("Step 2")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument();
    });

    it("should render flow-specific step labels when flowType is provided", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 2,
        flowType: "cdb",
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByText("Step 1: Document Analysis")).toBeInTheDocument(); // Step 1 is in progress
      expect(screen.getByText("Step 2: Process Content")).toBeInTheDocument(); // Step 2 is pending, so shows neutral form
    });

    it("should use backend message when available in stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Custom step 1 message",
            subTasks: [],
          },
          {
            step: 2,
            status: "in_progress",
            message: "Custom step 2 message",
            subTasks: [],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByText("Custom step 1 message")).toBeInTheDocument();
      expect(screen.getByText("Custom step 2 message")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument(); // No detail, so falls back to default
    });
  });

  describe("step status and icons", () => {
    it("should show correct icons for different step statuses", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [
          { step: 1, status: "complete", subTasks: [] },
          { step: 2, status: "complete", subTasks: [] },
          { step: 3, status: "in_progress", subTasks: [] },
          { step: 4, status: "pending", subTasks: [] },
          { step: 5, status: "pending", subTasks: [] },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Complete steps should have checkmark icons (we can't easily test for specific icons)
      // But we can verify the steps are rendered with proper status
      const steps = screen.getAllByText(/Step \d/);
      expect(steps).toHaveLength(5);
    });

    it("should derive correct status for steps without explicit stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [], // No explicit step details
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should render all steps with derived statuses
      expect(screen.getByText("Step 1")).toBeInTheDocument();
      expect(screen.getByText("Step 2")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument();
      expect(screen.getByText("Step 4")).toBeInTheDocument();
      expect(screen.getByText("Step 5")).toBeInTheDocument();
    });

    it("should handle when process is inactive but currentStep > 1", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should still render steps
      expect(screen.getByText("Step 1")).toBeInTheDocument();
      expect(screen.getByText("Step 2")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument();
    });
  });

  describe("sub-tasks", () => {
    it("should render sub-tasks when they exist", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 2,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Step with sub-tasks",
            subTasks: [
              {
                subStep: 1,
                status: "complete",
                message: "Sub-task 1 complete",
              },
              {
                subStep: 2,
                status: "in_progress",
                message: "Sub-task 2 processing",
              },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByText("Step with sub-tasks")).toBeInTheDocument();

      // Click to expand sub-tasks
      fireEvent.click(screen.getByText("Step with sub-tasks"));

      expect(screen.getByText("Sub-task 1 complete")).toBeInTheDocument();
      expect(screen.getByText("Sub-task 2 processing")).toBeInTheDocument();
      expect(screen.getByText("Processing...")).toBeInTheDocument();
    });

    it("should translate subtask messages based on status", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 3,
        flowType: null,
        stepDetails: [
          {
            step: 3,
            status: "in_progress",
            message: "Step with status-based translations",
            subTasks: [
              {
                subStep: 1,
                status: "pending",
                message: "Mapping sections for: test-file.pdf...",
              },
              {
                subStep: 2,
                status: "in_progress",
                message: "Identifying issues for Section 1: Test Section...",
              },
              {
                subStep: 3,
                status: "complete",
                message: "Processing: document.pdf - Generating description...",
              },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug="test-thread" />);

      // Expand the step to see subtasks
      fireEvent.click(screen.getByText("Step with status-based translations"));

      // Check that different statuses use different translation keys
      expect(
        screen.getByText("Map sections for: test-file...")
      ).toBeInTheDocument(); // pending -> neutral (filename cleaned)
      expect(
        screen.getByText("Identifying issues for Section 1: Test Section...")
      ).toBeInTheDocument(); // in_progress -> ongoing
      expect(
        screen.getByText("Processed: document - Generating description")
      ).toBeInTheDocument(); // complete -> finished
    });

    it("should show placeholder message for sub-tasks without custom message", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Step with sub-tasks",
            subTasks: [
              { subStep: 1, status: "complete" }, // No message - should use ongoing form for complete
              { subStep: 2, status: "in_progress" }, // No message - should use ongoing form for in_progress
              { subStep: 3, status: "pending" }, // No message - should use infinitive form for pending
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Click to expand sub-tasks
      fireEvent.click(screen.getByText("Step with sub-tasks"));

      expect(screen.getByText("Processing sub-task 1")).toBeInTheDocument(); // complete status uses ongoing form
      expect(screen.getByText("Processing sub-task 2")).toBeInTheDocument(); // in_progress status uses ongoing form
      expect(screen.getByText("Sub-task 3")).toBeInTheDocument(); // pending status uses infinitive form
    });

    it("should toggle sub-task visibility when clicking on expandable step", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Expandable step",
            subTasks: [
              { subStep: 1, status: "complete", message: "Hidden sub-task" },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Sub-task should not be visible initially
      expect(screen.queryByText("Hidden sub-task")).not.toBeInTheDocument();

      // Click to expand
      fireEvent.click(screen.getByText("Expandable step"));
      expect(screen.getByText("Hidden sub-task")).toBeInTheDocument();

      // Click to collapse
      fireEvent.click(screen.getByText("Expandable step"));
      expect(screen.queryByText("Hidden sub-task")).not.toBeInTheDocument();
    });

    it("should not make step clickable if it has no sub-tasks", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Simple step",
            subTasks: [],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      const stepElement = screen.getByText("Simple step");
      // Should not have hover effects or be clickable (hard to test without DOM inspection)
      expect(stepElement).toBeInTheDocument();
    });
  });

  describe("step expansion state", () => {
    it("should maintain independent expansion state for multiple steps", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 2,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "First expandable step",
            subTasks: [{ subStep: 1, message: "First sub-task" }],
          },
          {
            step: 2,
            status: "in_progress",
            message: "Second expandable step",
            subTasks: [{ subStep: 1, message: "Second sub-task" }],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Expand first step
      fireEvent.click(screen.getByText("First expandable step"));
      expect(screen.getByText("First sub-task")).toBeInTheDocument();
      expect(screen.queryByText("Second sub-task")).not.toBeInTheDocument();

      // Expand second step (first should remain expanded)
      fireEvent.click(screen.getByText("Second expandable step"));
      expect(screen.getByText("First sub-task")).toBeInTheDocument();
      expect(screen.getByText("Second sub-task")).toBeInTheDocument();

      // Collapse first step (second should remain expanded)
      fireEvent.click(screen.getByText("First expandable step"));
      expect(screen.queryByText("First sub-task")).not.toBeInTheDocument();
      expect(screen.getByText("Second sub-task")).toBeInTheDocument();
    });
  });

  describe("edge cases", () => {
    it("should handle empty stepDetails gracefully", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should still render steps with default labels
      expect(screen.getByText("Step 1")).toBeInTheDocument();
      expect(screen.getByText("Step 2")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument();
    });

    it("should handle totalSteps of 0", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 0,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should not render any steps
      expect(screen.queryByText(/Step \d/)).not.toBeInTheDocument();
    });

    it("should handle missing subTasks array", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Step without subTasks array",
            // subTasks property missing
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(
        screen.getByText("Step without subTasks array")
      ).toBeInTheDocument();
      // Should not crash and step should not be expandable
    });

    it("should handle invalid step numbers in stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [
          {
            step: 999,
            status: "complete",
            message: "Invalid step",
            subTasks: [],
          },
          { step: 1, status: "complete", message: "Valid step", subTasks: [] },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should render all totalSteps regardless of invalid stepDetails
      expect(screen.getByText("Valid step")).toBeInTheDocument(); // Step 1 has detail
      expect(screen.getByText("Step 2")).toBeInTheDocument(); // Step 2 has no detail, uses default
      expect(screen.getByText("Step 3")).toBeInTheDocument(); // Step 3 has no detail, uses default
      expect(screen.queryByText("Invalid step")).not.toBeInTheDocument(); // Step 999 is outside range
    });

    it("should handle streamdd translation keys with status-based variations", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Processing documents",
            subTasks: [
              {
                subStep: 1,
                status: "pending",
                label: "streamdd_progress_modal.sub_step_chunk_label",
                labelArgs: { index: 1 },
              },
              {
                subStep: 2,
                status: "in_progress",
                label: "streamdd_progress_modal.sub_step_chunk_label",
                labelArgs: { index: 2 },
              },
              {
                subStep: 3,
                status: "complete",
                label: "streamdd_progress_modal.sub_step_chunk_label",
                labelArgs: { index: 3 },
              },
              {
                subStep: 4,
                status: "pending",
                label: "streamdd_progress_modal.sub_step_memo_label",
                labelArgs: { workspaceSlug: "test-workspace" },
              },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Expand the step to see subtasks
      fireEvent.click(screen.getByText("Processing documents"));

      // Check that status-based translations are used correctly
      expect(screen.getByText("Process document group 1")).toBeInTheDocument(); // pending -> neutral
      expect(
        screen.getByText("Processing document group 2")
      ).toBeInTheDocument(); // in_progress -> base
      expect(
        screen.getByText("Processed document group 3")
      ).toBeInTheDocument(); // complete -> finished
      expect(
        screen.getByText("Fetch legal data from test-workspace")
      ).toBeInTheDocument(); // pending -> neutral
    });
  });

  describe("scrolling", () => {
    it("should have scrollable container with proper max height", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 5,
        totalSteps: 10,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Test that container exists (we can't easily test CSS in jsdom)
      const container = screen.getByText("Step 1").closest("div");
      expect(container).toBeInTheDocument();
    });
  });
});
