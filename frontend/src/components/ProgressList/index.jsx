import { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { LuChevronDown, LuLoaderCircle } from "react-icons/lu";
import { IoIosCheckmarkCircle } from "react-icons/io";
import { MdError } from "react-icons/md";
import useThreadProgress from "@/hooks/useThreadProgress";
import { cleanFileName } from "@/utils/fileHelpers";

const ProgressList = ({ threadSlug }) => {
  const { t } = useTranslation();
  const {
    isActive,
    currentStep,
    totalSteps,
    flowType,
    stepDetails,
    isCompleted,
  } = useThreadProgress(threadSlug);

  // Local helper that returns `null` when the translation key is missing
  const safeT = (key, args = {}) => {
    const res = t(key, { defaultValue: "", ...args });
    return res && res !== key ? res : null;
  };

  // Function to translate sub-task messages that come from backend
  const translateSubTaskMessage = (message, status = "in_progress") => {
    if (!message || typeof message !== "string") return message;

    // Pattern: "Mapping sections for: filename..." or "Mapped sections for: filename"
    const mappingSectionsMatch = message.match(
      /^(Mapping|Mapped) sections for: (.*?)(?:\.\.\.)?$/
    );
    if (mappingSectionsMatch) {
      const verb = mappingSectionsMatch[1];
      const filename = cleanFileName(mappingSectionsMatch[2]);
      if (verb === "Mapped" || status === "complete") {
        return (
          safeT("cdbProgress.subTasks.mappingSectionsFinished", { filename }) ??
          `Mapped sections for: ${filename}`
        );
      } else if (status === "pending") {
        return (
          safeT("cdbProgress.subTasks.mappingSectionsNeutral", { filename }) ??
          `Mapping sections for: ${filename}...`
        );
      } else {
        return (
          safeT("cdbProgress.subTasks.mappingSections", { filename }) ??
          `Mapping sections for: ${filename}...`
        );
      }
    }

    // Pattern: "Identifying issues for Section X: title..." or "Identified issues for Section X: title"
    const identifyingIssuesMatch = message.match(
      /^(Identifying|Identified) issues for Section (\d+): (.*?)(?:\.\.\.)?$/
    );
    if (identifyingIssuesMatch) {
      const verb = identifyingIssuesMatch[1];
      const sectionNumber = identifyingIssuesMatch[2];
      const sectionTitle = identifyingIssuesMatch[3].trim();
      if (verb === "Identified" || status === "complete") {
        return (
          safeT("cdbProgress.subTasks.identifyingIssuesFinished", {
            sectionNumber,
            sectionTitle,
          }) ??
          `Identified issues for Section ${sectionNumber}: ${sectionTitle}`
        );
      } else if (status === "pending") {
        return (
          safeT("cdbProgress.subTasks.identifyingIssuesNeutral", {
            sectionNumber,
            sectionTitle,
          }) ??
          `Identifying issues for Section ${sectionNumber}: ${sectionTitle}...`
        );
      } else {
        return (
          safeT("cdbProgress.subTasks.identifyingIssues", {
            sectionNumber,
            sectionTitle,
          }) ??
          `Identifying issues for Section ${sectionNumber}: ${sectionTitle}...`
        );
      }
    }

    // Pattern: "Processing: filename - action..." or "Processed: filename - action"
    const processingDocumentMatch = message.match(
      /^(Processing|Processed): (.+?) - (.*?)(?:\.\.\.)?$/
    );
    if (processingDocumentMatch) {
      const verb = processingDocumentMatch[1];
      const filename = cleanFileName(processingDocumentMatch[2]);
      const actionText = processingDocumentMatch[3].trim();

      // Translate common actions
      let translatedAction = actionText;
      if (actionText === "Generating description") {
        translatedAction =
          safeT("cdbProgress.subTasks.actions.generatingDescription") ??
          "Generating description";
      } else if (actionText === "Checking relevance") {
        translatedAction =
          safeT("cdbProgress.subTasks.actions.checkingRelevance") ??
          "Checking relevance";
      } else if (actionText.includes("Generating section list")) {
        translatedAction =
          safeT("cdbProgress.subTasks.actions.generatingSectionList") ??
          "Generating section list";
      }

      // Use different translation keys based on verb/status
      if (verb === "Processed" || status === "complete") {
        return (
          safeT("cdbProgress.subTasks.processingDocumentFinished", {
            filename,
            action: translatedAction,
          }) ?? `Processed: ${filename} - ${translatedAction}`
        );
      } else if (status === "pending") {
        return (
          safeT("cdbProgress.subTasks.processingDocumentNeutral", {
            filename,
            action: translatedAction,
          }) ?? `Processing: ${filename} - ${translatedAction}...`
        );
      } else {
        return (
          safeT("cdbProgress.subTasks.processingDocument", {
            filename,
            action: translatedAction,
          }) ?? `Processing: ${filename} - ${translatedAction}...`
        );
      }
    }

    // Pattern: "Generating memo for: issueText..." or "Generated memo for: issueText"
    const generatingMemoMatch = message.match(
      /^(Generating|Generated) memo for: (.*?)(?:\.\.\.)?$/
    );
    if (generatingMemoMatch) {
      const verb = generatingMemoMatch[1];
      const issueText = generatingMemoMatch[2];
      if (verb === "Generated" || status === "complete") {
        return (
          safeT("cdbProgress.subTasks.generatingMemoFinished", { issueText }) ??
          `Generated memo for: ${issueText}`
        );
      } else if (status === "pending") {
        return (
          safeT("cdbProgress.subTasks.generatingMemoNeutral", { issueText }) ??
          `Generating memo for: ${issueText}...`
        );
      } else {
        return (
          safeT("cdbProgress.subTasks.generatingMemo", { issueText }) ??
          `Generating memo for: ${issueText}...`
        );
      }
    }

    // Pattern: "Generated memo for: issueText" (completion)
    const generatedMemoMatch = message.match(/^Generated memo for: (.+)$/);
    if (generatedMemoMatch) {
      const issueText = generatedMemoMatch[1];
      return (
        safeT("cdbProgress.subTasks.generatingMemoFinished", { issueText }) ??
        `Generated memo for: ${issueText}`
      );
    }

    // Pattern: "Drafting Section X: title..." or "Drafted Section X: title" (mainDoc flow)
    const draftingSectionMatch = message.match(
      /^(Drafting|Drafted) Section (\d+): (.*?)(?:\.\.\.)?$/
    );
    if (draftingSectionMatch) {
      const verb = draftingSectionMatch[1];
      const sectionNumber = draftingSectionMatch[2];
      const sectionTitle = draftingSectionMatch[3].trim();
      if (verb === "Drafted" || status === "complete") {
        return (
          safeT("cdbProgress.subTasks.draftingSectionFinished", {
            sectionNumber,
            sectionTitle,
          }) ?? `Drafted Section ${sectionNumber}: ${sectionTitle}`
        );
      } else if (status === "pending") {
        return (
          safeT("cdbProgress.subTasks.draftingSectionNeutral", {
            sectionNumber,
            sectionTitle,
          }) ?? `Draft Section ${sectionNumber}: ${sectionTitle}...`
        );
      } else {
        return (
          safeT("cdbProgress.subTasks.draftingSection", {
            sectionNumber,
            sectionTitle,
          }) ?? `Drafting Section ${sectionNumber}: ${sectionTitle}...`
        );
      }
    }

    // Pattern: "Resolving workspace for: issueText..." or "Resolved workspace for: issueText"
    const resolvingWorkspaceMatch = message.match(
      /^(Resolving|Resolved) workspace for: (.*?)(?:\.\.\.)?$/
    );
    if (resolvingWorkspaceMatch) {
      const verb = resolvingWorkspaceMatch[1];
      const issueText = resolvingWorkspaceMatch[2];
      if (verb === "Resolved" || status === "complete") {
        return (
          safeT("cdbProgress.subTasks.resolvingWorkspaceFinished", {
            issueText,
          }) ?? `Resolved workspace for: ${issueText}`
        );
      } else if (status === "pending") {
        return (
          safeT("cdbProgress.subTasks.resolvingWorkspaceNeutral", {
            issueText,
          }) ?? `Resolving workspace for: ${issueText}...`
        );
      } else {
        return (
          safeT("cdbProgress.subTasks.resolvingWorkspace", { issueText }) ??
          `Resolving workspace for: ${issueText}...`
        );
      }
    }

    // Pattern: "Memo for: issueText (ws: workspaceSlug)" (noMainDoc flow)
    const memoForMatch = message.match(/^Memo for: (.+) \(ws: (.+)\)$/);
    if (memoForMatch) {
      const issueText = memoForMatch[1];
      const workspaceSlug = memoForMatch[2];
      // Use different translation keys based on status
      if (status === "pending") {
        return (
          safeT("cdbProgress.subTasks.memoForNeutral", {
            issueText,
            workspaceSlug,
          }) ?? `Memo for: ${issueText} (ws: ${workspaceSlug})`
        );
      } else if (status === "complete") {
        return (
          safeT("cdbProgress.subTasks.memoForFinished", {
            issueText,
            workspaceSlug,
          }) ?? `Memo for: ${issueText} (ws: ${workspaceSlug})`
        );
      } else {
        return (
          safeT("cdbProgress.subTasks.memoFor", { issueText, workspaceSlug }) ??
          `Memo for: ${issueText} (ws: ${workspaceSlug})`
        );
      }
    }

    // Pattern: "Error - No workspace for: issueText..." (noMainDoc flow)
    const errorNoWorkspaceMatch = message.match(
      /^Error - No workspace for: (.+)\.\.\.$/
    );
    if (errorNoWorkspaceMatch) {
      const issueText = errorNoWorkspaceMatch[1];
      return (
        safeT("cdbProgress.subTasks.errorNoWorkspace", { issueText }) ??
        `Error - No workspace for: ${issueText}...`
      );
    }

    // Pattern: "Error drafting Section X: title" (referenceFiles flow)
    const errorDraftingSectionMatch = message.match(
      /^Error drafting Section (\d+): (.+)$/
    );
    if (errorDraftingSectionMatch) {
      const sectionNumber = errorDraftingSectionMatch[1];
      const sectionTitle = errorDraftingSectionMatch[2].trim();
      return (
        safeT("cdbProgress.subTasks.errorDraftingSection", {
          sectionNumber,
          sectionTitle,
        }) ?? `Error drafting Section ${sectionNumber}: ${sectionTitle}`
      );
    }

    // If no pattern matches, return original message
    return message;
  };

  const [expandedSteps, setExpandedSteps] = useState(new Set());

  const toggleStepExpansion = (stepNumber) => {
    setExpandedSteps((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(stepNumber)) {
        newSet.delete(stepNumber);
      } else {
        newSet.add(stepNumber);
      }
      return newSet;
    });
  };

  const allSteps = useMemo(() => {
    const steps = [];
    for (let i = 1; i <= totalSteps; i++) {
      // Find step detail from backend data
      const stepDetail = stepDetails.find((detail) => detail.step === i);

      const stepStatus =
        stepDetail?.status ||
        (i < currentStep
          ? "complete"
          : i === currentStep && isActive
            ? "in_progress"
            : i === currentStep && (isCompleted || !isActive)
              ? "complete"
              : "pending");

      let stepLabel = `${t("chatProgress.step")} ${i}`;

      // Prioritize i18n translations over backend messages
      if (flowType) {
        const stepKey = `chatProgress.types.${flowType}.step${i}`;

        // Choose label based on step status
        let labelKey = "label"; // Default to ongoing form
        if (stepStatus === "pending") {
          labelKey = "labelNeutral"; // Use neutral/infinitive form for pending
        } else if (stepStatus === "complete") {
          labelKey = "labelFinished"; // Use finished/past participle form for complete
        }

        const translationKey = `${stepKey}.${labelKey}`;
        const translatedLabel = t(translationKey, {
          defaultValue: "",
        });
        if (translatedLabel && translatedLabel !== translationKey) {
          stepLabel = translatedLabel;
        } else {
          // Fallback to regular label if specific form not available
          const fallbackKey = `${stepKey}.label`;
          const fallbackLabel = t(fallbackKey, { defaultValue: "" });
          if (fallbackLabel && fallbackLabel !== fallbackKey) {
            stepLabel = fallbackLabel;
          } else if (stepDetail?.message) {
            // Only use backend message if no translation is available
            stepLabel = stepDetail.message;
          }
        }
      } else if (stepDetail?.message) {
        // Use backend message only if no flowType is available
        stepLabel = stepDetail.message;
      }

      // Create display sub-tasks: actual sub-tasks + placeholders for expected total
      let displaySubTasks = stepDetail?.subTasks || [];
      const expectedTotal = stepDetail?.expectedTotal;

      // Only create placeholders if:
      // 1. We have an expectedTotal
      // 2. The step is not complete
      // 3. We don't already have enough actual subtasks
      if (
        expectedTotal &&
        expectedTotal > 0 &&
        stepStatus !== "complete" &&
        displaySubTasks.length < expectedTotal
      ) {
        // Create placeholders for the expected total
        const placeholders = [];
        const existingSubSteps = new Set(
          displaySubTasks.map((st) => st.subStep)
        );

        for (let j = 1; j <= expectedTotal; j++) {
          // Check if we already have an actual sub-task for this position
          if (!existingSubSteps.has(j)) {
            // Only create placeholder if the step is still active or in progress
            // Don't create placeholders for pending steps that haven't started
            if (stepStatus === "in_progress" || stepStatus === "starting") {
              placeholders.push({
                subStep: j,
                status: "pending",
                message: null,
                label: null,
                isPlaceholder: true,
              });
            }
          }
        }

        // Combine actual sub-tasks with placeholders and sort by subStep
        displaySubTasks = [...displaySubTasks, ...placeholders].sort(
          (a, b) => a.subStep - b.subStep
        );
      }

      // Additional cleanup: Remove any remaining placeholders if the step is complete
      // This handles cases where the step completed but we still have stale placeholders
      if (stepStatus === "complete") {
        displaySubTasks = displaySubTasks.filter(
          (subTask) => !subTask.isPlaceholder
        );
      }

      steps.push({
        step: i,
        label: stepLabel,
        status: stepStatus,
        subTasks: displaySubTasks,
        hasSubTasks: displaySubTasks && displaySubTasks.length > 0,
        expectedTotal: expectedTotal,
      });
    }
    return steps;
  }, [totalSteps, currentStep, isActive, flowType, stepDetails, t]);

  const getStatusIcon = (status, step) => {
    switch (status) {
      case "complete":
        return <IoIosCheckmarkCircle className="text-green-500 size-5" />;
      case "starting":
      case "in_progress":
        return (
          <LuLoaderCircle className="animate-spin text-foreground size-5" />
        );
      default:
        return (
          <div className="size-5 bg-secondary rounded-md flex items-center justify-center text-xs font-medium text-foreground">
            {step.step}
          </div>
        );
    }
  };

  const getSubTaskStatusIcon = (status) => {
    switch (status) {
      case "complete":
        return (
          <IoIosCheckmarkCircle className="text-green-500 size-4 flex-shrink-0" />
        );
      case "starting":
      case "in_progress":
        return (
          <LuLoaderCircle className="animate-spin text-foreground size-4 flex-shrink-0" />
        );
      case "error":
      case "failed":
        return <MdError className="text-red-500 size-4 flex-shrink-0" />;
      default:
        return (
          <div className="size-3 mt-0.5 bg-secondary-hover rounded-full flex items-center justify-center flex-shrink-0" />
        );
    }
  };

  if (!threadSlug) {
    return (
      <div className="text-center text-muted py-8">
        {t("chatProgress.noThreadSelected")}
      </div>
    );
  }

  if (!isActive && !isCompleted && currentStep === 1) {
    return (
      <div className="text-center text-muted py-8">
        {t("chatProgress.noActiveProgress")}
      </div>
    );
  }

  return (
    <div className="space-y-2 overflow-y-auto max-h-[32rem]">
      {allSteps.map((step) => {
        const isExpanded = expandedSteps.has(step.step);

        return (
          <div key={step.step} className="space-y-1">
            <div
              className={`flex items-start gap-2 p-3 rounded-lg transition-colors`}
            >
              <div className="flex-shrink-0 pt-0.5">
                {getStatusIcon(step.status, step)}
              </div>

              <div className="flex-1">
                <div
                  className={`flex items-center gap-2 ${
                    step.hasSubTasks ? "cursor-pointer transition-colors" : ""
                  }`}
                  onClick={
                    step.hasSubTasks
                      ? () => toggleStepExpansion(step.step)
                      : undefined
                  }
                >
                  <div
                    className={`font-medium ${
                      step.status === "in_progress" ||
                      step.status === "complete"
                        ? "text-foreground"
                        : "text-muted"
                    } ${step.hasSubTasks ? "hover:underline" : ""}`}
                  >
                    {step.label}
                  </div>

                  {step.hasSubTasks && (
                    <LuChevronDown
                      className={`shrink-0 size-5 ${isExpanded ? "" : "-rotate-90"} transition-transform`}
                    />
                  )}
                </div>
              </div>
            </div>

            {step.hasSubTasks && isExpanded && (
              <div className="ml-8 space-y-2">
                {step.subTasks.map((subTask, index) => (
                  <div
                    key={subTask.subStep ?? `${step.step}-${index}`}
                    className="flex items-start gap-3 px-2 py-1 rounded"
                  >
                    <div className="pt-0.5">
                      {getSubTaskStatusIcon(subTask.status)}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-foreground">
                        {(() => {
                          // Priority order: message > label > placeholder based on status
                          if (subTask.message) {
                            return translateSubTaskMessage(
                              subTask.message,
                              subTask.status
                            );
                          }

                          if (subTask.label) {
                            const isTranslationKey =
                              typeof subTask.label === "string" &&
                              (subTask.label.startsWith("cdbProgress.") ||
                                subTask.label.startsWith(
                                  "streamdd_progress_modal."
                                ));

                            if (isTranslationKey) {
                              // This is a translation key - use t() with labelArgs
                              const translationKey = subTask.label;
                              const args = subTask.labelArgs || {};

                              // For streamdd flows, add status-based translation if available
                              if (
                                translationKey.startsWith(
                                  "streamdd_progress_modal.sub_step_"
                                )
                              ) {
                                let statusSuffix = "";
                                if (subTask.status === "pending") {
                                  statusSuffix = "_neutral";
                                } else if (subTask.status === "complete") {
                                  statusSuffix = "_finished";
                                }

                                // Try status-based translation first, fallback to base translation
                                const statusKey = translationKey + statusSuffix;
                                const statusTranslation = t(statusKey, {
                                  ...args,
                                  defaultValue: "",
                                });
                                if (
                                  statusTranslation &&
                                  statusTranslation !== statusKey
                                ) {
                                  return statusTranslation;
                                }
                              }

                              // Use base translation key
                              const translation = t(translationKey, args);
                              if (
                                translation &&
                                translation !== translationKey
                              ) {
                                return translation;
                              }
                            } else {
                              // This is a raw message - try to translate it, but clean the filename if no translation occurs.
                              const translatedLabel = translateSubTaskMessage(
                                subTask.label,
                                subTask.status
                              );
                              return translatedLabel !== subTask.label
                                ? translatedLabel
                                : cleanFileName(subTask.label);
                            }
                          }

                          // Otherwise, use placeholder text based on status
                          if (subTask.status === "pending") {
                            return t("cdbProgress.general.placeholderSubTask", {
                              index: subTask.subStep,
                            });
                          } else {
                            return t(
                              "cdbProgress.general.placeholderSubTaskOngoing",
                              {
                                index: subTask.subStep,
                              }
                            );
                          }
                        })()}
                      </div>
                      {(subTask.status === "starting" ||
                        subTask.status === "in_progress") && (
                        <div className="text-xs text-muted">
                          {t("chatProgress.processing")}...
                        </div>
                      )}
                      {(subTask.status === "error" ||
                        subTask.status === "failed") && (
                        <div className="text-xs text-red-500">
                          {t("chatProgress.failed")}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ProgressList;
