import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import ProgressModal from "@/components/Modals/ProgressModal";
import AbortWarningModal from "@/components/Modals/AbortWarningModal";
import useThreadProgress from "@/hooks/useThreadProgress";
import Progress from "@/components/ui/Progress";
import { calculateIncrementalProgress } from "@/utils/progressUtils";
import { cn } from "@/utils/classes";

export default function ChatProgress({ threadSlug = "", updateHistory }) {
  const { t } = useTranslation();
  const [isProgressModalOpen, setIsProgressModalOpen] = useState(false);
  const [isAbortModalOpen, setIsAbortModalOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const { currentStep, totalSteps, flowType, cancel, stepDetails } =
    useThreadProgress(threadSlug);

  const addCancellationMessage = () => {
    if (updateHistory) {
      const cancellationMessage = {
        uuid: `cancelled-${Date.now()}`,
        type: "statusResponse",
        content: t("chatProgress.cancelled"),
        role: "assistant",
        closed: true,
        error: null,
        animate: false,
        pending: false,
      };

      updateHistory((prevHistory) => [...prevHistory, cancellationMessage]);
    }
  };

  const handleAbort = () => {
    cancel();
    addCancellationMessage();
  };

  const handleShowAbortModal = () => {
    setIsAbortModalOpen(true);
  };

  const handleCancelAbort = () => {
    setIsAbortModalOpen(false);
  };

  const handleConfirmAbort = () => {
    setIsAbortModalOpen(false);
    handleAbort();
  };

  const handleViewDetails = () => {
    setIsProgressModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsProgressModalOpen(false);
  };

  // Use the incremental progress calculation
  const progressValue = calculateIncrementalProgress(
    currentStep,
    totalSteps,
    stepDetails
  );

  const stepKey =
    flowType && currentStep
      ? `chatProgress.types.${flowType}.step${currentStep}`
      : null;

  const stepLabel = stepKey
    ? t(`${stepKey}.label`)
    : `${t("chatProgress.step")} ${currentStep}`;
  const stepDescription = stepKey
    ? t(`${stepKey}.desc`)
    : t("chatProgress.processing");

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 350);

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <div
        className={cn(
          `flex flex-col gap-6 max-w-[26rem] px-5 pt-5 pb-6 rounded-xl border bg-elevated transition-all duration-300 transform`,
          isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-8"
        )}
      >
        <div className="flex items-start justify-between gap-4">
          <div className="flex flex-col gap-1 max-w-[18rem]">
            <h4 className="text-lg font-medium truncate">{stepLabel}</h4>
            <p className="text-muted font-medium line-clamp-2">
              {stepDescription}
            </p>
          </div>

          <div className="flex flex-col items-center gap-3 w-20">
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDetails}
              className="w-full"
            >
              {t("chatProgress.details")}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleShowAbortModal}
              className="w-full text-red-600"
            >
              {t("chatProgress.abort")}
            </Button>
          </div>
        </div>

        <Progress value={progressValue} />
      </div>

      <ProgressModal
        isOpen={isProgressModalOpen}
        onClose={handleCloseModal}
        threadSlug={threadSlug}
      />

      <AbortWarningModal
        isOpen={isAbortModalOpen}
        onCancel={handleCancelAbort}
        onAbort={handleConfirmAbort}
      />
    </>
  );
}
