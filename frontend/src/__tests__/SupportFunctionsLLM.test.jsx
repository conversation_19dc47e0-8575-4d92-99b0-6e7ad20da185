import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import SupportFunctionsLLM from "../pages/GeneralSettings/SupportFunctionsLLM";

// Mock the system model
jest.mock("../models/system", () => ({
  setSupportFunctionsLLM: jest.fn(),
  keys: jest.fn(),
}));

// Mock the toast system
jest.mock("../utils/toast", () => jest.fn());

// Mock the translation system
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key, options) => {
      if (options && typeof options === "object") {
        let result = key;
        Object.keys(options).forEach((optionKey) => {
          result = result.replace(`{{${optionKey}}}`, options[optionKey]);
        });
        return result;
      }
      return key;
    },
  }),
}));

// Mock paths utility
jest.mock("../utils/paths", () => ({
  home: () => "/",
}));

// Mock components
jest.mock("../components/SettingsSidebar", () => {
  return function MockSidebar() {
    return <div data-testid="sidebar">Sidebar</div>;
  };
});

jest.mock("../components/HeaderWorkspace", () => {
  return function MockHeaderWorkspace() {
    return <div data-testid="header-workspace">Header</div>;
  };
});

jest.mock("../components/Preloader", () => {
  return function MockPreLoader() {
    return <div data-testid="preloader">Loading...</div>;
  };
});

jest.mock("../components/Button", () => ({
  Button: function MockButton({ children, onClick, disabled, type, ...props }) {
    return (
      <button
        onClick={onClick}
        disabled={disabled}
        type={type}
        data-testid="save-button"
        {...props}
      >
        {children}
      </button>
    );
  },
}));

jest.mock("../components/ui/Toggle", () => {
  return function MockToggle({ label, description, checked, onCheckedChange }) {
    return (
      <div data-testid="toggle">
        <label>
          <input
            type="checkbox"
            checked={checked}
            onChange={(e) => onCheckedChange(e.target.checked)}
            data-testid={`toggle-${
              label.includes("prompt-upgrade")
                ? "prompt-upgrade"
                : label.includes("validation")
                  ? "validation"
                  : "manual-time"
            }`}
          />
          {label}
        </label>
        {description && <p>{description}</p>}
      </div>
    );
  };
});

jest.mock("../components/LLMSelection/BaseLLMPreference", () => {
  return function MockBaseLLMPreference({
    selectedLLM,
    onLLMChange,
    settings,
    setHasChanges,
  }) {
    return (
      <div data-testid="base-llm-preference">
        <div data-testid="provider-key">LLMProvider_SUPPORT</div>
        <input
          data-testid="llm-provider-input"
          value={selectedLLM || ""}
          onChange={(e) => {
            onLLMChange(e.target.value);
            if (setHasChanges) setHasChanges(true);
          }}
        />
      </div>
    );
  };
});

// Mock @phosphor-icons/react
jest.mock("@phosphor-icons/react", () => ({
  ArrowLeft: function MockArrowLeft(props) {
    return (
      <span data-testid="arrow-left" {...props}>
        ←
      </span>
    );
  },
}));

// Test wrapper component
const TestWrapper = ({ children }) => <BrowserRouter>{children}</BrowserRouter>;

describe("SupportFunctionsLLM Component", () => {
  let mockSystem;
  let mockShowToast;

  beforeEach(() => {
    jest.clearAllMocks();

    // Get the mocked modules
    mockSystem = require("../models/system");
    mockShowToast = require("../utils/toast");

    // Setup default mock responses
    mockSystem.setSupportFunctionsLLM.mockResolvedValue({ success: true });
    mockSystem.keys.mockResolvedValue({
      LLMProvider_SUPPORT: "openai",
      supportFunctionPromptUpgrade: false,
      supportFunctionValidation: false,
      supportFunctionManualTime: false,
    });
    mockShowToast.mockImplementation(() => {});
  });

  const renderComponent = (mockSettings = {}) => {
    if (Object.keys(mockSettings).length > 0) {
      mockSystem.keys.mockResolvedValue(mockSettings);
    }

    return render(
      <TestWrapper>
        <SupportFunctionsLLM />
      </TestWrapper>
    );
  };

  test("renders the support functions LLM page", async () => {
    renderComponent();

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    expect(screen.getByText("support-functions-llm.title")).toBeInTheDocument();
    expect(
      screen.getByText("support-functions-llm.description")
    ).toBeInTheDocument();
    expect(screen.getByTestId("base-llm-preference")).toBeInTheDocument();
  });

  test("displays loading state initially", () => {
    renderComponent();

    expect(screen.getByTestId("preloader")).toBeInTheDocument();
  });

  test("loads settings on mount", async () => {
    renderComponent();

    await waitFor(() => {
      expect(mockSystem.keys).toHaveBeenCalledWith(false);
    });
  });

  test("displays LLM provider selection", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    const providerInput = screen.getByTestId("llm-provider-input");
    expect(providerInput).toBeInTheDocument();
    expect(screen.getByTestId("provider-key")).toHaveTextContent(
      "LLMProvider_SUPPORT"
    );
  });

  test("displays support function toggles", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    expect(screen.getByTestId("toggle-prompt-upgrade")).toBeInTheDocument();
    expect(screen.getByTestId("toggle-validation")).toBeInTheDocument();
    expect(screen.getByTestId("toggle-manual-time")).toBeInTheDocument();

    expect(
      screen.getByText("support-functions-llm.prompt-upgrade.title")
    ).toBeInTheDocument();
    expect(
      screen.getByText("support-functions-llm.validation.title")
    ).toBeInTheDocument();
    expect(
      screen.getByText("support-functions-llm.manual-time.title")
    ).toBeInTheDocument();
  });

  test("save button is disabled when no changes are made", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    // Initially no save button should be visible since hasChanges is false
    expect(screen.queryByTestId("save-button")).not.toBeInTheDocument();
  });

  test("save button appears when changes are made", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    const promptUpgradeInput = screen.getByTestId("toggle-prompt-upgrade");
    fireEvent.click(promptUpgradeInput);

    await waitFor(() => {
      const saveButton = screen.getByTestId("save-button");
      expect(saveButton).toBeInTheDocument();
      expect(saveButton).not.toBeDisabled();
    });
  });

  test("toggles can be enabled and disabled", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    const promptUpgradeInput = screen.getByTestId("toggle-prompt-upgrade");
    const validationInput = screen.getByTestId("toggle-validation");
    const manualTimeInput = screen.getByTestId("toggle-manual-time");

    expect(promptUpgradeInput).not.toBeChecked();
    expect(validationInput).not.toBeChecked();
    expect(manualTimeInput).not.toBeChecked();

    fireEvent.click(promptUpgradeInput);
    fireEvent.click(validationInput);

    expect(promptUpgradeInput).toBeChecked();
    expect(validationInput).toBeChecked();
    expect(manualTimeInput).not.toBeChecked();
  });

  test("LLM provider can be selected", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    const providerInput = screen.getByTestId("llm-provider-input");

    fireEvent.change(providerInput, { target: { value: "anthropic" } });

    expect(providerInput.value).toBe("anthropic");

    await waitFor(() => {
      const saveButton = screen.getByTestId("save-button");
      expect(saveButton).not.toBeDisabled();
    });
  });

  test("calls setSupportFunctionsLLM on save", async () => {
    const mockSettings = {
      LLMProvider_SUPPORT: "openai",
      OpenAiModelPref_SUPPORT: "gpt-4o-mini",
      supportFunctionPromptUpgrade: true,
      supportFunctionValidation: false,
      supportFunctionManualTime: true,
    };

    renderComponent(mockSettings);

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    // Make a change to enable the save button (just toggle change, no LLM change)
    const toggleElement = screen.getByTestId("toggle-prompt-upgrade");
    fireEvent.click(toggleElement);

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockSystem.setSupportFunctionsLLM).toHaveBeenCalledWith({
        // No LLM provider included since selectedLLM === settings.LLMProvider_SUPPORT
        // Model preferences included because selectedLLM is "openai" and the pref exists
        OpenAiModelPref_SUPPORT: "gpt-4o-mini",
        supportFunctionPromptUpgrade: false, // Will be toggled from true to false
        supportFunctionValidation: false,
        supportFunctionManualTime: true,
      });
    });
  });

  test("handles save errors gracefully", async () => {
    mockSystem.setSupportFunctionsLLM.mockRejectedValue(
      new Error("Save failed")
    );

    const consoleSpy = jest
      .spyOn(console, "error")
      .mockImplementation(() => {});

    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    // Make a change
    const promptUpgradeInput = screen.getByTestId("toggle-prompt-upgrade");
    fireEvent.click(promptUpgradeInput);

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    // Save
    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        "support-functions-llm.save-error",
        "error"
      );
    });

    consoleSpy.mockRestore();
  });

  test("should handle API failure response", async () => {
    mockSystem.setSupportFunctionsLLM.mockResolvedValue({
      success: false,
      error: "Invalid provider",
    });

    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    const providerInput = screen.getByTestId("llm-provider-input");
    fireEvent.change(providerInput, { target: { value: "invalid" } });

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId("save-button"));

    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        "support-functions-llm.save-error",
        "error"
      );
    });
  });

  test("should have proper component structure", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    // Should have sidebar and header
    expect(screen.getByTestId("sidebar")).toBeInTheDocument();
    expect(screen.getByTestId("header-workspace")).toBeInTheDocument();

    // Should have proper heading structure
    expect(screen.getByText("support-functions-llm.title")).toBeInTheDocument();
    expect(
      screen.getByText("support-functions-llm.description")
    ).toBeInTheDocument();
  });

  test("should prevent multiple submissions when saving", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    // Make a change to enable the save button
    const promptUpgradeToggle = screen.getByTestId("toggle-prompt-upgrade");
    fireEvent.click(promptUpgradeToggle);

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");

    // Click multiple times quickly
    fireEvent.click(saveButton);
    fireEvent.click(saveButton);
    fireEvent.click(saveButton);

    // Should only be called once due to saving state protection
    await waitFor(() => {
      expect(mockSystem.setSupportFunctionsLLM).toHaveBeenCalledTimes(1);
    });
  });

  test("should handle settings with model preferences", async () => {
    const mockSettings = {
      LLMProvider_SUPPORT: "anthropic",
      AnthropicModelPref_SUPPORT: "claude-3-haiku",
      supportFunctionPromptUpgrade: false,
      supportFunctionValidation: true,
      supportFunctionManualTime: false,
    };

    renderComponent(mockSettings);

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    // Make a change to enable the save button (just toggle change, no LLM change)
    const toggleElement = screen.getByTestId("toggle-validation");
    fireEvent.click(toggleElement);

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockSystem.setSupportFunctionsLLM).toHaveBeenCalledWith({
        // No LLM provider included since selectedLLM === settings.LLMProvider_SUPPORT
        // Model preferences included because selectedLLM is "anthropic" and the pref exists
        AnthropicModelPref_SUPPORT: "claude-3-haiku",
        supportFunctionPromptUpgrade: false,
        supportFunctionValidation: false, // Will be toggled from true to false
        supportFunctionManualTime: false,
      });
    });
  });

  test("should show success toast on successful save", async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    // Make a change to enable the save button
    const promptUpgradeToggle = screen.getByTestId("toggle-prompt-upgrade");
    fireEvent.click(promptUpgradeToggle);

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        "support-functions-llm.save-success",
        "success"
      );
    });
  });

  test("should include LLM provider when it changes", async () => {
    const mockSettings = {
      LLMProvider_SUPPORT: "openai",
      supportFunctionPromptUpgrade: false,
      supportFunctionValidation: false,
      supportFunctionManualTime: false,
    };

    renderComponent(mockSettings);

    await waitFor(() => {
      expect(screen.queryByTestId("preloader")).not.toBeInTheDocument();
    });

    // Change the LLM provider
    const providerInput = screen.getByTestId("llm-provider-input");
    fireEvent.change(providerInput, { target: { value: "anthropic" } });

    await waitFor(() => {
      expect(screen.getByTestId("save-button")).toBeInTheDocument();
    });

    const saveButton = screen.getByTestId("save-button");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockSystem.setSupportFunctionsLLM).toHaveBeenCalledWith({
        LLMProvider_SUPPORT: "anthropic", // Included because it changed
        supportFunctionPromptUpgrade: false,
        supportFunctionValidation: false,
        supportFunctionManualTime: false,
      });
    });
  });
});
