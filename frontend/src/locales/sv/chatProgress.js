export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Bearbetar",
    processing: "Bearbetar...",
    step: "Steg",
    timeLeft: "Tid kvar",
    details: "Detaljer",
    abort: "Avbryt",
    modalTitle: "Förloppsinformation",
    "close-msg": "Är du säker på att du vill avbryta processen?",
    noThreadSelected: "Ingen tråd vald",
    noActiveProgress: "Inget aktivt framsteg",
    of: "av",
    started: "Startad",
    failed: "Misslyckades",
    error: {
      title: "Det uppstod ett fel när din begäran bearbetades.",
      description:
        "Försök igen eller kontakta supporten om problemet kvarstår.",
      retry: "Försök igen",
      dismiss: "Stäng",
      showDetails: "Visa tekniska detaljer",
      hideDetails: "Dölj tekniska detaljer",
    },
    cancelled: "Processen avbröts.",
    completed: "Process slutförd",
    completedDescription: "Dokument har genererats och lagts till i chatten",
    types: {
      main: {
        step1: {
          label: "Genererar sektionslista",
          labelNeutral: "Generera sektionslista",
          labelFinished: "Genererat sektionslista",
          desc: "Använder huvuddokumentet för att skapa en initial struktur.",
        },
        step2: {
          label: "Bearbetar dokument",
          labelNeutral: "Bearbeta dokument",
          labelFinished: "Bearbetat dokument",
          desc: "Genererar beskrivningar och kontrollerar relevans.",
        },
        step3: {
          label: "Kopplar dokument till sektioner",
          labelNeutral: "Koppla dokument till sektioner",
          labelFinished: "Kopplat dokument till sektioner",
          desc: "Tilldelar relevanta dokument till varje sektion.",
        },
        step4: {
          label: "Identifierar juridiska frågor",
          labelNeutral: "Identifiera juridiska frågor",
          labelFinished: "Identifierat juridiska frågor",
          desc: "Extraherar centrala juridiska frågor för varje sektion.",
        },
        step5: {
          label: "Genererar juridiska PM",
          labelNeutral: "Generera juridiska PM",
          labelFinished: "Genererat juridiska PM",
          desc: "Skapar juridiska promemorior för de identifierade frågorna.",
        },
        step6: {
          label: "Utformar sektioner",
          labelNeutral: "Utforma sektioner",
          labelFinished: "Utformat sektioner",
          desc: "Komponerar innehållet för varje enskild sektion.",
        },
        step7: {
          label: "Kombinerar & slutför dokument",
          labelNeutral: "Kombinera & slutföra dokument",
          labelFinished: "Kombinerat & slutfört dokument",
          desc: "Sammanställer sektionerna till det slutliga dokumentet.",
        },
      },
      noMain: {
        step1: {
          label: "Bearbetar dokument",
          labelNeutral: "Bearbeta dokument",
          labelFinished: "Bearbetat dokument",
          desc: "Genererar beskrivningar för alla uppladdade filer.",
        },
        step2: {
          label: "Genererar sektionslista",
          labelNeutral: "Generera sektionslista",
          labelFinished: "Genererat sektionslista",
          desc: "Skapar en strukturerad lista över sektioner från dokumentsammanfattningar.",
        },
        step3: {
          label: "Slutför dokumentkoppling",
          labelNeutral: "Slutföra dokumentkoppling",
          labelFinished: "Slutfört dokumentkoppling",
          desc: "Bekräftar dokumentens relevans för varje planerad sektion.",
        },
        step4: {
          label: "Identifierar juridiska frågor",
          labelNeutral: "Identifiera juridiska frågor",
          labelFinished: "Identifierat juridiska frågor",
          desc: "Extraherar centrala juridiska frågor för varje sektion.",
        },
        step5: {
          label: "Genererar juridiska PM",
          labelNeutral: "Generera juridiska PM",
          labelFinished: "Genererat juridiska PM",
          desc: "Skapar juridiska promemorior för de identifierade frågorna.",
        },
        step6: {
          label: "Utformar sektioner",
          labelNeutral: "Utforma sektioner",
          labelFinished: "Utformat sektioner",
          desc: "Komponerar innehållet för varje enskild sektion.",
        },
        step7: {
          label: "Kombinerar & slutför dokument",
          labelNeutral: "Kombinera & slutföra dokument",
          labelFinished: "Kombinerat & slutfört dokument",
          desc: "Sammanställer alla sektioner till det slutliga juridiska dokumentet.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Bearbetar referensfiler",
          labelNeutral: "Bearbeta referensfiler",
          labelFinished: "Bearbetat referensfiler",
          desc: "Bearbetar referensfiler",
        },
        step2: {
          label: "Bearbetar granskningsfiler",
          labelNeutral: "Bearbeta granskningsfiler",
          labelFinished: "Bearbetat granskningsfiler",
          desc: "Bearbetar granskningsfiler",
        },
        step3: {
          label: "Genererar sektionslista",
          labelNeutral: "Generera sektionslista",
          labelFinished: "Genererat sektionslista",
          desc: "Genererar sektionslista",
        },
        step4: {
          label: "Utformar sektioner",
          labelNeutral: "Utforma sektioner",
          labelFinished: "Utformat sektioner",
          desc: "Utformar sektioner",
        },
        step5: {
          label: "Genererar rapport",
          labelNeutral: "Generera rapport",
          labelFinished: "Genererat rapport",
          desc: "Genererar rapport",
        },
      },
      documentDrafting: {
        step1: {
          label: "Förbereder dokument",
          labelNeutral: "Förbereda dokument",
          labelFinished: "Förberett dokument",
          desc: "Samlar och förbereder alla relevanta dokument för utkastprocessen.",
        },
        step2: {
          label: "Analyserar innehåll",
          labelNeutral: "Analysera innehåll",
          labelFinished: "Analyserat innehåll",
          desc: "Analyserar dokumentinnehåll och identifierar viktiga juridiska frågor och klausuler.",
        },
        step3: {
          label: "Genererar utkast",
          labelNeutral: "Generera utkast",
          labelFinished: "Genererat utkast",
          desc: "Komponerar det slutliga dokumentutkastet baserat på analysen.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Dokumentutkast",
  },
  cdbProgress: {
    title: "Komplex dokumentbyggare",
    general: {
      placeholderSubTask: "Bearbeta objekt {{index}}...",
      placeholderSubTaskOngoing: "Bearbetar objekt {{index}}...",
    },
    subTasks: {
      mappingSections: "Kopplar sektioner för: {{filename}}...",
      mappingSectionsNeutral: "Koppla sektioner för: {{filename}}...",
      mappingSectionsFinished: "Kopplat sektioner för: {{filename}}",
      identifyingIssues:
        "Identifierar frågor för Sektion {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesNeutral:
        "Identifiera frågor för Sektion {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesFinished:
        "Identifierat frågor för Sektion {{sectionNumber}}: {{sectionTitle}}",
      processingDocument: "Bearbetar: {{filename}} - {{action}}...",
      processingDocumentNeutral: "Bearbeta: {{filename}} - {{action}}...",
      processingDocumentFinished: "Bearbetat: {{filename}} - {{action}}",
      generatingMemo: "Genererar PM för: {{issueText}}...",
      generatingMemoNeutral: "Generera PM för: {{issueText}}...",
      generatingMemoFinished: "Genererat PM för: {{issueText}}",
      resolvingWorkspace: "Löser arbetsområde för: {{issueText}}...",
      resolvingWorkspaceNeutral: "Lös arbetsområde för: {{issueText}}...",
      resolvingWorkspaceFinished: "Löst arbetsområde för: {{issueText}}",
      draftingSection: "Skapar Sektion {{sectionNumber}}: {{sectionTitle}}...",
      draftingSectionNeutral:
        "Skapa Sektion {{sectionNumber}}: {{sectionTitle}}...",
      draftingSectionFinished:
        "Skapade Sektion {{sectionNumber}}: {{sectionTitle}}",
      memoFor: "PM för: {{issueText}} (ao: {{workspaceSlug}})",
      memoForNeutral: "Generera PM för: {{issueText}} (ao: {{workspaceSlug}})",
      memoForFinished:
        "Genererat PM för: {{issueText}} (ao: {{workspaceSlug}})",
      errorNoWorkspace: "Fel - Inget arbetsområde för: {{issueText}}...",
      errorDraftingSection:
        "Fel vid skapande av Sektion {{sectionNumber}}: {{sectionTitle}}",
      actions: {
        generatingDescription: "Genererar beskrivning",
        checkingRelevance: "Kontrollerar relevans",
        generatingSectionList: "Genererar sektionslista",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Bekräfta avbrott",
    confirm_abort_description: "Är du säker på att du vill avbryta processen?",
    keep_running: "Fortsätt köra",
    abort_run: "Avbryt process",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Svarsgenereringens förlopp",
    description:
      "Visar framdriften för uppgifter för att slutföra prompten, utifrån länkning till andra arbetsområden och filstorlekar. Fönstret stängs automatiskt när alla steg är klara.",
    step_fetching_memos: "Hämtar juridisk data om aktuella ämnen",
    step_processing_chunks: "Bearbetar uppladdade dokument",
    step_combining_responses: "Slutför svar",
    sub_step_chunk_label: "Bearbetar dokumentgrupp {{index}}",
    sub_step_chunk_label_neutral: "Bearbeta dokumentgrupp {{index}}",
    sub_step_chunk_label_finished: "Bearbetat dokumentgrupp {{index}}",
    sub_step_memo_label: "Hämtade juridisk data från {{workspaceSlug}}",
    sub_step_memo_label_neutral: "Hämta juridisk data från {{workspaceSlug}}",
    sub_step_memo_label_finished: "Hämtat juridisk data från {{workspaceSlug}}",
    placeholder_sub_task: "Köad deluppgift",
    desc_fetching_memos:
      "Hämtar relevant juridisk information från länkade arbetsområden",
    desc_processing_chunks:
      "Analyserar och extraherar information från dokumentgrupper",
    desc_combining_responses:
      "Sammanställer information till ett omfattande svar",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Juridisk uppgift körs i bakgrunden.",
    dd: "Dokumentutkast fortsätter i bakgrunden.",
    reopen: "Öppna statusfönster",
  },
};
