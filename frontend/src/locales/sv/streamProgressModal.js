export default {
  // =========================
  // CDB PROGRESS
  // =========================
  cdbProgress: {
    "close-msg": "Är du säker på att du vill avbryta processen?",
    general: {
      placeholderSubTask: "Bearbetar objekt {{index}}...",
    },
    main: {
      step1: {
        label: "Generera lista över sektioner",
        desc: "Använder huvuddokumentet för att skapa en initial struktur.",
      },
      step2: {
        label: "Bearbeta dokument",
        desc: "Genererar beskrivningar och kontrollerar relevans.",
      },
      step3: {
        label: "Mappa dokument till sektioner",
        desc: "Tilldelar relevanta dokument till varje sektion.",
      },
      step4: {
        label: "Identifiera juridiska frågor",
        desc: "Extraherar viktiga juridiska frågor för varje sektion.",
      },
      step5: {
        label: "Generera juridiska PM",
        desc: "Skapar juridiska PM för de identifierade frågorna.",
      },
      step6: {
        label: "Skriva sektioner",
        desc: "Skriver innehållet för varje enskild sektion.",
      },
      step7: {
        label: "Sammanfoga och slutföra dokument",
        desc: "Sammansätter sektionerna till det slutliga dokumentet.",
      },
    },
    noMain: {
      step1: {
        label: "Bearbeta dokument",
        desc: "Genererar beskrivningar för alla uppladdade filer.",
      },
      step2: {
        label: "Generera lista över sektioner",
        desc: "Skapar en strukturerad lista över sektioner från dokumentsammanfattningar.",
      },
      step3: {
        label: "Slutföra dokumentmappning",
        desc: "Bekräftar dokumentrelevans för varje planerad sektion.",
      },
      step4: {
        label: "Identifiera juridiska frågor",
        desc: "Extraherar viktiga juridiska frågor för varje sektion.",
      },
      step5: {
        label: "Generera juridiska PM",
        desc: "Skapar juridiska PM för de identifierade frågorna.",
      },
      step6: {
        label: "Skriva sektioner",
        desc: "Skriver innehållet för varje enskild sektion.",
      },
      step7: {
        label: "Sammanfoga och slutföra dokument",
        desc: "Sammansätter alla sektioner till det slutliga juridiska dokumentet.",
      },
    },
    referenceFiles: {
      step1: {
        label: "Bearbetar referensfiler",
        desc: "Bearbetar referensfiler",
      },
      step2: {
        label: "Bearbetar granskningsfiler",
        desc: "Bearbetar granskningsfiler",
      },
      step3: {
        label: "Generera sektionslista",
        desc: "Generera sektionslista",
      },
      step4: {
        label: "Skriva sektioner",
        desc: "Skriva sektioner",
      },
      step5: {
        label: "Generera rapport",
        desc: "Generera rapport",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Bekräfta avbrott",
    confirm_abort_description: "Är du säker på att du vill avbryta processen?",
    keep_running: "Fortsätt köra",
    abort_run: "Avbryt process",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================
  streamdd_progress_modal: {
    title: "Framsteg för svarsgenerering",
    description:
      "Visar det verkliga framstegen av uppgifter för att slutföra skapandet av dokument, beroende på länkning till andra arbetsytor och storleken på filerna. Modalen stängs automatiskt när alla steg är slutförda.",
    step_fetching_memos: "Hämtar juridisk data om aktuella ämnen",
    step_processing_chunks: "Bearbetar uppladdade dokument",
    step_combining_responses: "Slutför svar",
    sub_step_chunk_label: "Bearbetar dokumentgrupp {{index}}",
    sub_step_memo_label: "Juridisk data hämtad från {{workspaceSlug}}",
    placeholder_sub_task: "Väntar på steg",
    desc_fetching_memos:
      "Hämtar relevant juridisk information från länkade arbetsytor",
    desc_processing_chunks:
      "Analyserar och extraherar information från dokumentgrupper",
    desc_combining_responses: "Kombinerar information till ett omfattande svar",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Juridisk uppgift körs i bakgrunden.",
    dd: "Dokumentskapande fortsätter i bakgrunden.",
    reopen: "Öppna statusfönster",
  },
};
