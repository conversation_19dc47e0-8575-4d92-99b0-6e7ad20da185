export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Verarbeitung",
    processing: "Verarbeitung...",
    step: "Schritt",
    timeLeft: "Verbleibende Zeit",
    details: "Details",
    abort: "Abbrechen",
    modalTitle: "Fortschrittsdetails",
    "close-msg": "Sind Sie sicher, dass Sie den Vorgang abbrechen möchten?",
    noThreadSelected: "Kein Thread ausgewählt",
    noActiveProgress: "Kein aktiver Fortschritt",
    of: "von",
    started: "Gestartet",
    failed: "Fehlgeschlagen",
    error: {
      title: "Bei der Bearbeitung Ihrer Anfrage ist ein Fehler aufgetreten.",
      description:
        "Bitte versuchen Sie es erneut oder wenden Sie sich an den Support, wenn das Problem weiterhin besteht.",
      retry: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      dismiss: "Schl<PERSON>ßen",
      showDetails: "Technische Details anzeigen",
      hideDetails: "Technische Details ausblenden",
    },
    cancelled: "Der Prozess wurde abgebrochen.",
    completed: "Prozess abgeschlossen",
    completedDescription: "Dokument wurde erstellt und zum Chat hinzugefügt",
    types: {
      main: {
        step1: {
          label: "Generierung der Abschnittsliste",
          labelNeutral: "Abschnittsliste generieren",
          labelFinished: "Abschnittsliste generiert",
          desc: "Verwendung des Hauptdokuments zur Erstellung einer ersten Struktur.",
        },
        step2: {
          label: "Verarbeitung der Dokumente",
          labelNeutral: "Dokumente verarbeiten",
          labelFinished: "Dokumente verarbeitet",
          desc: "Beschreibungen generieren und Relevanz prüfen.",
        },
        step3: {
          label: "Zuordnung der Dokumente zu Abschnitten",
          labelNeutral: "Dokumente zu Abschnitten zuordnen",
          labelFinished: "Dokumente zu Abschnitten zugeordnet",
          desc: "Relevante Dokumente jedem Abschnitt zuweisen.",
        },
        step4: {
          label: "Identifizierung rechtlicher Probleme",
          labelNeutral: "Rechtliche Probleme identifizieren",
          labelFinished: "Rechtliche Probleme identifiziert",
          desc: "Wichtige rechtliche Probleme für jeden Abschnitt extrahieren.",
        },
        step5: {
          label: "Generierung rechtlicher Memos",
          labelNeutral: "Rechtliche Memos generieren",
          labelFinished: "Rechtliche Memos generiert",
          desc: "Rechtliche Memoranden für die identifizierten Probleme erstellen.",
        },
        step6: {
          label: "Verfassung der Abschnitte",
          labelNeutral: "Abschnitte verfassen",
          labelFinished: "Abschnitte verfasst",
          desc: "Inhalt für jeden einzelnen Abschnitt erstellen.",
        },
        step7: {
          label: "Kombinierung & Finalisierung des Dokuments",
          labelNeutral: "Dokument kombinieren & finalisieren",
          labelFinished: "Dokument kombiniert & finalisiert",
          desc: "Abschnitte zum finalen Dokument zusammenfügen.",
        },
      },
      noMain: {
        step1: {
          label: "Verarbeitung der Dokumente",
          labelNeutral: "Dokumente verarbeiten",
          labelFinished: "Dokumente verarbeitet",
          desc: "Beschreibungen für alle hochgeladenen Dateien generieren.",
        },
        step2: {
          label: "Generierung der Abschnittsliste",
          labelNeutral: "Abschnittsliste generieren",
          labelFinished: "Abschnittsliste generiert",
          desc: "Strukturierte Abschnittsliste aus Dokumentzusammenfassungen erstellen.",
        },
        step3: {
          label: "Finalisierung der Dokumentzuordnung",
          labelNeutral: "Dokumentzuordnung finalisieren",
          labelFinished: "Dokumentzuordnung finalisiert",
          desc: "Relevanz der Dokumente für jeden geplanten Abschnitt bestätigen.",
        },
        step4: {
          label: "Identifizierung rechtlicher Probleme",
          labelNeutral: "Rechtliche Probleme identifizieren",
          labelFinished: "Rechtliche Probleme identifiziert",
          desc: "Wichtige rechtliche Probleme für jeden Abschnitt extrahieren.",
        },
        step5: {
          label: "Generierung rechtlicher Memos",
          labelNeutral: "Rechtliche Memos generieren",
          labelFinished: "Rechtliche Memos generiert",
          desc: "Rechtliche Memoranden für die identifizierten Probleme erstellen.",
        },
        step6: {
          label: "Verfassung der Abschnitte",
          labelNeutral: "Abschnitte verfassen",
          labelFinished: "Abschnitte verfasst",
          desc: "Inhalt für jeden einzelnen Abschnitt erstellen.",
        },
        step7: {
          label: "Kombinierung & Finalisierung des Dokuments",
          labelNeutral: "Dokument kombinieren & finalisieren",
          labelFinished: "Dokument kombiniert & finalisiert",
          desc: "Alle Abschnitte zum finalen rechtlichen Dokument zusammenfügen.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Verarbeitung von Referenzdateien",
          labelNeutral: "Referenzdateien verarbeiten",
          labelFinished: "Referenzdateien verarbeitet",
          desc: "Verarbeitung von Referenzdateien",
        },
        step2: {
          label: "Verarbeitung von Prüfdateien",
          labelNeutral: "Prüfdateien verarbeiten",
          labelFinished: "Prüfdateien verarbeitet",
          desc: "Verarbeitung von Prüfdateien",
        },
        step3: {
          label: "Generierung der Abschnittsliste",
          labelNeutral: "Abschnittsliste generieren",
          labelFinished: "Abschnittsliste generiert",
          desc: "Abschnittsliste generieren",
        },
        step4: {
          label: "Entwurf der Abschnitte",
          labelNeutral: "Abschnitte entwerfen",
          labelFinished: "Abschnitte entworfen",
          desc: "Abschnitte entwerfen",
        },
        step5: {
          label: "Generierung des Berichts",
          labelNeutral: "Bericht generieren",
          labelFinished: "Bericht generiert",
          desc: "Bericht generieren",
        },
      },
      documentDrafting: {
        step1: {
          label: "Vorbereitung der Dokumente",
          labelNeutral: "Dokumente vorbereiten",
          labelFinished: "Dokumente vorbereitet",
          desc: "Sammlung und Vorbereitung aller relevanten Dokumente für den Entwurfsprozess.",
        },
        step2: {
          label: "Analyse des Inhalts",
          labelNeutral: "Inhalt analysieren",
          labelFinished: "Inhalt analysiert",
          desc: "Analyse des Dokumentinhalts und Identifizierung wichtiger rechtlicher Probleme und Klauseln.",
        },
        step3: {
          label: "Generierung des Entwurfs",
          labelNeutral: "Entwurf generieren",
          labelFinished: "Entwurf generiert",
          desc: "Erstellung des finalen Dokumententwurfs basierend auf der Analyse.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Dokumenterstellung",
  },
  cdbProgress: {
    title: "Komplexer Dokumentenersteller",
    general: {
      placeholderSubTask: "Element {{index}} bearbeiten...",
      placeholderSubTaskOngoing: "Element {{index}} wird bearbeitet...",
    },
    subTasks: {
      mappingSections: "Zuordnung von Abschnitten für: {{filename}}...",
      mappingSectionsNeutral: "Abschnitte zuordnen für: {{filename}}...",
      mappingSectionsFinished: "Abschnitte zugeordnet für: {{filename}}",
      identifyingIssues:
        "Identifizierung von Problemen für Abschnitt {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesNeutral:
        "Probleme identifizieren für Abschnitt {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesFinished:
        "Probleme identifiziert für Abschnitt {{sectionNumber}}: {{sectionTitle}}",
      processingDocument: "Verarbeitung: {{filename}} - {{action}}...",
      processingDocumentNeutral: "Verarbeiten: {{filename}} - {{action}}...",
      processingDocumentFinished: "Verarbeitet: {{filename}} - {{action}}",
      generatingMemo: "Generierung des Memos für: {{issueText}}...",
      generatingMemoNeutral: "Memo generieren für: {{issueText}}...",
      generatingMemoFinished: "Memo generiert für: {{issueText}}",
      resolvingWorkspace: "Arbeitsbereich auflösen für: {{issueText}}...",
      resolvingWorkspaceNeutral:
        "Arbeitsbereich auflösen für: {{issueText}}...",
      resolvingWorkspaceFinished: "Arbeitsbereich aufgelöst für: {{issueText}}",
      draftingSection:
        "Abschnitt {{sectionNumber}} entwerfen: {{sectionTitle}}...",
      draftingSectionNeutral:
        "Abschnitt {{sectionNumber}} entwerfen: {{sectionTitle}}...",
      draftingSectionFinished:
        "Abschnitt {{sectionNumber}} entworfen: {{sectionTitle}}",
      memoFor: "Memo für: {{issueText}} (ab: {{workspaceSlug}})",
      memoForNeutral:
        "Memo generieren für: {{issueText}} (ab: {{workspaceSlug}})",
      memoForFinished:
        "Memo generiert für: {{issueText}} (ab: {{workspaceSlug}})",
      errorNoWorkspace: "Fehler - Kein Arbeitsbereich für: {{issueText}}...",
      errorDraftingSection:
        "Fehler beim Erstellen von Abschnitt {{sectionNumber}}: {{sectionTitle}}",
      actions: {
        generatingDescription: "Beschreibung generieren",
        checkingRelevance: "Relevanz prüfen",
        generatingSectionList: "Abschnittsliste generieren",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Abbruch bestätigen",
    confirm_abort_description:
      "Sind Sie sicher, dass Sie den Vorgang abbrechen möchten?",
    keep_running: "Weiter ausführen",
    abort_run: "Vorgang abbrechen",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Fortschritt der Antwortgenerierung",
    description:
      "Zeigt den Echtzeitfortschritt der Aufgaben zur Fertigstellung der Eingabeaufforderung an, abhängig von der Verknüpfung mit anderen Arbeitsbereichen und der Größe der Dateien. Das Modal schließt sich automatisch, sobald alle Schritte abgeschlossen sind.",
    step_fetching_memos: "Abrufen von Rechtsdaten zu aktuellen Themen",
    step_processing_chunks: "Verarbeitung hochgeladener Dokumente",
    step_combining_responses: "Antwort finalisieren",
    sub_step_chunk_label: "Verarbeitung der Dokumentengruppe {{index}}",
    sub_step_chunk_label_neutral: "Dokumentengruppe {{index}} verarbeiten",
    sub_step_chunk_label_finished: "Dokumentengruppe {{index}} verarbeitet",
    sub_step_memo_label: "Rechtsdaten von {{workspaceSlug}} abgerufen",
    sub_step_memo_label_neutral: "Rechtsdaten von {{workspaceSlug}} abrufen",
    sub_step_memo_label_finished: "Rechtsdaten von {{workspaceSlug}} abgerufen",
    placeholder_sub_task: "Warteschlangen-Unteraufgabe",
    desc_fetching_memos:
      "Abrufen relevanter Rechtsinformationen aus verknüpften Arbeitsbereichen",
    desc_processing_chunks:
      "Analyse und Extraktion von Informationen aus Dokumentengruppen",
    desc_combining_responses:
      "Synthese von Informationen zu einer umfassenden Antwort",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Rechtliche Aufgabe läuft im Hintergrund.",
    dd: "Dokumenterstellung läuft im Hintergrund weiter.",
    reopen: "Statusfenster öffnen",
  },
};
