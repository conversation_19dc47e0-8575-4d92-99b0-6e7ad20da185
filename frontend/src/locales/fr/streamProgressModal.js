export default {
  // =========================
  // CDB PROGRESS
  // =========================
  cdbProgress: {
    "close-msg": "Êtes-vous sûr de vouloir annuler le processus ?",
    general: {
      placeholderSubTask: "Traitement de l'élément {{index}}...",
    },
    main: {
      step1: {
        label: "Générer la liste des sections",
        desc: "Utilisation du document principal pour créer une structure initiale.",
      },
      step2: {
        label: "Traiter les documents",
        desc: "Génération des descriptions et vérification de la pertinence.",
      },
      step3: {
        label: "Associer les documents aux sections",
        desc: "Attribution de documents pertinents à chaque section.",
      },
      step4: {
        label: "Identifier les questions juridiques",
        desc: "Extraction des questions juridiques importantes pour chaque section.",
      },
      step5: {
        label: "Générer les mémorandums juridiques",
        desc: "Création de mémorandums juridiques pour les questions identifiées.",
      },
      step6: {
        label: "Rédiger les sections",
        desc: "Rédaction du contenu pour chaque section individuelle.",
      },
      step7: {
        label: "Fusionner et finaliser le document",
        desc: "Assemblage des sections dans le document final.",
      },
    },
    noMain: {
      step1: {
        label: "Traiter les documents",
        desc: "Génération des descriptions pour tous les fichiers téléchargés.",
      },
      step2: {
        label: "Générer la liste des sections",
        desc: "Création d'une liste structurée de sections à partir des résumés de documents.",
      },
      step3: {
        label: "Finaliser l'association des documents",
        desc: "Confirmation de la pertinence des documents pour chaque section planifiée.",
      },
      step4: {
        label: "Identifier les questions juridiques",
        desc: "Extraction des questions juridiques importantes pour chaque section.",
      },
      step5: {
        label: "Générer les mémorandums juridiques",
        desc: "Création de mémorandums juridiques pour les questions identifiées.",
      },
      step6: {
        label: "Rédiger les sections",
        desc: "Rédaction du contenu pour chaque section individuelle.",
      },
      step7: {
        label: "Fusionner et finaliser le document",
        desc: "Assemblage de toutes les sections dans le document juridique final.",
      },
    },
    referenceFiles: {
      step1: {
        label: "Traitement des fichiers de référence",
        desc: "Traitement des fichiers de référence",
      },
      step2: {
        label: "Traitement des fichiers de révision",
        desc: "Traitement des fichiers de révision",
      },
      step3: {
        label: "Générer la liste des sections",
        desc: "Générer la liste des sections",
      },
      step4: {
        label: "Rédiger les sections",
        desc: "Rédiger les sections",
      },
      step5: {
        label: "Générer le rapport",
        desc: "Générer le rapport",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Confirmer l'abandon",
    confirm_abort_description:
      "Êtes-vous sûr de vouloir abandonner le processus ?",
    keep_running: "Continuer l'exécution",
    abort_run: "Abandonner le processus",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================
  streamdd_progress_modal: {
    title: "Progression de la génération de réponse",
    description:
      "Affiche le progrès réel des tâches pour compléter la création de documents, en fonction de la liaison à d'autres espaces de travail et de la taille des fichiers. Le modal se ferme automatiquement lorsque toutes les étapes sont terminées.",
    step_fetching_memos:
      "Récupération des données juridiques sur les sujets actuels",
    step_processing_chunks: "Traitement des documents téléchargés",
    step_combining_responses: "Finalisation de la réponse",
    sub_step_chunk_label: "Traitement du groupe de documents {{index}}",
    sub_step_memo_label: "Données juridiques récupérées de {{workspaceSlug}}",
    placeholder_sub_task: "Étape en attente",
    desc_fetching_memos:
      "Récupération d'informations juridiques pertinentes à partir d'espaces de travail liés",
    desc_processing_chunks:
      "Analyse et extraction d'informations à partir de groupes de documents",
    desc_combining_responses:
      "Combinaison d'informations en une réponse complète",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Tâche juridique en cours d'exécution en arrière-plan.",
    dd: "La création de document continue en arrière-plan.",
    reopen: "Ouvrir la fenêtre de statut",
  },
};
