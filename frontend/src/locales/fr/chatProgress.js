export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Traitement",
    processing: "Traitement...",
    step: "Étape",
    timeLeft: "Temps restant",
    details: "Détails",
    abort: "Abandonner",
    modalTitle: "Détails du progrès",
    "close-msg": "Êtes-vous sûr de vouloir annuler le processus ?",
    noThreadSelected: "Aucun fil sélectionné",
    noActiveProgress: "Aucun progrès actif",
    of: "de",
    started: "Commencé",
    failed: "Échoué",
    error: {
      title: "Une erreur s'est produite lors du traitement de votre demande.",
      description:
        "Veuillez réessayer ou contacter le support si le problème persiste.",
      retry: "<PERSON><PERSON><PERSON><PERSON>",
      dismiss: "<PERSON><PERSON><PERSON>",
      showDetails: "Afficher les détails techniques",
      hideDetails: "Masquer les détails techniques",
    },
    cancelled: "Le processus a été annulé.",
    completed: "Processus terminé",
    completedDescription: "Le document a été généré et ajouté au chat",
    types: {
      main: {
        step1: {
          label: "Génération de la liste des sections",
          labelNeutral: "Générer la liste des sections",
          labelFinished: "Liste des sections générée",
          desc: "Utilisation du document principal pour créer une structure initiale.",
        },
        step2: {
          label: "Traitement des documents",
          labelNeutral: "Traiter les documents",
          labelFinished: "Documents traités",
          desc: "Génération des descriptions et vérification de la pertinence.",
        },
        step3: {
          label: "Mappage des documents aux sections",
          labelNeutral: "Mapper les documents aux sections",
          labelFinished: "Documents mappés aux sections",
          desc: "Attribution des documents pertinents à chaque section.",
        },
        step4: {
          label: "Identification des problèmes juridiques",
          labelNeutral: "Identifier les problèmes juridiques",
          labelFinished: "Problèmes juridiques identifiés",
          desc: "Extraction des problèmes juridiques clés pour chaque section.",
        },
        step5: {
          label: "Génération des mémos juridiques",
          labelNeutral: "Générer les mémos juridiques",
          labelFinished: "Mémos juridiques générés",
          desc: "Création de mémorandums juridiques pour les problèmes identifiés.",
        },
        step6: {
          label: "Rédaction des sections",
          labelNeutral: "Rédiger les sections",
          labelFinished: "Sections rédigées",
          desc: "Composition du contenu pour chaque section individuelle.",
        },
        step7: {
          label: "Combinaison & finalisation du document",
          labelNeutral: "Combiner & finaliser le document",
          labelFinished: "Document combiné & finalisé",
          desc: "Assemblage de toutes les sections dans le document juridique final.",
        },
      },
      noMain: {
        step1: {
          label: "Traitement des documents",
          labelNeutral: "Traiter les documents",
          labelFinished: "Documents traités",
          desc: "Génération des descriptions pour tous les fichiers téléchargés.",
        },
        step2: {
          label: "Génération de la liste des sections",
          labelNeutral: "Générer la liste des sections",
          labelFinished: "Liste des sections générée",
          desc: "Création d'une liste structurée de sections à partir des résumés de documents.",
        },
        step3: {
          label: "Finalisation du mappage des documents",
          labelNeutral: "Finaliser le mappage des documents",
          labelFinished: "Mappage des documents finalisé",
          desc: "Confirmation de la pertinence des documents pour chaque section planifiée.",
        },
        step4: {
          label: "Identification des problèmes juridiques",
          labelNeutral: "Identifier les problèmes juridiques",
          labelFinished: "Problèmes juridiques identifiés",
          desc: "Extraction des problèmes juridiques clés pour chaque section.",
        },
        step5: {
          label: "Génération des mémos juridiques",
          labelNeutral: "Générer les mémos juridiques",
          labelFinished: "Mémos juridiques générés",
          desc: "Création de mémorandums juridiques pour les problèmes identifiés.",
        },
        step6: {
          label: "Rédaction des sections",
          labelNeutral: "Rédiger les sections",
          labelFinished: "Sections rédigées",
          desc: "Composition du contenu pour chaque section individuelle.",
        },
        step7: {
          label: "Combinaison & finalisation du document",
          labelNeutral: "Combiner & finaliser le document",
          labelFinished: "Document combiné & finalisé",
          desc: "Assemblage de toutes les sections dans le document juridique final.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Traitement des fichiers de référence",
          labelNeutral: "Traiter les fichiers de référence",
          labelFinished: "Fichiers de référence traités",
          desc: "Traitement des fichiers de référence",
        },
        step2: {
          label: "Traitement des fichiers de révision",
          labelNeutral: "Traiter les fichiers de révision",
          labelFinished: "Fichiers de révision traités",
          desc: "Traitement des fichiers de révision",
        },
        step3: {
          label: "Génération de la liste des sections",
          labelNeutral: "Générer la liste des sections",
          labelFinished: "Liste des sections générée",
          desc: "Génération de la liste des sections",
        },
        step4: {
          label: "Rédaction des sections",
          labelNeutral: "Rédiger les sections",
          labelFinished: "Sections rédigées",
          desc: "Rédaction des sections",
        },
        step5: {
          label: "Génération du rapport",
          labelNeutral: "Générer le rapport",
          labelFinished: "Rapport généré",
          desc: "Génération du rapport",
        },
      },
      documentDrafting: {
        step1: {
          label: "Préparation des documents",
          labelNeutral: "Préparer les documents",
          labelFinished: "Documents préparés",
          desc: "Collecte et préparation de tous les documents pertinents pour le processus de rédaction.",
        },
        step2: {
          label: "Analyse du contenu",
          labelNeutral: "Analyser le contenu",
          labelFinished: "Contenu analysé",
          desc: "Analyse du contenu des documents et identification des problèmes juridiques clés et des clauses.",
        },
        step3: {
          label: "Génération du brouillon",
          labelNeutral: "Générer le brouillon",
          labelFinished: "Brouillon généré",
          desc: "Composition du brouillon final du document basé sur l'analyse.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Rédaction de Document",
  },
  cdbProgress: {
    title: "Générateur de documents complexes",
    general: {
      placeholderSubTask: "Traiter l'élément {{index}}...",
      placeholderSubTaskOngoing: "Traitement de l'élément {{index}}...",
    },
    subTasks: {
      mappingSections: "Mappage des sections pour : {{filename}}...",
      mappingSectionsNeutral: "Mapper les sections pour : {{filename}}...",
      mappingSectionsFinished: "Sections mappées pour : {{filename}}",
      identifyingIssues:
        "Identification des problèmes pour la Section {{sectionNumber}} : {{sectionTitle}}...",
      identifyingIssuesNeutral:
        "Identifier les problèmes pour la Section {{sectionNumber}} : {{sectionTitle}}...",
      identifyingIssuesFinished:
        "Problèmes identifiés pour la Section {{sectionNumber}} : {{sectionTitle}}",
      processingDocument: "Traitement : {{filename}} - {{action}}...",
      processingDocumentNeutral: "Traiter : {{filename}} - {{action}}...",
      processingDocumentFinished: "Traité : {{filename}} - {{action}}",
      generatingMemo: "Génération du mémo pour : {{issueText}}...",
      generatingMemoNeutral: "Générer le mémo pour : {{issueText}}...",
      generatingMemoFinished: "Mémo généré pour : {{issueText}}",
      resolvingWorkspace:
        "Résolution de l'espace de travail pour : {{issueText}}...",
      resolvingWorkspaceNeutral:
        "Résoudre l'espace de travail pour : {{issueText}}...",
      resolvingWorkspaceFinished:
        "Espace de travail résolu pour : {{issueText}}",
      draftingSection:
        "Rédaction de la Section {{sectionNumber}} : {{sectionTitle}}...",
      draftingSectionNeutral:
        "Rédiger la Section {{sectionNumber}} : {{sectionTitle}}...",
      draftingSectionFinished:
        "Section {{sectionNumber}} rédigée : {{sectionTitle}}",
      memoFor: "Mémo pour : {{issueText}} (et : {{workspaceSlug}})",
      memoForNeutral:
        "Générer le mémo pour : {{issueText}} (et : {{workspaceSlug}})",
      memoForFinished:
        "Mémo généré pour : {{issueText}} (et : {{workspaceSlug}})",
      errorNoWorkspace:
        "Erreur - Aucun espace de travail pour : {{issueText}}...",
      errorDraftingSection:
        "Erreur lors de la rédaction de la Section {{sectionNumber}} : {{sectionTitle}}",
      actions: {
        generatingDescription: "Génération de la description",
        checkingRelevance: "Vérification de la pertinence",
        generatingSectionList: "Génération de la liste des sections",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Confirmer l'abandon",
    confirm_abort_description:
      "Êtes-vous sûr de vouloir annuler le processus ?",
    keep_running: "Continuer l'exécution",
    abort_run: "Abandonner le processus",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Progression de la génération de réponse",
    description:
      "Affiche la progression en temps réel des tâches pour terminer l'invite, selon la liaison avec d'autres espaces de travail et la taille des fichiers. La fenêtre se fermera automatiquement une fois toutes les étapes terminées.",
    step_fetching_memos:
      "Récupération des données juridiques sur les sujets actuels",
    step_processing_chunks: "Traitement des documents téléchargés",
    step_combining_responses: "Finaliser la réponse",
    sub_step_chunk_label: "Traitement du groupe de documents {{index}}",
    sub_step_chunk_label_neutral: "Traiter le groupe de documents {{index}}",
    sub_step_chunk_label_finished: "Traité le groupe de documents {{index}}",
    sub_step_memo_label: "Données juridiques récupérées de {{workspaceSlug}}",
    sub_step_memo_label_neutral:
      "Récupérer les données juridiques de {{workspaceSlug}}",
    sub_step_memo_label_finished:
      "Récupéré les données juridiques de {{workspaceSlug}}",
    placeholder_sub_task: "Sous-tâche en file d'attente",
    desc_fetching_memos:
      "Récupération d'informations juridiques pertinentes depuis les espaces de travail liés",
    desc_processing_chunks:
      "Analyse et extraction d'informations depuis les groupes de documents",
    desc_combining_responses:
      "Synthèse des informations en une réponse complète",
  },

  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "La tâche juridique s'exécute en arrière-plan.",
    dd: "La rédaction du document continue en arrière-plan.",
    reopen: "Ouvrir la fenêtre de statut",
  },
};
