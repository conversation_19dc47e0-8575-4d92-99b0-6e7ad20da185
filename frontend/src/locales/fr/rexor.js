export default {
  // ----------------------------
  // Rexor Integration
  // ----------------------------
  rexor: {
    "register-project": "Enregistrer le projet Rexor",
    "project-id": "ID du projet",
    "resource-id": "ID de la ressource",
    "activity-id": "ID de l'activité",
    register: "Enregistrer le projet",
    "invoice-text": "Nombre de recherches Foynet",
    "estimated-manual-time": " * Temps manuel estimé : {{hours}} heures",
    "estimated-manual-time-prefix": " * Temps manuel estimé :",
    registering: "enregistrement en cours...",
    "not-active": "Cette affaire n'est pas active pour l'enregistrement",
    account: {
      title: "Se connecter à Rexor",
      username: "Nom d'utilisateur",
      password: "Mot de passe",
      "no-token": "Aucun token reçu dans handleLoginSuccess",
      logout: "Se déconnecter",
      "no-user": "Veuillez vous connecter d'abord",
      connected: "Connecté à Rexor",
      "not-connected": "Non connecté",
      "change-account": "Changer de compte",
      "session-expired": "Session expirée. Veuillez vous reconnecter.",
    },
    "hide-article-transaction":
      "Masquer le formulaire de transaction d'article",
    "show-article-transaction":
      "Afficher le formulaire de transaction d'article",
    "article-transaction-title": "Ajouter une transaction d'article",
    "registration-date": "Date d'enregistrement",
    description: "Description",
    "description-internal": "Description interne",
    "hours-worked": "Heures travaillées",
    "invoiced-hours": "Heures facturées",
    invoiceable: "Facturable",
    "sending-article-transaction": "Envoi de la transaction d'article...",
    "save-article-transaction": "Sauvegarder la transaction d'article",
    "project-not-register": "Le projet doit d'abord être enregistré.",
    "article-transaction-error":
      "Échec de l'écriture de la transaction d'article",
    "not-exist": "Cette affaire n'a pas pu être trouvée",
    "missing-economy-id-admin":
      "Veuillez ajouter un ID économique à votre profil utilisateur avant d'enregistrer un projet.",
    "missing-economy-id-user":
      "Veuillez demander à l'administrateur système d'ajouter un ID économique à votre profil utilisateur pour pouvoir enregistrer un projet.",
    "api-settings": {
      title: "Configuration de l'API Rexor",
      description:
        "Configurez les endpoints d'API Rexor personnalisés et les identifiants. Laissez vide pour utiliser les valeurs par défaut.",
      "loading-message": "Chargement des paramètres de l'API Rexor...",
      "api-base-url": "URL de base de l'API",
      "api-base-url-placeholder": "https://api.rexor.se/v231/Api",
      "auth-url": "URL d'authentification",
      "auth-url-placeholder": "https://auth.rexor.se/v231/Token",
      "client-id-dev": "ID client de développement",
      "client-id-dev-placeholder": "testfoyen",
      "client-id-prod": "ID client de production",
      "client-id-prod-placeholder": "foyen",
      "api-host": "Hôte de l'API",
      "api-host-placeholder": "api.rexor.se",
      "save-button": "Enregistrer les paramètres",
      "reset-button": "Réinitialiser aux valeurs par défaut",
      "success-message": "Paramètres de l'API Rexor enregistrés avec succès",
      "error-message":
        "Échec de l'enregistrement des paramètres de l'API Rexor",
    },
  },
};
