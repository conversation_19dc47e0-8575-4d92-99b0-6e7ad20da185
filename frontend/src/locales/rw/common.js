const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Ingero",
    "workspaces-name": "<PERSON><PERSON>a ry'umwanya w'akazi",
    ok: "OK",
    error: "ikosa",
    confirm: "Emeza",
    confirmstart: "Emeza hanyuma utangire",
    savesuccess: "Igenamiterere ryabitswe neza",
    saveerror: "Kubika igenamiterere byanze",
    success: "ibyatunganye",
    user: "<PERSON>koresh<PERSON>",
    selection: "Ihitamo ry'icyitegererezo",
    saving: "<PERSON>bi<PERSON>...",
    save: "Kubika impinduka",
    previous: "Paji ibanza",
    next: "Paji ikurikira",
    cancel: "<PERSON>raguza",
    "search-placeholder": "Gushaka...",
    "no-results": "<PERSON>ta bisubizo byabonetse",
    "more-actions": "<PERSON><PERSON><PERSON>",
    "delete-message": "<PERSON><PERSON> ubutumwa",
    copy: "<PERSON><PERSON>",
    edit: "Hindura",
    regenerate: "Ongera ukore",
    "export-word": "Ohereza muri Word",
    "text-editing": "Guhindura inyandiko",
    "stop-generating": "Hagarika gukora",
    "attach-file": "Shyiraho dosiye kuri iyi chat",
    home: "Ahabanza",
    settings: "Igenamiterere",
    support: "Ubufasha",
    "clear-reference": "Siba referanse",
    "send-message": "Ohereza ubutumwa",
    "ask-legal": "Saba amakuru y'amategeko",
    "stop-response": "Hagarika gukora igisubizo",
    "contact-support": "Vugisha Ubufasha",
    "copy-connection": "Kopi umurongo wo guhuza",
    "auto-connect": "Huza byikora ku mugereka",
    back: "Gusubira inyuma",
    "back-to-workspaces": "Gusubira ku mwanya w'akazi",
    off: "Cye",
    on: "Kuri",
    continue: "Gukomeza",
    rename: "Guhindura izina",
    delete: "Gusiba",
    "default-skill": "Uku gushobora kwa mbere kwemewe ntikugomba gucaho.",
    timeframes: "Ibihe",
    other: "Izindi ngingo",
    placeholder: {
      username: "Izina ry'umukoresha",
      password: "Ijambo ry'ibanga",
      email: "Injiza aderesi imeri",
      "support-email": "<EMAIL>",
      website: "https://www.example.com",
      "site-name": "IST Legal",
      "search-llm": "Shakisha utanga serivisi ya LLM wihariye",
      "search-providers": "Shakisha abatanga serivisi",
      "message-heading": "Umutwe w'ubutumwa",
      "message-content": "Ubutumwa",
      "token-limit": "4096",
      "max-tokens": "Umubare ntarengwa wa tokens ku cyifuzo (urugero: 1024)",
      "api-key": "Urufunguzo rwa API",
      "base-url": "URL y'ibanze",
      endpoint: "Icyerekezo cya API",
    },
    tooltip: {
      copy: "Koporora kuri clipboard",
      delete: "Siba iki kintu",
      edit: "Hindura iki kintu",
      save: "Bika impinduka",
      cancel: "Hagarika impinduka",
      search: "Shakisha ibintu",
      add: "Ongeraho ikintu gishya",
      remove: "Kuraho ikintu",
      upload: "Ohereza dosiye",
      download: "Kuramo dosiye",
      refresh: "Vugurura amakuru",
      settings: "Fungura igenamiterere",
      more: "Ibindi bikorwa",
    },
    "default.message": "Injiza ubutumwa bwawe hano",
    preview: "Foto",
    prompt: "Prompt",
    loading: "Kuvuga...",
    download: "Kuramo mu buryo bwibanze",
    open_in_new_tab: "Fungura mu icyiciro gishya hamwe n'imiterere",
    close: "Kuramo",
    done: "Byarangiye",
    note: "Icyitonderwa",
    clearing: "Gusiba...",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Urabyizeye ko ushaka gusiba {{name}}?\nNyuma y'ibi, ntabwo izaboneka muri iyi porogaramu.\n\nIbi ntibishobora guhindurwa.",
  deleteConfirmation:
    "Urabyizeye ko ushaka gusiba ${user.username}?\nNyuma y'ibi, bazasohoka muri konti yabo kandi ntibazashobora kongera gukoresha iyi porogaramu.\n\nIbi ntibishobora guhindurwa.",
  suspendConfirmation:
    "Urabyizeye ko ushaka guhagarika {{username}}?\nNyuma y'ibi, bazasohoka muri konti yabo kandi ntibazashobora kongera kwinjira muri iyi porogaramu kugeza igihe umuyobozi azaba abemerera.\n\nIbi ntibishobora guhindurwa.",
  flushVectorCachesWorkspaceConfirmation:
    "Ese uri wemeza ko ushaka gusiba ububiko bw'igihe gito bwa vekiteri kuri iyi mbuga y'akazi?",
  apiKeys: {
    "deactivate-title": "Hagarika urufunguzo rwa API",
    "deactivate-message":
      "Urabyizeye ko ushaka guhagarika uru rufunguzo rwa API?\nNyuma y'ibi, ntiruzongera gukoreshwa.\n\nIbi ntibishobora guhindurwa.",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Siba",
    edit: "Hindura",
    suspend: "Hagarika",
    unsuspend: "Subizaho",
    save: "Bika",
    accept: "Emeza",
    decline: "Oya",
    ok: "OK",
    "flush-vector-caches": "Siba ububiko bwa vekiteri",
    cancel: "Hagarika",
    saving: "Kubika...",
    save_llm: "Bika ihitamo rya LLM",
    save_template: "Bika imbata",
    "reset-to-default": "Subiramo ku miterere y'ibanze",
    create: "Kurema",
    enable: "Subizaho",
    disable: "Hagarika",
    reset: "Subiramo",
    revoke: "Kuvanaho",
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc":
      "Urabyizeye ko ushaka gusiba izi dosiye n'ububiko?\nIbi bizazikura kuri sisitemu kandi zivanwe mu makuru zose.\nIbi ntibishobora gusubirwamo.",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: { enabled: "byemewe", disabled: "byafunzwe" },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Ntibyakunze kubona modeli zihariye",
    "fetch-models-error": "Ikosa ryo kubona modeli",
    "upgrade-error": "Ikosa mugihe cyo kuzamura",
    "failed-process-file": "Ntibyashobotse gutunganya dosiye: {{text}}",
    "failed-process-attachment": "Ntibyashobotse gutunganya umugereka",
    "failed-extract-content": "Ntibyashobotse gukura ibiri mu {{fileName}}",
    "failed-process-content": "Ntibyashobotse gutunganya ibiri mu dosiye",
    common: { error: "Ikosa" },
    workspace: {
      "already-exists": "Umwanya w'akazi ufite iri zina usanzwe uhari",
    },
    auth: {
      "invalid-credentials": "Amakuru yo kwinjira atari yo.",
      "account-suspended": "Konti yahagaritswe n'umuyobozi.",
      "invalid-password": "Ijambo ry'ibanga ritari ryo ryatanzwe",
    },
    env: {
      "anthropic-key-format":
        "Imiterere y'urufunguzo rwa API ya Anthropic itari yo. Urufunguzo rugomba gutangira na 'sk-ant-'",
      "openai-key-format":
        "Urufunguzo rwa API ya OpenAI rugomba gutangira na 'sk-'",
      "jina-key-format":
        "Urufunguzo rwa API ya Jina rugomba gutangira na 'jina_'",
    },
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Birimo gukuramo ishusho...",
    download: "Kuramo ifoto ya grafike",
  },

  // =========================
  // OPTIONS
  // =========================
  options: { yes: "Yego", no: "Oya" },

  // =========================
  // USER MANAGEMENT
  // =========================
  user: {
    "delete-title": "Siba umukoresha",
    "suspend-title": "Hagarika umukoresha",
    "unsuspend-title": "Subizaho umukoresha",
    suspended: "Umukoresha yahagaritswe neza",
    unsuspended: "Umukoresha yasubizwaho neza",
  },

  // =========================
  // METRICS VISIBILITY
  // =========================
  "metrics.visibility.hover": "Metrics zirahari.",
  "metrics.visibility.available": "Metrics ziboneka.",

  // =========================
  // INVOICE REFERENCE NAVIGATION
  // =========================
  "invoice-reference-navigation": {
    title: "Referanse y'ifatiro ikora",
    message:
      "Ufite referanse y'ifatiro ikora ({{reference}}) kandi ugiye kujya ku {{destinationType}} itandukanye. Ni iki ushaka gukora?",
    "current-reference": "Referanse y'ubu:",
    explanation:
      "Ushobora gusiba referanse ukongere gukomeza cyangwa kubika referanse ukongere gukomeza.",
    "clear-and-continue": "Siba referanse ukongere gukomeza",
    "keep-and-continue": "Bika referanse ukongere gukomeza",
    success: "Referanse y'ifatiro yasibwe neza",
    "destination-types": {
      thread: "urudodo",
      workspace: "icyumba cy'akazi",
      module: "igice",
      location: "ahantu",
    },
  },

  // Months
  "month.1": "Mut.",
  "month.2": "Gas.",
  "month.3": "Wer",
  "month.4": "Mat.",
  "month.5": "Gic.",
  "month.6": "Kam.",
  "month.7": "Nya.",
  "month.8": "Kan.",
  "month.9": "Nze.",
  "month.10": "Ukw.",
  "month.11": "Ugu.",
  "month.12": "Ukub",

  // =========================
  // VERSION DISPLAY
  // =========================
  version: {
    "tooltip-title": "Verisiyo {{version}}",
    title: "Verisiyo {{version}}",
  },

  // =========================
  // STYLE UPLOAD
  // =========================
  "style-upload": {
    "manage-files": "Gucunga amadosiye y'imiterere",
    "manage-files-description":
      "Hano ushobora kohereza amadosiye wanditse kugira ngo ukore imiterere yihariye. Iyo ibi bikora, ibisohoka kuri platiforme bizahura cyane n'imiterere yawe y'umwuga yo kwandika.",
    "file-already-exists": "Dosiye isanzwe ihari",
    "files-uploaded": "Amadosiye yoherejwe neza",
    "remove-file": "Kuraho dosiye",
    "add-more": "Ongeraho amadosiye menshi",
    "clear-all": "Siba byose",
    "no-files": "Nyamuneka ohereza byibuze dosiye imwe",
  },

  // =========================
  // WCAG
  // =========================
  header: {
    account: "Konti",
    "style-alignment": "Guhuriza imisusire",
    "sign-out": "Gusohoka",
    "user-menu": "Ibikubiyemo by'ukoresha",
  },

  // =========================
  // TEXT EDITING
  // =========================
  "text-editing": {
    "button-tooltip": "Hindura ubutumwa hamwe no gutunganya inyandiko nziza",
    "button-label": "Guhindura inyandiko",
    "modal-title": "Muhinduzi w'inyandiko",
    "modal-description":
      "Hindura ibiri mu butumwa bwawe hamwe no gutunganya inyandiko nziza",
    "editor-placeholder": "Tangira guhindura ubutumwa bwawe...",
    "save-success": "Ubutumwa bwavuguruwe neza",
    "save-error": "Byanze guvugurura ubutumwa",
    "error-empty-content": "Ntushobora kubika ibintu byubusa",
    saving: "Kubika...",
  },
};

export default TRANSLATIONS;
