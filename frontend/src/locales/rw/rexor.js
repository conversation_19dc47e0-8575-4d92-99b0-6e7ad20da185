export default {
  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Iyandikishe umushinga wa Rexor",
    "project-id": "ID y'umushinga",
    "resource-id": "ID y'ibikoresho",
    "activity-id": "ID y'igikorwa",
    register: "Iyandikishe umushinga",
    "invoice-text": "Umubare w'ishakisha rya Foynet",
    "estimated-manual-time":
      " * Igihe giteganijwe cyo gukora mu ntoki: {{hours}} amasaha",
    registering: "<PERSON><PERSON><PERSON> kwandikwa...",
    "not-active": "Uyu mwanya nturimo gukora kugirango wandikwe",
    account: {
      title: "Injira muri Rexor",
      username: "Izina ryo kwinjira",
      password: "Ijambo ry'ibanga",
      "no-token": "Nta token yakiriwe muri handleLoginSuccess",
      logout: "So<PERSON>ka",
      "no-user": "Nyamuneka injira mbere",
      connected: "Wahurijwe na Rexor",
      "not-connected": "Ntabwo wahurijwe",
      "change-account": "Hindura konti",
      "session-expired": "Igihe cyarangiye. Nyamuneka ongera winjire.",
    },
    "hide-article-transaction": "Hisha ifishi y'igihe",
    "show-article-transaction": "Erekana ifishi y'igihe",
    "article-transaction-title": "Ongeraho igikorwa cy'igihe",
    "registration-date": "Itariki yo kwandikwa",
    description: "Ibisobanuro",
    "description-internal": "Ibisobanuro by'imbere",
    "hours-worked": "Amasaha yakoze",
    "invoiced-hours": "Amasaha yishyuwe",
    invoiceable: "Bishobora kwishyurwa",
    "sending-article-transaction": "Kohereza igikorwa cy'igihe...",
    "save-article-transaction": "Bika igikorwa cy'igihe",
    "project-not-register": "Umushinga ugomba kwandikwa mbere.",
    "article-transaction-error": "Ntibishobotse kwandika igikorwa cy'igihe",
    "not-exist": "Iki kibazo nticyashoboye kuboneka",
    "missing-economy-id-admin":
      "Nyamuneka ongeraho ID y'ubukungu kuri profil yawe mbere yo kwandikisha umushinga.",
    "missing-economy-id-user":
      "Nyamuneka usabe umuyobozi wa sisitemu kongeraho ID y'ubukungu kuri profil yawe kugirango ushobore kwandikisha umushinga.",
    "api-settings": {
      title: "Rexor API Configuration",
      description:
        "Configure custom Rexor API endpoints and credentials. Leave empty to use default values.",
      "loading-message": "Loading Rexor API settings...",
      "api-base-url": "API Base URL",
      "api-base-url-placeholder": "https://api.rexor.se/v231/Api",
      "auth-url": "Authentication URL",
      "auth-url-placeholder": "https://auth.rexor.se/v231/Token",
      "client-id-dev": "Development Client ID",
      "client-id-dev-placeholder": "testfoyen",
      "client-id-prod": "Production Client ID",
      "client-id-prod-placeholder": "foyen",
      "api-host": "API Host",
      "api-host-placeholder": "api.rexor.se",
      "save-button": "Save Settings",
      "reset-button": "Reset to Defaults",
      "success-message": "Rexor API settings saved successfully",
      "error-message": "Failed to save Rexor API settings",
    },
  },
};
