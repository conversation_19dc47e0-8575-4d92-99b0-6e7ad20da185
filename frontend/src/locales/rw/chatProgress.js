export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Gutunganya",
    processing: "Gutunganya...",
    step: "Intambwe",
    timeLeft: "Igihe gisigaye",
    details: "Amakuru arambuye",
    abort: "Hagarika",
    modalTitle: "Amakuru y'iterambere",
    "close-msg": "Urashaka gukuraho inzira?",
    noThreadSelected: "Nta mukurambere wahiswemo",
    noActiveProgress: "Nta terambere rikora",
    of: "bya",
    started: "By<PERSON>ngiye",
    failed: "Byaraniciye",
    error: {
      title: "Habayeho ikosa mugihe cyo gukora ubusabe bwawe.",
      description:
        "Ongera ugerageze cyangwa uvugane n'ubufasha niba ikibazo giko<PERSON>.",
      retry: "Ongera ugerageze",
      dismiss: "Funga",
      showDetails: "Erekana amakuru y'ubuhanga",
      hideDetails: "Hisha amakuru y'ubuhanga",
    },
    cancelled: "Inzira yahagaritswe.",
    completed: "Inzira yarangiye",
    completedDescription: "Inyandiko yarakozwe kandi yongerwemo mu biganiro",
    types: {
      main: {
        step1: {
          label: "Gukora urutonde rw'ibice",
          labelNeutral: "Kora urutonde rw'ibice",
          labelFinished: "Byakoze urutonde rw'ibice",
          desc: "Gukoresha inyandiko nyamukuru gukora inyubakwa y'ibanze.",
        },
        step2: {
          label: "Gutunganya inyandiko",
          labelNeutral: "Tunganya inyandiko",
          labelFinished: "Byatunganije inyandiko",
          desc: "Gutanga ibisobanuro no kugenzura ubwiyunge.",
        },
        step3: {
          label: "Gushyira inyandiko mu bice",
          labelNeutral: "Shyira inyandiko mu bice",
          labelFinished: "Byashyize inyandiko mu bice",
          desc: "Gutanga inyandiko zifite akamaro kuri buri gice.",
        },
        step4: {
          label: "Kumenya ibibazo by'amategeko",
          labelNeutral: "Menya ibibazo by'amategeko",
          labelFinished: "Byamenye ibibazo by'amategeko",
          desc: "Gukuramo ibibazo by'amategeko by'ingenzi kuri buri gice.",
        },
        step5: {
          label: "Gukora incamake z'amategeko",
          labelNeutral: "Kora incamake z'amategeko",
          labelFinished: "Byakoze incamake z'amategeko",
          desc: "Gukora inyandiko z'amategeko ku bibazo byagaragaye.",
        },
        step6: {
          label: "Kwandika ibice",
          labelNeutral: "Andika ibice",
          labelFinished: "Byanditse ibice",
          desc: "Gukora ibiri mu kigice cyose.",
        },
        step7: {
          label: "Guhuza & kurangiza inyandiko",
          labelNeutral: "Huza & rangiza inyandiko",
          labelFinished: "Byahuje & byarangije inyandiko",
          desc: "Guhuza ibice byose mu nyandiko ya nyuma.",
        },
      },
      noMain: {
        step1: {
          label: "Gutunganya inyandiko",
          labelNeutral: "Tunganya inyandiko",
          labelFinished: "Byatunganije inyandiko",
          desc: "Gutanga ibisobanuro ku madosiye yose azamurwa.",
        },
        step2: {
          label: "Gukora urutonde rw'ibice",
          labelNeutral: "Kora urutonde rw'ibice",
          labelFinished: "Byakoze urutonde rw'ibice",
          desc: "Gukora urutonde rwubatswe rw'ibice ruraturuka ku ncamake z'inyandiko.",
        },
        step3: {
          label: "Kurangiza gushyira inyandiko",
          labelNeutral: "Rangiza gushyira inyandiko",
          labelFinished: "Byarangije gushyira inyandiko",
          desc: "Kwemeza ubwiyunge bw'inyandiko kuri buri gice cyateganijwe.",
        },
        step4: {
          label: "Kumenya ibibazo by'amategeko",
          labelNeutral: "Menya ibibazo by'amategeko",
          labelFinished: "Byamenye ibibazo by'amategeko",
          desc: "Gukuramo ibibazo by'amategeko by'ingenzi kuri buri gice.",
        },
        step5: {
          label: "Gukora incamake z'amategeko",
          labelNeutral: "Kora incamake z'amategeko",
          labelFinished: "Byakoze incamake z'amategeko",
          desc: "Gukora inyandiko z'amategeko ku bibazo byagaragaye.",
        },
        step6: {
          label: "Kwandika ibice",
          labelNeutral: "Andika ibice",
          labelFinished: "Byanditse ibice",
          desc: "Gukora ibiri mu kigice cyose.",
        },
        step7: {
          label: "Guhuza & kurangiza inyandiko",
          labelNeutral: "Huza & rangiza inyandiko",
          labelFinished: "Byahuje & byarangije inyandiko",
          desc: "Guhuza ibice byose mu nyandiko y'amategeko ya nyuma.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Gutunganya amadosiye y'isoko",
          labelNeutral: "Tunganya amadosiye y'isoko",
          labelFinished: "Byatunganije amadosiye y'isoko",
          desc: "Gutunganya amadosiye y'isoko",
        },
        step2: {
          label: "Gutunganya amadosiye y'isuzuma",
          labelNeutral: "Tunganya amadosiye y'isuzuma",
          labelFinished: "Byatunganije amadosiye y'isuzuma",
          desc: "Gutunganya amadosiye y'isuzuma",
        },
        step3: {
          label: "Gukora urutonde rw'ibice",
          labelNeutral: "Kora urutonde rw'ibice",
          labelFinished: "Byakoze urutonde rw'ibice",
          desc: "Gukora urutonde rw'ibice",
        },
        step4: {
          label: "Kwandika ibice",
          labelNeutral: "Andika ibice",
          labelFinished: "Byanditse ibice",
          desc: "Kwandika ibice",
        },
        step5: {
          label: "Gukora raporo",
          labelNeutral: "Kora raporo",
          labelFinished: "Byakoze raporo",
          desc: "Gukora raporo",
        },
      },
      documentDrafting: {
        step1: {
          label: "Gutegura inyandiko",
          labelNeutral: "Tegura inyandiko",
          labelFinished: "Byateguje inyandiko",
          desc: "Gukusanya no gutegura inyandiko zose zifite akamaro mu gihe cyo kwandika.",
        },
        step2: {
          label: "Gusesengura ibiri",
          labelNeutral: "Sesengura ibiri",
          labelFinished: "Byasesenguje ibiri",
          desc: "Gusesengura ibiri mu nyandiko no kumenya ibibazo by'amategeko by'ingenzi n'ingingo.",
        },
        step3: {
          label: "Gukora inyandiko ya mbere",
          labelNeutral: "Kora inyandiko ya mbere",
          labelFinished: "Byakoze inyandiko ya mbere",
          desc: "Gukora inyandiko ya nyuma iri mu rwego rw'inyandiko ukurikije isesengura.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Kwandika Inyandiko",
  },
  cdbProgress: {
    title: "Umwubatsi w'inyandiko zigoye",
    general: {
      placeholderSubTask: "Gutunganya ikintu {{index}}...",
      placeholderSubTaskOngoing: "Gutunganya ikintu {{index}}...",
    },
    subTasks: {
      mappingSections: "Gushyira hamwe ibice bya: {{filename}}...",
      mappingSectionsNeutral: "Gushyira hamwe ibice bya: {{filename}}...",
      mappingSectionsFinished: "Byashyizwe hamwe ibice bya: {{filename}}",
      identifyingIssues:
        "Kumenya ibibazo byo mu gice {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesNeutral:
        "Kumenya ibibazo byo mu gice {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesFinished:
        "Byamenyekanye ibibazo byo mu gice {{sectionNumber}}: {{sectionTitle}}",
      processingDocument: "Gutunganya: {{filename}} - {{action}}...",
      processingDocumentNeutral: "Gutunganya: {{filename}} - {{action}}...",
      processingDocumentFinished: "Byatunganijwe: {{filename}} - {{action}}",
      generatingMemo: "Gukora inyandiko ya: {{issueText}}...",
      generatingMemoNeutral: "Kora inyandiko ya: {{issueText}}...",
      generatingMemoFinished: "Inyandiko yakozwe ya: {{issueText}}",
      resolvingWorkspace: "Gukemura icyicaro cya: {{issueText}}...",
      resolvingWorkspaceNeutral: "Kemura icyicaro cya: {{issueText}}...",
      resolvingWorkspaceFinished: "Icyicaro cyakemuwe cya: {{issueText}}",
      draftingSection: "Gukora Igice {{sectionNumber}}: {{sectionTitle}}...",
      draftingSectionNeutral:
        "Kora Igice {{sectionNumber}}: {{sectionTitle}}...",
      draftingSectionFinished:
        "Byakozwe Igice {{sectionNumber}}: {{sectionTitle}}",
      memoFor: "Inyandiko ya: {{issueText}} (ic: {{workspaceSlug}})",
      memoForNeutral:
        "Kora inyandiko ya: {{issueText}} (ic: {{workspaceSlug}})",
      memoForFinished:
        "Inyandiko yakozwe ya: {{issueText}} (ic: {{workspaceSlug}})",
      errorNoWorkspace: "Ikosa - Nta cyicaro cya: {{issueText}}...",
      errorDraftingSection:
        "Ikosa mu gukora Igice {{sectionNumber}}: {{sectionTitle}}",
      actions: {
        generatingDescription: "Gukora ibisobanuro",
        checkingRelevance: "Kureba niba bifitanye isano",
        generatingSectionList: "Gukora urutonde rw'ibice",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Kwemeza Guhagarika",
    confirm_abort_description: "Urashaka gukuraho inzira?",
    keep_running: "Komeza gukora",
    abort_run: "Hagarika inzira",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Iterambere ry'Ibyavuye",
    description:
      "Yerekana iterambere ry'imirimo mu gihe nyacyo kugira ngo urangize ikibazo, bitewe no guhuza n'andi mahuriro y'akazi n'ubunini bw'amadosiye. Idirishya rizifunga wenyine iyo intambwe zose zarangiye.",
    step_fetching_memos: "Gushaka amakuru y'amategeko ku ngingo z'ubu",
    step_processing_chunks: "Gutunganya inyandiko zoherejwe",
    step_combining_responses: "Kurangiza igisubizo",
    sub_step_chunk_label: "Gutunganya itsinda ry'inyandiko {{index}}",
    sub_step_chunk_label_neutral: "Gutunganya itsinda ry'inyandiko {{index}}",
    sub_step_chunk_label_finished:
      "Byatunganijwe itsinda ry'inyandiko {{index}}",
    sub_step_memo_label:
      "Byabonetse amakuru y'amategeko muri {{workspaceSlug}}",
    sub_step_memo_label_neutral:
      "Gushaka amakuru y'amategeko muri {{workspaceSlug}}",
    sub_step_memo_label_finished:
      "Byabonetse amakuru y'amategeko muri {{workspaceSlug}}",
    placeholder_sub_task: "Akazi gato mu murongo",
    desc_fetching_memos:
      "Gushaka amakuru y'amategeko akenewe mu mahuriro y'akazi ahujwe",
    desc_processing_chunks:
      "Gusesengura no gukuramo amakuru mu matsinda y'inyandiko",
    desc_combining_responses: "Guhuza amakuru mu gisubizo cyuzuye",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Umurimo w'amategeko urakora inyuma.",
    dd: "Kwandika inyandiko bikomeje inyuma.",
    reopen: "Fungura idirishya rya sitati",
  },
};
