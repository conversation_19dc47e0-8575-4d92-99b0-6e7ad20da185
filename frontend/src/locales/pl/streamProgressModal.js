export default {
  // =========================
  // CDB PROGRESS
  // =========================
  cdbProgress: {
    "close-msg": "<PERSON>zy na pewno chcesz anulować proces?",
    general: {
      placeholderSubTask: "Przetwarzanie elementu {{index}}...",
    },
    main: {
      step1: {
        label: "Generuj listę sekcji",
        desc: "Używanie głównego dokumentu do utworzenia wstępnej struktury.",
      },
      step2: {
        label: "Przetwarzanie dokumentów",
        desc: "Generowanie opisów i sprawdzanie trafności.",
      },
      step3: {
        label: "Mapowanie dokumentów do sekcji",
        desc: "Przypisywanie odpowiednich dokumentów do każdej sekcji.",
      },
      step4: {
        label: "Identyfikacja zagadnień prawnych",
        desc: "Wyodrębnianie ważnych zagadnień prawnych dla każdej sekcji.",
      },
      step5: {
        label: "Generowanie not prawnych",
        desc: "Tworzenie not prawnych dla zidentyfikowanych zagadnień.",
      },
      step6: {
        label: "Pisanie sekcji",
        desc: "Napisanie treści dla każdej pojedynczej sekcji.",
      },
      step7: {
        label: "Scalanie i finalizacja dokumentu",
        desc: "Łączenie sekcji w końcowy dokument.",
      },
    },
    noMain: {
      step1: {
        label: "Przetwarzanie dokumentów",
        desc: "Generowanie opisów dla wszystkich przesłanych plików.",
      },
      step2: {
        label: "Generowanie listy sekcji",
        desc: "Tworzenie strukturalnej listy sekcji z podsumowań dokumentów.",
      },
      step3: {
        label: "Finalizacja mapowania dokumentów",
        desc: "Potwierdzenie trafności dokumentów dla każdej planowanej sekcji.",
      },
      step4: {
        label: "Identyfikacja zagadnień prawnych",
        desc: "Wyodrębnianie ważnych zagadnień prawnych dla każdej sekcji.",
      },
      step5: {
        label: "Generowanie not prawnych",
        desc: "Tworzenie not prawnych dla zidentyfikowanych zagadnień.",
      },
      step6: {
        label: "Pisanie sekcji",
        desc: "Napisanie treści dla każdej pojedynczej sekcji.",
      },
      step7: {
        label: "Scalanie i finalizacja dokumentu",
        desc: "Łączenie wszystkich sekcji w końcowy dokument prawny.",
      },
    },
    referenceFiles: {
      step1: {
        label: "Przetwarzanie plików referencyjnych",
        desc: "Przetwarzanie plików referencyjnych",
      },
      step2: {
        label: "Przetwarzanie plików przeglądowych",
        desc: "Przetwarzanie plików przeglądowych",
      },
      step3: {
        label: "Generowanie listy sekcji",
        desc: "Generowanie listy sekcji",
      },
      step4: {
        label: "Pisanie sekcji",
        desc: "Pisanie sekcji",
      },
      step5: {
        label: "Generowanie raportu",
        desc: "Generowanie raportu",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Potwierdź przerwanie",
    confirm_abort_description: "Czy na pewno chcesz przerwać proces?",
    keep_running: "Kontynuuj",
    abort_run: "Przerwij proces",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================
  streamdd_progress_modal: {
    title: "Postęp generowania odpowiedzi",
    description:
      "Pokazuje rzeczywisty postęp zadań niezbędnych do ukończenia tworzenia dokumentów, w zależności od powiązania z innymi obszarami roboczymi i rozmiaru plików. Modal zamyka się automatycznie po ukończeniu wszystkich kroków.",
    step_fetching_memos:
      "Pobieranie danych prawnych dotyczących bieżących tematów",
    step_processing_chunks: "Przetwarzanie przesłanych dokumentów",
    step_combining_responses: "Finalizacja odpowiedzi",
    sub_step_chunk_label: "Przetwarzanie grupy dokumentów {{index}}",
    sub_step_memo_label: "Dane prawne pobrane z {{workspaceSlug}}",
    placeholder_sub_task: "Oczekiwanie na krok",
    desc_fetching_memos:
      "Pobieranie odpowiednich informacji prawnych z powiązanych obszarów roboczych",
    desc_processing_chunks:
      "Analiza i wyodrębnianie informacji z grup dokumentów",
    desc_combining_responses: "Łączenie informacji w kompleksową odpowiedź",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Zadanie prawne działa w tle.",
    dd: "Tworzenie dokumentu kontynuuje w tle.",
    reopen: "Otwórz okno statusu",
  },
};
