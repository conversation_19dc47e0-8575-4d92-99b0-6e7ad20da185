export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Przetwarzanie",
    processing: "Przetwarzanie...",
    step: "Krok",
    timeLeft: "Pozostały czas",
    details: "Szczegóły",
    abort: "Przerwij",
    modalTitle: "Szczegóły postępu",
    "close-msg": "<PERSON>zy na pewno chcesz anulować proces?",
    noThreadSelected: "Nie wybrano wątku",
    noActiveProgress: "Brak aktywnego postępu",
    of: "z",
    started: "Roz<PERSON>częto",
    failed: "Nieudane",
    error: {
      title: "Wystąpił błąd podczas przetwarzania Twojego żądania.",
      description:
        "Spróbuj ponownie lub skontaktuj się z pomocą techniczną, jeśli problem będzie się powtarzał.",
      retry: "Spróbuj ponownie",
      dismiss: "Zamknij",
      showDetails: "Po<PERSON>ż szczegóły techniczne",
      hideDetails: "Ukryj szczegóły techniczne",
    },
    cancelled: "Proces został anulowany.",
    completed: "Proces zakończony",
    completedDescription: "Dokument został wygenerowany i dodany do czatu",
    types: {
      main: {
        step1: {
          label: "Generowanie listy sekcji",
          labelNeutral: "Generować listę sekcji",
          labelFinished: "Wygenerowano listę sekcji",
          desc: "Używanie głównego dokumentu do utworzenia wstępnej struktury.",
        },
        step2: {
          label: "Przetwarzanie dokumentów",
          labelNeutral: "Przetwarzać dokumenty",
          labelFinished: "Przetworzono dokumenty",
          desc: "Generowanie opisów i sprawdzanie trafności.",
        },
        step3: {
          label: "Mapowanie dokumentów do sekcji",
          labelNeutral: "Mapować dokumenty do sekcji",
          labelFinished: "Zmapowano dokumenty do sekcji",
          desc: "Przypisywanie odpowiednich dokumentów do każdej sekcji.",
        },
        step4: {
          label: "Identyfikowanie problemów prawnych",
          labelNeutral: "Identyfikować problemy prawne",
          labelFinished: "Zidentyfikowano problemy prawne",
          desc: "Wyodrębnianie kluczowych problemów prawnych dla każdej sekcji.",
        },
        step5: {
          label: "Generowanie not prawnych",
          labelNeutral: "Generować noty prawne",
          labelFinished: "Wygenerowano noty prawne",
          desc: "Tworzenie memorandów prawnych dla zidentyfikowanych problemów.",
        },
        step6: {
          label: "Tworzenie sekcji",
          labelNeutral: "Tworzyć sekcje",
          labelFinished: "Utworzono sekcje",
          desc: "Komponowanie treści dla każdej pojedynczej sekcji.",
        },
        step7: {
          label: "Łączenie i finalizowanie dokumentu",
          labelNeutral: "Łączyć i finalizować dokument",
          labelFinished: "Połączono i sfinalizowano dokument",
          desc: "Składanie sekcji w końcowy dokument.",
        },
      },
      noMain: {
        step1: {
          label: "Przetwarzanie dokumentów",
          labelNeutral: "Przetwarzać dokumenty",
          labelFinished: "Przetworzono dokumenty",
          desc: "Generowanie opisów dla wszystkich przesłanych plików.",
        },
        step2: {
          label: "Generowanie listy sekcji",
          labelNeutral: "Generować listę sekcji",
          labelFinished: "Wygenerowano listę sekcji",
          desc: "Tworzenie uporządkowanej listy sekcji z podsumowań dokumentów.",
        },
        step3: {
          label: "Finalizowanie mapowania dokumentów",
          labelNeutral: "Finalizować mapowanie dokumentów",
          labelFinished: "Sfinalizowano mapowanie dokumentów",
          desc: "Potwierdzanie trafności dokumentów dla każdej planowanej sekcji.",
        },
        step4: {
          label: "Identyfikowanie problemów prawnych",
          labelNeutral: "Identyfikować problemy prawne",
          labelFinished: "Zidentyfikowano problemy prawne",
          desc: "Wyodrębnianie kluczowych problemów prawnych dla każdej sekcji.",
        },
        step5: {
          label: "Generowanie not prawnych",
          labelNeutral: "Generować noty prawne",
          labelFinished: "Wygenerowano noty prawne",
          desc: "Tworzenie memorandów prawnych dla zidentyfikowanych problemów.",
        },
        step6: {
          label: "Tworzenie sekcji",
          labelNeutral: "Tworzyć sekcje",
          labelFinished: "Utworzono sekcje",
          desc: "Komponowanie treści dla każdej pojedynczej sekcji.",
        },
        step7: {
          label: "Łączenie i finalizowanie dokumentu",
          labelNeutral: "Łączyć i finalizować dokument",
          labelFinished: "Połączono i sfinalizowano dokument",
          desc: "Składanie wszystkich sekcji w końcowy dokument prawny.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Przetwarzanie plików referencyjnych",
          labelNeutral: "Przetwarzać pliki referencyjne",
          labelFinished: "Przetworzono pliki referencyjne",
          desc: "Przetwarzanie plików referencyjnych",
        },
        step2: {
          label: "Przetwarzanie plików przeglądowych",
          labelNeutral: "Przetwarzać pliki przeglądowe",
          labelFinished: "Przetworzono pliki przeglądowe",
          desc: "Przetwarzanie plików przeglądowych",
        },
        step3: {
          label: "Generowanie listy sekcji",
          labelNeutral: "Generować listę sekcji",
          labelFinished: "Wygenerowano listę sekcji",
          desc: "Generowanie listy sekcji",
        },
        step4: {
          label: "Pisanie sekcji",
          labelNeutral: "Pisać sekcje",
          labelFinished: "Napisano sekcje",
          desc: "Pisanie sekcji",
        },
        step5: {
          label: "Generowanie raportu",
          labelNeutral: "Generować raport",
          labelFinished: "Wygenerowano raport",
          desc: "Generowanie raportu",
        },
      },
      documentDrafting: {
        step1: {
          label: "Przygotowywanie dokumentów",
          labelNeutral: "Przygotowywać dokumenty",
          labelFinished: "Przygotowano dokumenty",
          desc: "Zbieranie i przygotowywanie wszystkich odpowiednich dokumentów dla procesu tworzenia.",
        },
        step2: {
          label: "Analizowanie treści",
          labelNeutral: "Analizować treść",
          labelFinished: "Przeanalizowano treść",
          desc: "Analizowanie treści dokumentów i identyfikowanie kluczowych problemów prawnych i klauzul.",
        },
        step3: {
          label: "Generowanie szkicu",
          labelNeutral: "Generować szkic",
          labelFinished: "Wygenerowano szkic",
          desc: "Komponowanie końcowego szkicu dokumentu na podstawie analizy.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Tworzenie Dokumentu",
  },
  cdbProgress: {
    title: "Złożony generator dokumentów",
    general: {
      placeholderSubTask: "Przetwarzać element {{index}}...",
      placeholderSubTaskOngoing: "Przetwarzanie elementu {{index}}...",
    },
    subTasks: {
      mappingSections: "Mapowanie sekcji dla: {{filename}}...",
      mappingSectionsNeutral: "Mapować sekcje dla: {{filename}}...",
      mappingSectionsFinished: "Zmapowano sekcje dla: {{filename}}",
      identifyingIssues:
        "Identyfikowanie problemów dla Sekcji {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesNeutral:
        "Identyfikować problemy dla Sekcji {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesFinished:
        "Zidentyfikowano problemy dla Sekcji {{sectionNumber}}: {{sectionTitle}}",
      processingDocument: "Przetwarzanie: {{filename}} - {{action}}...",
      processingDocumentNeutral: "Przetwarzać: {{filename}} - {{action}}...",
      processingDocumentFinished: "Przetworzono: {{filename}} - {{action}}",
      generatingMemo: "Generowanie notatki dla: {{issueText}}...",
      generatingMemoNeutral: "Generuj notatkę dla: {{issueText}}...",
      generatingMemoFinished: "Wygenerowano notatkę dla: {{issueText}}",
      resolvingWorkspace:
        "Rozwiązywanie obszaru roboczego dla: {{issueText}}...",
      resolvingWorkspaceNeutral: "Rozwiąż obszar roboczy dla: {{issueText}}...",
      resolvingWorkspaceFinished:
        "Rozwiązano obszar roboczy dla: {{issueText}}",
      draftingSection: "Pisanie Sekcji {{sectionNumber}}: {{sectionTitle}}...",
      draftingSectionNeutral:
        "Napisz Sekcję {{sectionNumber}}: {{sectionTitle}}...",
      draftingSectionFinished:
        "Napisano Sekcję {{sectionNumber}}: {{sectionTitle}}",
      memoFor: "Notatka dla: {{issueText}} (or: {{workspaceSlug}})",
      memoForNeutral:
        "Generuj notatkę dla: {{issueText}} (or: {{workspaceSlug}})",
      memoForFinished:
        "Wygenerowano notatkę dla: {{issueText}} (or: {{workspaceSlug}})",
      errorNoWorkspace: "Błąd - Brak obszaru roboczego dla: {{issueText}}...",
      errorDraftingSection:
        "Błąd podczas tworzenia Sekcji {{sectionNumber}}: {{sectionTitle}}",
      actions: {
        generatingDescription: "Generowanie opisu",
        checkingRelevance: "Sprawdzanie trafności",
        generatingSectionList: "Generowanie listy sekcji",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Potwierdź przerwanie",
    confirm_abort_description: "Czy na pewno chcesz anulować proces?",
    keep_running: "Kontynuuj działanie",
    abort_run: "Przerwij proces",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Postęp generowania odpowiedzi",
    description:
      "Wyświetla postęp zadań w czasie rzeczywistym w celu ukończenia zapytania, w zależności od powiązania z innymi obszarami roboczymi i rozmiarów plików. Okno zamknie się automatycznie po ukończeniu wszystkich kroków.",
    step_fetching_memos: "Pobieranie danych prawnych na aktualne tematy",
    step_processing_chunks: "Przetwarzanie przesłanych dokumentów",
    step_combining_responses: "Finalizowanie odpowiedzi",
    sub_step_chunk_label: "Przetwarzanie grupy dokumentów {{index}}",
    sub_step_chunk_label_neutral: "Przetworzyć grupę dokumentów {{index}}",
    sub_step_chunk_label_finished: "Przetworzono grupę dokumentów {{index}}",
    sub_step_memo_label: "Pobrano dane prawne z {{workspaceSlug}}",
    sub_step_memo_label_neutral: "Pobrać dane prawne z {{workspaceSlug}}",
    sub_step_memo_label_finished: "Pobrano dane prawne z {{workspaceSlug}}",
    placeholder_sub_task: "Podzadanie w kolejce",
    desc_fetching_memos:
      "Pobieranie istotnych informacji prawnych z powiązanych obszarów roboczych",
    desc_processing_chunks:
      "Analizowanie i wyodrębnianie informacji z grup dokumentów",
    desc_combining_responses: "Synteza informacji w kompleksową odpowiedź",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Zadanie prawne działa w tle.",
    dd: "Tworzenie dokumentu kontynuowane w tle.",
    reopen: "Otwórz okno statusu",
  },
};
