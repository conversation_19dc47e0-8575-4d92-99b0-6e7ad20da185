const TRANSLATIONS = {
  "support-functions-llm": {
    title: "LLM dla Funkcji Wsparcia",
    description:
      "Skonfiguruj centralnego dostawcę LLM dla różnych funkcji wsparcia i zarządzaj ustawieniami zastąpienia.",
    "save-success":
      "Ustawienia LLM Funkcji Wsparcia zostały pomyślnie zapisane",
    "save-error":
      "Nie udało się zapisać ustawień LLM Funkcji Wsparcia: {{error}}",
    functions: {
      title: "Zastąpienia Funkcji Wsparcia",
    },
    "prompt-upgrade": {
      title: "Ulepszenie Promptu",
      description:
        "Zastąp LLM używany do ulepszania promptów użytkownika, aby używać LLM Funkcji Wsparcia zamiast konkretnego ustawienia LLM Ulepszenia Promptu.",
    },
    validation: {
      title: "Prompt Walidacji",
      description:
        "Zastąp LLM używany do walidacji odpowiedzi, aby uży<PERSON>ć LLM Funkcji Wsparcia zamiast konkretnego ustawienia LLM Walidacji.",
    },
    "manual-time": {
      title: "Ręczne Szacowanie Czasu",
      description:
        "Zastąp LLM używany do ręcznego szacowania czasu pracy, aby używać LLM Funkcji Wsparcia zamiast konkretnego ustawienia.",
    },
  },
  "override-notice": {
    title: "Zastąpienie Ustawienia Aktywne",
    message:
      "Ustawienie LLM {{functionName}} jest obecnie zastąpione przez konfigurację LLM Funkcji Wsparcia.",
    "manage-link": "Zarządzaj ustawieniami LLM Funkcji Wsparcia",
  },
};

export default TRANSLATIONS;
