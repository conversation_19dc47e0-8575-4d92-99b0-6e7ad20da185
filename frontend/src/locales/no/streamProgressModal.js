export default {
  // =========================
  // CDB PROGRESS
  // =========================
  cdbProgress: {
    "close-msg": "Er du sikker på at du vil avbryte prosessen?",
    general: {
      placeholderSubTask: "Behandler element {{index}}...",
    },
    main: {
      step1: {
        label: "Generer liste over seksjoner",
        desc: "Bruker hoveddokumentet for å lage en innledende struktur.",
      },
      step2: {
        label: "Behandle dokumenter",
        desc: "Genererer beskrivelser og sjekker relevans.",
      },
      step3: {
        label: "Koble dokumenter til seksjoner",
        desc: "Tildeler relevante dokumenter til hver seksjon.",
      },
      step4: {
        label: "Identifiser juridiske spørsmål",
        desc: "Trekker ut viktige juridiske spørsmål for hver seksjon.",
      },
      step5: {
        label: "Generer juridiske notater",
        desc: "Lager juridiske notater for de identifiserte spørsmålene.",
      },
      step6: {
        label: "Skrive seksjoner",
        desc: "Skriver innholdet for hver enkelt seksjon.",
      },
      step7: {
        label: "Slå sammen og fullføre dokument",
        desc: "Setter sammen seksjonene til det endelige dokumentet.",
      },
    },
    noMain: {
      step1: {
        label: "Behandle dokumenter",
        desc: "Genererer beskrivelser for alle opplastede filer.",
      },
      step2: {
        label: "Generer liste over seksjoner",
        desc: "Lager en strukturert liste over seksjoner fra dokumentsammendrag.",
      },
      step3: {
        label: "Fullføre dokumentkobling",
        desc: "Bekrefter dokumentrelevans for hver planlagte seksjon.",
      },
      step4: {
        label: "Identifiser juridiske spørsmål",
        desc: "Trekker ut viktige juridiske spørsmål for hver seksjon.",
      },
      step5: {
        label: "Generer juridiske notater",
        desc: "Lager juridiske notater for de identifiserte spørsmålene.",
      },
      step6: {
        label: "Skrive seksjoner",
        desc: "Skriver innholdet for hver enkelt seksjon.",
      },
      step7: {
        label: "Slå sammen og fullføre dokument",
        desc: "Setter sammen alle seksjoner til det endelige juridiske dokumentet.",
      },
    },
    referenceFiles: {
      step1: {
        label: "Behandler referansefiler",
        desc: "Behandler referansefiler",
      },
      step2: {
        label: "Behandler gjennomgangsfiler",
        desc: "Behandler gjennomgangsfiler",
      },
      step3: {
        label: "Generer seksjonsliste",
        desc: "Generer seksjonsliste",
      },
      step4: {
        label: "Skrive seksjoner",
        desc: "Skrive seksjoner",
      },
      step5: {
        label: "Generer rapport",
        desc: "Generer rapport",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Bekreft avbrudd",
    confirm_abort_description: "Er du sikker på at du vil avbryte prosessen?",
    keep_running: "Fortsett kjøring",
    abort_run: "Avbryt prosess",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================
  streamdd_progress_modal: {
    title: "Fremdrift for svargenerering",
    description:
      "Viser den faktiske fremdriften av oppgaver for å fullføre opprettelsen av dokumenter, avhengig av kobling til andre arbeidsområder og størrelsen på filene. Modalen lukkes automatisk når alle trinn er fullført.",
    step_fetching_memos: "Henter juridiske data om aktuelle emner",
    step_processing_chunks: "Behandler opplastede dokumenter",
    step_combining_responses: "Fullfører svar",
    sub_step_chunk_label: "Behandler dokumentgruppe {{index}}",
    sub_step_memo_label: "Juridiske data hentet fra {{workspaceSlug}}",
    placeholder_sub_task: "Venter på trinn",
    desc_fetching_memos:
      "Henter relevant juridisk informasjon fra koblede arbeidsområder",
    desc_processing_chunks:
      "Analyserer og trekker ut informasjon fra dokumentgrupper",
    desc_combining_responses: "Kombinerer informasjon til et omfattende svar",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Juridisk oppgave kjører i bakgrunnen.",
    dd: "Dokumentoppretting fortsetter i bakgrunnen.",
    reopen: "Åpne statusvindu",
  },
};
