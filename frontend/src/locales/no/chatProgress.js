export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Behandler",
    processing: "Behandler...",
    step: "Steg",
    timeLeft: "Tid igjen",
    details: "Detaljer",
    abort: "Avbryt",
    modalTitle: "Fremdriftsdetaljer",
    "close-msg": "Er du sikker på at du vil avbryte prosessen?",
    noThreadSelected: "Ingen tråd valgt",
    noActiveProgress: "Ingen aktiv fremgang",
    of: "av",
    started: "Startet",
    failed: "Mislyktes",
    error: {
      title: "Det oppstod en feil under behandling av forespørselen din.",
      description:
        "Vennligst prøv igjen eller kontakt support hvis problemet vedvarer.",
      retry: "Prøv igjen",
      dismiss: "Lukk",
      showDetails: "Vis tekniske detaljer",
      hideDetails: "Skjul tekniske detaljer",
    },
    cancelled: "Prosessen ble avbrutt.",
    completed: "Prosess fullført",
    completedDescription: "Dokument har blitt generert og lagt til i chatten",
    types: {
      main: {
        step1: {
          label: "Genererer seksjonsliste",
          labelNeutral: "Generere seksjonsliste",
          labelFinished: "Generert seksjonsliste",
          desc: "Bruker hoveddokumentet for å lage en innledende struktur.",
        },
        step2: {
          label: "Behandler dokumenter",
          labelNeutral: "Behandle dokumenter",
          labelFinished: "Behandlet dokumenter",
          desc: "Genererer beskrivelser og sjekker relevans.",
        },
        step3: {
          label: "Kobler dokumenter til seksjoner",
          labelNeutral: "Koble dokumenter til seksjoner",
          labelFinished: "Koblet dokumenter til seksjoner",
          desc: "Tildeler relevante dokumenter til hver seksjon.",
        },
        step4: {
          label: "Identifiserer juridiske problemer",
          labelNeutral: "Identifisere juridiske problemer",
          labelFinished: "Identifisert juridiske problemer",
          desc: "Trekker ut sentrale juridiske problemer for hver seksjon.",
        },
        step5: {
          label: "Genererer juridiske memoer",
          labelNeutral: "Generere juridiske memoer",
          labelFinished: "Generert juridiske memoer",
          desc: "Lager juridiske memoranda for de identifiserte problemene.",
        },
        step6: {
          label: "Utarbeider seksjoner",
          labelNeutral: "Utarbeide seksjoner",
          labelFinished: "Utarbeidet seksjoner",
          desc: "Komponerer innholdet for hver enkelt seksjon.",
        },
        step7: {
          label: "Kombinerer & fullfører dokument",
          labelNeutral: "Kombinere & fullføre dokument",
          labelFinished: "Kombinert & fullført dokument",
          desc: "Setter sammen seksjoner til det endelige dokumentet.",
        },
      },
      noMain: {
        step1: {
          label: "Behandler dokumenter",
          labelNeutral: "Behandle dokumenter",
          labelFinished: "Behandlet dokumenter",
          desc: "Genererer beskrivelser for alle opplastede filer.",
        },
        step2: {
          label: "Genererer seksjonsliste",
          labelNeutral: "Generere seksjonsliste",
          labelFinished: "Generert seksjonsliste",
          desc: "Lager en strukturert liste over seksjoner fra dokumentsammendrag.",
        },
        step3: {
          label: "Fullfører dokumentkobling",
          labelNeutral: "Fullføre dokumentkobling",
          labelFinished: "Fullført dokumentkobling",
          desc: "Bekrefter dokumentenes relevans for hver planlagte seksjon.",
        },
        step4: {
          label: "Identifiserer juridiske problemer",
          labelNeutral: "Identifisere juridiske problemer",
          labelFinished: "Identifisert juridiske problemer",
          desc: "Trekker ut sentrale juridiske problemer for hver seksjon.",
        },
        step5: {
          label: "Genererer juridiske memoer",
          labelNeutral: "Generere juridiske memoer",
          labelFinished: "Generert juridiske memoer",
          desc: "Lager juridiske memoranda for de identifiserte problemene.",
        },
        step6: {
          label: "Utarbeider seksjoner",
          labelNeutral: "Utarbeide seksjoner",
          labelFinished: "Utarbeidet seksjoner",
          desc: "Komponerer innholdet for hver enkelt seksjon.",
        },
        step7: {
          label: "Kombinerer & fullfører dokument",
          labelNeutral: "Kombinere & fullføre dokument",
          labelFinished: "Kombinert & fullført dokument",
          desc: "Setter sammen alle seksjoner til det endelige juridiske dokumentet.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Behandler referansefiler",
          labelNeutral: "Behandle referansefiler",
          labelFinished: "Behandlet referansefiler",
          desc: "Behandler referansefiler",
        },
        step2: {
          label: "Behandler gjennomgangsfiler",
          labelNeutral: "Behandle gjennomgangsfiler",
          labelFinished: "Behandlet gjennomgangsfiler",
          desc: "Behandler gjennomgangsfiler",
        },
        step3: {
          label: "Genererer seksjonsliste",
          labelNeutral: "Generere seksjonsliste",
          labelFinished: "Generert seksjonsliste",
          desc: "Genererer seksjonsliste",
        },
        step4: {
          label: "Utarbeider seksjoner",
          labelNeutral: "Utarbeide seksjoner",
          labelFinished: "Utarbeidet seksjoner",
          desc: "Utarbeider seksjoner",
        },
        step5: {
          label: "Genererer rapport",
          labelNeutral: "Generere rapport",
          labelFinished: "Generert rapport",
          desc: "Genererer rapport",
        },
      },
      documentDrafting: {
        step1: {
          label: "Forbereder dokumenter",
          labelNeutral: "Forberede dokumenter",
          labelFinished: "Forberedt dokumenter",
          desc: "Samler og forbereder alle relevante dokumenter for utarbeidelsesprosessen.",
        },
        step2: {
          label: "Analyserer innhold",
          labelNeutral: "Analysere innhold",
          labelFinished: "Analysert innhold",
          desc: "Analyserer dokumentinnhold og identifiserer viktige juridiske problemer og klausuler.",
        },
        step3: {
          label: "Genererer utkast",
          labelNeutral: "Generere utkast",
          labelFinished: "Generert utkast",
          desc: "Komponerer det endelige dokumentutkastet basert på analysen.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Dokumentutkast",
  },
  cdbProgress: {
    title: "Kompleks dokumentbygger",
    general: {
      placeholderSubTask: "Behandle element {{index}}...",
      placeholderSubTaskOngoing: "Behandler element {{index}}...",
    },
    subTasks: {
      mappingSections: "Kartlegger seksjoner for: {{filename}}...",
      mappingSectionsNeutral: "Kartlegge seksjoner for: {{filename}}...",
      mappingSectionsFinished: "Kartlagt seksjoner for: {{filename}}",
      identifyingIssues:
        "Identifiserer problemer for Seksjon {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesNeutral:
        "Identifisere problemer for Seksjon {{sectionNumber}}: {{sectionTitle}}...",
      identifyingIssuesFinished:
        "Identifisert problemer for Seksjon {{sectionNumber}}: {{sectionTitle}}",
      processingDocument: "Behandler: {{filename}} - {{action}}...",
      processingDocumentNeutral: "Behandle: {{filename}} - {{action}}...",
      processingDocumentFinished: "Behandlet: {{filename}} - {{action}}",
      generatingMemo: "Genererer memo for: {{issueText}}...",
      generatingMemoNeutral: "Generer memo for: {{issueText}}...",
      generatingMemoFinished: "Generert memo for: {{issueText}}",
      resolvingWorkspace: "Løser arbeidsområde for: {{issueText}}...",
      resolvingWorkspaceNeutral: "Løs arbeidsområde for: {{issueText}}...",
      resolvingWorkspaceFinished: "Løst arbeidsområde for: {{issueText}}",
      draftingSection: "Skriver Seksjon {{sectionNumber}}: {{sectionTitle}}...",
      draftingSectionNeutral:
        "Skriv Seksjon {{sectionNumber}}: {{sectionTitle}}...",
      draftingSectionFinished:
        "Skrev Seksjon {{sectionNumber}}: {{sectionTitle}}",
      memoFor: "Memo for: {{issueText}} (ao: {{workspaceSlug}})",
      memoForNeutral: "Generer memo for: {{issueText}} (ao: {{workspaceSlug}})",
      memoForFinished:
        "Generert memo for: {{issueText}} (ao: {{workspaceSlug}})",
      errorNoWorkspace: "Feil - Ingen arbeidsområde for: {{issueText}}...",
      errorDraftingSection:
        "Feil ved skriving av Seksjon {{sectionNumber}}: {{sectionTitle}}",
      actions: {
        generatingDescription: "Genererer beskrivelse",
        checkingRelevance: "Sjekker relevans",
        generatingSectionList: "Genererer seksjonsliste",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Bekreft avbrudd",
    confirm_abort_description: "Er du sikker på at du vil avbryte prosessen?",
    keep_running: "Fortsett kjøring",
    abort_run: "Avbryt prosess",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Fremdrift for svarsgenerering",
    description:
      "Viser sanntidsfremdrift for oppgaver for å fullføre forespørselen, avhengig av kobling til andre arbeidsområder og filstørrelser. Vinduet lukkes automatisk når alle trinn er fullført.",
    step_fetching_memos: "Henter juridiske data om aktuelle emner",
    step_processing_chunks: "Behandler opplastede dokumenter",
    step_combining_responses: "Fullfører svar",
    sub_step_chunk_label: "Behandler dokumentgruppe {{index}}",
    sub_step_chunk_label_neutral: "Behandle dokumentgruppe {{index}}",
    sub_step_chunk_label_finished: "Behandlet dokumentgruppe {{index}}",
    sub_step_memo_label: "Hentet juridiske data fra {{workspaceSlug}}",
    sub_step_memo_label_neutral: "Hente juridiske data fra {{workspaceSlug}}",
    sub_step_memo_label_finished: "Hentet juridiske data fra {{workspaceSlug}}",
    placeholder_sub_task: "Køet deloppgave",
    desc_fetching_memos:
      "Henter relevant juridisk informasjon fra tilknyttede arbeidsområder",
    desc_processing_chunks:
      "Analyserer og trekker ut informasjon fra dokumentgrupper",
    desc_combining_responses: "Syntetiserer informasjon til et omfattende svar",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Juridisk oppgave kjører i bakgrunnen.",
    dd: "Dokumentutkast fortsetter i bakgrunnen.",
    reopen: "Åpne statusvindu",
  },
};
