export default {
  // =========================
  // CDB PROGRESS
  // =========================
  cdbProgress: {
    "close-msg": "Are you sure you want to cancel the process?",
    general: {
      placeholderSubTask: "Processing item {{index}}...",
    },
    main: {
      step1: {
        label: "Generate list of sections",
        desc: "Using the main document to create an initial structure.",
      },
      step2: {
        label: "Process documents",
        desc: "Generating descriptions and checking relevance.",
      },
      step3: {
        label: "Map documents to sections",
        desc: "Assigning relevant documents to each section.",
      },
      step4: {
        label: "Identify legal issues",
        desc: "Extracting important legal questions for each section.",
      },
      step5: {
        label: "Generate legal memos",
        desc: "Creating legal memos for the identified questions.",
      },
      step6: {
        label: "Draft sections",
        desc: "Writing the content for each individual section.",
      },
      step7: {
        label: "Merge & finalize document",
        desc: "Assembling the sections into the final document.",
      },
    },
    noMain: {
      step1: {
        label: "Process documents",
        desc: "Generating descriptions for all uploaded files.",
      },
      step2: {
        label: "Generate list of sections",
        desc: "Creating a structured list of sections from document summaries.",
      },
      step3: {
        label: "Finalize document mapping",
        desc: "Confirming document relevance for each planned section.",
      },
      step4: {
        label: "Identify legal issues",
        desc: "Extracting important legal questions for each section.",
      },
      step5: {
        label: "Generate legal memos",
        desc: "Creating legal memos for the identified questions.",
      },
      step6: {
        label: "Draft sections",
        desc: "Writing the content for each individual section.",
      },
      step7: {
        label: "Merge & finalize document",
        desc: "Assembling all sections into the final legal document.",
      },
    },
    referenceFiles: {
      step1: {
        label: "Processing reference files",
        desc: "Processing reference files",
      },
      step2: {
        label: "Processing review files",
        desc: "Processing review files",
      },
      step3: {
        label: "Generate section list",
        desc: "Generate section list",
      },
      step4: {
        label: "Draft sections",
        desc: "Draft sections",
      },
      step5: {
        label: "Generate report",
        desc: "Generate report",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Confirm Abort",
    confirm_abort_description: "Are you sure you want to abort the process?",
    keep_running: "Keep Running",
    abort_run: "Abort Process",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================
  streamdd_progress_modal: {
    title: "Answer Generation Progress",
    description:
      "Shows the real progress of tasks to complete the creation of documents, depending on the linking to other workspaces and the size of the files. The modal closes automatically when all steps are completed.",
    step_fetching_memos: "Fetching legal data on current topics",
    step_processing_chunks: "Processing uploaded documents",
    step_combining_responses: "Finalizing answer",
    sub_step_chunk_label: "Processing document group {{index}}",
    sub_step_memo_label: "Legal data retrieved from {{workspaceSlug}}",
    placeholder_sub_task: "Waiting step",
    desc_fetching_memos:
      "Retrieving relevant legal information from linked workspaces",
    desc_processing_chunks:
      "Analyzing and extracting information from document groups",
    desc_combining_responses:
      "Combining information into a comprehensive answer",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Legal task running in background.",
    dd: "Document creation continues in background.",
    reopen: "Open status window",
  },
};
