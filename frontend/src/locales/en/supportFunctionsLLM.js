const TRANSLATIONS = {
  "support-functions-llm": {
    title: "LLM for Support Functions",
    description:
      "Configure a central LLM provider for various supporting functions and manage override settings.",
    "save-success": "Support Functions LLM settings saved successfully",
    "save-error": "Failed to save Support Functions LLM settings: {{error}}",
    functions: {
      title: "Support Function Overrides",
    },
    "prompt-upgrade": {
      title: "Prompt Upgrade",
      description:
        "Override the LLM used for upgrading user prompts to use the Support Functions LLM instead of the specific Prompt Upgrade LLM setting.",
    },
    validation: {
      title: "Validation Prompt",
      description:
        "Override the LLM used for validating responses to use the Support Functions LLM instead of the specific Validation LLM setting.",
    },
    "manual-time": {
      title: "Manual Time Estimation",
      description:
        "Override the LLM used for manual work time estimation to use the Support Functions LLM instead of the specific setting.",
    },
  },
  "override-notice": {
    title: "Setting Override Active",
    message:
      "The {{functionName}} LLM setting is currently overridden by the Support Functions LLM configuration.",
    "manage-link": "Manage Support Functions LLM settings",
  },
};

export default TRANSLATIONS;
