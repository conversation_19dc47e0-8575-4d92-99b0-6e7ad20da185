export default {
  settings: {
    title: "Instance Settings",
    system: "System Settings",
    invites: "Invites",
    invitations: "Invitations",
    users: "Users",
    chat: "DD Prompt settings",
    chats: "Chats",
    "chat-ui-settings": "Chat UI Settings",
    "request-legal-assistance": "Request Legal Assistance",
    workspaces: "Workspaces",
    "workspace-chats": "Workspace Chats",
    customization: "Customization",
    "api-keys": "Developer API",
    llm: "LLM",
    "support-functions-llm": "Support Functions LLM",
    "custom-user-ai": "Custom User AI",
    transcription: "Transcription",
    embedder: "Embedder",
    "text-splitting": "Text Splitter & Chunking",
    "vector-database": "Vector Database",
    embeds: "Chat Embed",
    "embed-chats": "Chat Embed History",
    security: "Security",
    "event-logs": "Event Logs",
    "privacy-data": "Privacy and Data",
    "ai-providers": "AI Providers",
    "agent-skills": "Agent Skills",
    admin: "Admin",
    tools: "Tools",
    audio: "Audio Preference",
    "link-settings": "System",
    "default-settings": "Default System",
    "browser-extension": "Browser Extension",
    "prompt-upgrade-llm": "Prompt Upgrade LLM",
    "voice-speech": "Voice & Speech",
    "pdr-settings": "PDR System",
    "document-builder": "Document Builder",
    "mcp-servers": "MCP Servers",
    feedback: "Feedback",
    deep_search: {
      title: "Deep Search Settings",
      description:
        "Configure web search capabilities for chat responses. When enabled, the system can search the web for information to enhance responses.",
    },
  },
};
