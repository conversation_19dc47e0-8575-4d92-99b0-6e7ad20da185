import useProgressStore from "@/stores/progressStore";

export default function useThreadProgress(threadSlug) {
  const safeThreadSlug =
    typeof threadSlug === "string" && threadSlug.trim()
      ? threadSlug.trim()
      : "";

  // Use multiple specific selectors to ensure React detects all changes
  const isActive = useProgressStore((state) =>
    safeThreadSlug
      ? state.threads.get(safeThreadSlug)?.isActive || false
      : false
  );
  const isCompleted = useProgressStore((state) =>
    safeThreadSlug
      ? state.threads.get(safeThreadSlug)?.isCompleted || false
      : false
  );
  const currentStep = useProgressStore((state) =>
    safeThreadSlug ? state.threads.get(safeThreadSlug)?.currentStep || 1 : 1
  );
  const totalSteps = useProgressStore((state) =>
    safeThreadSlug ? state.threads.get(safeThreadSlug)?.totalSteps || 7 : 7
  );
  const stepStatus = useProgressStore((state) =>
    safeThreadSlug
      ? state.threads.get(safeThreadSlug)?.stepStatus || "pending"
      : "pending"
  );
  const threadState = useProgressStore((state) =>
    safeThreadSlug ? state.threads.get(safeThreadSlug) : null
  );

  const start = (totalSteps = 7, flowType = null) => {
    if (!safeThreadSlug) return;
    useProgressStore
      .getState()
      .startProcess(safeThreadSlug, totalSteps, flowType);
  };

  const update = (event) => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().updateProgress(event, safeThreadSlug);
  };

  const finish = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().finishProcess(safeThreadSlug);
  };

  const cancel = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().cancelProcess(safeThreadSlug);
  };

  const setError = (errorMessage) => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().setError(safeThreadSlug, errorMessage);
  };

  const clearError = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().clearError(safeThreadSlug);
  };

  const forceCleanup = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().forceCleanup(safeThreadSlug);
  };

  const clearStaleProgress = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().clearStaleProgress(safeThreadSlug);
  };

  const getAbortSignal = () => {
    if (!safeThreadSlug) return null;
    const controller = useProgressStore
      .getState()
      .getAbortController(safeThreadSlug);
    return controller?.signal || null;
  };

  return {
    isActive,
    currentStep,
    totalSteps,
    startTime: threadState?.startTime || null,
    flowType: threadState?.flowType || null,
    currentSubStep: threadState?.currentSubStep || null,
    stepStatus,
    stepMessage: threadState?.stepMessage || null,
    stepDetails: threadState?.stepDetails || [],
    error: threadState?.error || null,
    isCompleted,
    completionTime: threadState?.completionTime || null,

    start,
    update,
    finish,
    cancel,
    setError,
    clearError,
    forceCleanup,
    clearStaleProgress,
    getAbortSignal,
  };
}

// Clean selector hook to check if a process is active for a specific thread
export function useIsProcessActive(threadSlug) {
  const safeThreadSlug =
    typeof threadSlug === "string" && threadSlug.trim()
      ? threadSlug.trim()
      : "";

  return useProgressStore((state) => {
    if (!safeThreadSlug) return false;
    const threadState = state.threads.get(safeThreadSlug);
    return threadState?.isActive || false;
  });
}
