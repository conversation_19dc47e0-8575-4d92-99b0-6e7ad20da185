# Chat Progress System

The **Chat Progress System** gives users real-time visibility into multi-step background processes that occur within a specific chat thread. It is designed to be generic and can handle various types of long-running tasks, such as document drafting or complex data analysis.

## How It Works

1.  **Backend Events**: A backend process (e.g., a streaming chat endpoint) emits progress events via Server-Sent Events (SSE). These events are tied to a specific `threadSlug`. Each event payload can include:

    - `step`: The current step number.
    - `status`: The state of the step (e.g., `in_progress`, `complete`, `error`).
    - `message`: A descriptive message for the current step or sub-task.
    - `subStep`: The current sub-task number within a step.
    - `flowType`: An identifier for the specific process (e.g., `"document-drafting-main"`, `"analysis"`), which allows the UI to display custom step labels.

2.  **State Management (`progressStore`)**: A centralized Zustand store, `progressStore.js`, listens for these events. It maintains a map of all active processes, keyed by their `threadSlug`. This allows the system to track multiple background tasks concurrently and independently.

3.  **React Hook (`useThreadProgress`)**: The `useThreadProgress(threadSlug)` hook provides a clean and simple API for React components to access and control the progress state of a single thread. It abstracts away the direct interaction with the Zustand store.

4.  **UI Components**:
    - `ChatProgress`: A small, inline component that shows the current step, a progress bar, and buttons to view details or abort the process.
    - `ProgressModal`: A modal dialog that displays a detailed, step-by-step view of the entire process using the `ProgressList` component. It shows completed steps, the current step, and pending steps.

## Progress Calculation

### Incremental Progress with Sub-Tasks

The progress bar now provides incremental updates based on sub-task completion, offering users more granular feedback during long-running processes.

#### How Progress is Calculated

1. **Previous Steps**: Steps before the current step contribute their full percentage to the total progress.

   ```javascript
   // Each completed step contributes: (100 / totalSteps)%
   if (step < currentStep) {
     totalProgress += progressPerStep; // Full progress for completed steps
   }
   ```

2. **Current Step with Sub-Tasks**: The current step's progress is calculated based on completed sub-tasks.

   ```javascript
   if (step === currentStep && hasSubTasks) {
     const actualSubTasks = subTasks.filter((st) => !st.isPlaceholder);
     const completedSubTasks = actualSubTasks.filter(
       (st) => st.status === "complete"
     );
     const subTaskProgress = completedSubTasks.length / expectedTotal;
     totalProgress += progressPerStep * subTaskProgress;
   }
   ```

3. **Current Step without Sub-Tasks**: Steps without sub-tasks get a small progress boost (10%) for starting.
   ```javascript
   if (step === currentStep && !hasSubTasks) {
     totalProgress += progressPerStep * 0.1; // 10% for starting the step
   }
   ```

#### Example Progress Calculations

**Scenario 1: 4 steps, currently on step 2 with sub-tasks**

- Step 1: Complete (25% contribution)
- Step 2: 3 out of 6 sub-tasks complete (25% × 0.5 = 12.5% contribution)
- **Total Progress**: 37.5%

**Scenario 2: 7 steps, currently on step 1 with no sub-tasks**

- Step 1: Started but no sub-tasks (14.3% × 0.1 = 1.4% contribution)
- **Total Progress**: 1.4%

**Scenario 3: Process completed**

- **Total Progress**: 100%

#### Sub-Task Handling

- **Actual vs. Placeholder Sub-Tasks**: Only non-placeholder sub-tasks are counted in progress calculation
- **Expected Total**: Uses `expectedTotal` from step details, falling back to actual sub-task count
- **Status Filtering**: Only sub-tasks with `status === 'complete'` count as finished

## `flowType` and Customization

The system supports different types of workflows through the `flowType` parameter. Each flow type can have custom step labels and descriptions defined in the internationalization files.

### Supported Flow Types

- `"main"`: Main document flow (7 steps)
- `"noMain"`: No main document flow (7 steps)
- `"referenceFiles"`: Reference files comparison flow (5 steps)
- `"documentDrafting"`: General document drafting (3 steps)

### Custom Step Labels

Step labels are defined in `frontend/src/locales/[lang]/chatProgress.js`:

```javascript
chatProgress: {
  types: {
    main: {
      step1: {
        label: "Generating section list",           // Active form
        labelNeutral: "Generate section list",     // Neutral/pending form
        labelFinished: "Generated section list",   // Completed form
        desc: "Using the main document to create an initial structure."
      }
      // ... more steps
    }
  }
}
```

The system automatically selects the appropriate label form based on step status:

- **Pending steps**: Use `labelNeutral` (infinitive form)
- **Active steps**: Use `label` (present continuous form)
- **Completed steps**: Use `labelFinished` (past participle form)

## Implementation Details

### Progress Store Structure

The progress store maintains the following structure for each thread:

```javascript
{
  isActive: boolean,
  isCompleted: boolean,
  currentStep: number,
  totalSteps: number,
  flowType: string,
  stepDetails: [
    {
      step: number,
      status: string,
      message: string,
      expectedTotal: number,
      subTasks: [
        {
          subStep: number,
          status: string,
          message: string,
          label: string,
          isPlaceholder: boolean
        }
      ]
    }
  ]
}
```

### Backend Integration

Backend processes should emit progress events in this format:

```javascript
writeResponseChunk(response, {
  uuid: chatId,
  type: "cdbProgress", // or "progress" for general progress
  flowType: "main",
  step: 2,
  subStep: 3,
  status: "complete",
  message: "Processing document: filename.pdf",
  expectedTotal: 5, // Expected number of sub-tasks for this step
  progress: 100, // Optional: explicit progress percentage
});
```

### Performance Considerations

- Progress calculations are memoized within React components
- The progress store uses efficient Map structures for thread storage
- Sub-task filtering is optimized to avoid unnecessary iterations
- Placeholder sub-tasks are filtered out during calculation to improve accuracy

## Testing

The system includes comprehensive tests covering:

- Basic progress calculation scenarios
- Sub-task completion tracking
- Placeholder sub-task handling
- Edge cases (zero steps, no sub-tasks, completion states)
- UI interaction and modal behavior

See `frontend/src/components/ChatProgress/__tests__/index.test.jsx` for detailed test coverage.

## Translations

UI strings for the modal, including titles and step names for both flows, should be managed in the locale files (e.g., `frontend/src/locales/en/common.js`). Ensure keys are descriptive and cover all steps for both `main` and `noMain` flows.

Example structure (illustrative):

```javascript
// In frontend/src/locales/en/common.js
{
  "doc_drafting_progress_modal": {
    "title": "Document Drafting Progress",
    "description": "Displays the real-time progress of document drafting tasks...",
    "steps": {
      "main_flow": {
        "step_1_name": "Initializing Main Draft...",
        "step_4_name": "Generating Sections from Main Document..."
        // ... other main flow steps
      },
      "no_main_flow": {
        "step_1_name": "Initializing Draft...",
        "step_3_name": "Generating Sections from Summaries..."
        // ... other no-main flow steps
      },
      "common_step_x_name": "Generating Legal Memos..." // For steps common to both
    }
  }
}
```

It is crucial to add equivalent keys in all supported locale files.

## Developer Tips

- Inspect progress events in your browser's developer tools (Network tab, usually under Fetch/XHR or WS/EventStream depending on the browser) to debug the data being sent from the backend.
- Use `useProgressStore.getState().forceCleanup(threadSlug)` to clear progress state for a specific thread during development or testing.
- Use `useProgressStore.getState().cleanupStaleProcesses()` to remove all stale progress states across all threads.

---
