# TipTapEditor Component

## Overview

The `TipTapEditor` is a rich text editor component built on top of Tiptap, providing a modern, extensible editing experience with markdown support and a comprehensive formatting toolbar.

## Features

- **Rich Text Formatting**: Bold, italic, strikethrough, inline code
- **Structured Content**: Headings (H1, H2, H3), lists, blockquotes
- **Markdown Support**: Bidirectional conversion between markdown and rich text
- **Keyboard Shortcuts**: Common shortcuts for formatting and actions
- **Responsive Design**: Works seamlessly across all device sizes
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Internationalization**: Full i18n support for all UI elements

## Usage

### Basic Usage

```jsx
import TipTapEditor from "@/components/ui/TipTapEditor";

function MyComponent() {
  const [content, setContent] = useState("");

  return (
    <TipTapEditor
      content={content}
      onChange={setContent}
      placeholder="Start typing..."
    />
  );
}
```

### Advanced Usage with Custom Styling

```jsx
<TipTapEditor
  content={markdownContent}
  onChange={handleContentChange}
  placeholder="Enter your text here..."
  className="custom-editor-class"
  toolbarClassName="custom-toolbar-class"
  showToolbar={true}
  disabled={false}
  autoFocus={true}
/>
```

## Props

| Prop               | Type       | Default             | Description                                                   |
| ------------------ | ---------- | ------------------- | ------------------------------------------------------------- |
| `content`          | `string`   | `""`                | Initial content as markdown string                            |
| `onChange`         | `function` | `undefined`         | Callback fired when content changes, receives markdown string |
| `placeholder`      | `string`   | `"Start typing..."` | Placeholder text displayed when editor is empty               |
| `className`        | `string`   | `""`                | Additional CSS classes for the editor container               |
| `toolbarClassName` | `string`   | `""`                | Additional CSS classes for the toolbar                        |
| `showToolbar`      | `boolean`  | `true`              | Whether to display the formatting toolbar                     |
| `disabled`         | `boolean`  | `false`             | Whether the editor is disabled                                |
| `autoFocus`        | `boolean`  | `false`             | Whether to automatically focus the editor on mount            |

## Toolbar Features

### Text Formatting

- **Bold** (`Ctrl+B`): Apply bold formatting
- **Italic** (`Ctrl+I`): Apply italic formatting
- **Strikethrough**: Apply strikethrough formatting
- **Inline Code**: Format text as inline code

### Structure

- **Heading 1**: Large heading format
- **Heading 2**: Medium heading format
- **Heading 3**: Small heading format

### Lists

- **Bullet List**: Create unordered lists
- **Numbered List**: Create ordered lists

### Content

- **Blockquote**: Format text as a quote
- **Horizontal Rule**: Insert a horizontal divider

### Actions

- **Undo** (`Ctrl+Z`): Undo last action
- **Redo** (`Ctrl+Y`): Redo last undone action

## Keyboard Shortcuts

| Shortcut | Action        |
| -------- | ------------- |
| `Ctrl+B` | Toggle bold   |
| `Ctrl+I` | Toggle italic |
| `Ctrl+Z` | Undo          |
| `Ctrl+Y` | Redo          |

## Styling

The editor uses Tailwind CSS classes and follows the project's design system:

- Toolbar buttons have hover and active states
- Active formatting is visually indicated
- Responsive design adapts to different screen sizes
- Dark/light mode compatibility

## Markdown Support

The editor seamlessly converts between markdown and rich text:

### Input

```markdown
# Heading 1

## Heading 2

**Bold text** and _italic text_

- List item 1
- List item 2
```

### Output

The markdown is rendered as formatted rich text and vice versa.

## Integration Examples

### Canvas Chat Integration

```jsx
// In Canvas Chat editing mode
{
  isEditing ? (
    <TipTapEditor
      content={editedResponse}
      onChange={(value) => setEditedResponse(value)}
      className="w-full min-h-[40rem]"
      placeholder={t("canvasChat.editAnswer")}
      autoFocus
    />
  ) : (
    <Markdown content={displayedContent} />
  );
}
```

## Testing

The component includes comprehensive unit tests:

```bash
npm test -- TipTapEditor
```

Tests cover:

- Component rendering
- Toolbar visibility
- Content changes
- Disabled state
- Custom styling
- Button functionality

## Accessibility

- All toolbar buttons have proper `title` attributes
- Keyboard navigation support
- Screen reader compatible
- Focus management
- ARIA labels for complex interactions

## Browser Support

- Modern browsers with ES2020 support
- Works with React 18.2.0+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Dependencies

- `@tiptap/react`: Core Tiptap React integration
- `@tiptap/starter-kit`: Basic editing functionality
- `@tiptap/extension-markdown`: Markdown conversion
- `@tiptap/extension-placeholder`: Placeholder support
- `@tiptap/extension-history`: Undo/redo functionality

## Troubleshooting

### Common Issues

1. **Content not updating**: Ensure `onChange` prop is provided and state is managed correctly
2. **Styling issues**: Check that Tailwind CSS is properly configured
3. **Markdown conversion**: Verify that content is valid markdown format

### Performance Considerations

- The editor is optimized for performance with efficient re-rendering
- Large documents (>10MB) may experience slower performance
- Use `useMemo` for complex content transformations

## Development

### Local Development

```bash
# Install dependencies
npm install

# Run tests
npm test -- TipTapEditor

# Build component
npm run build
```

### Adding New Extensions

To add new Tiptap extensions:

1. Install the extension package
2. Import and add to the `extensions` array in `TipTapEditor/index.jsx`
3. Add corresponding toolbar buttons if needed
4. Update tests and documentation

## Related Components

- `Markdown`: For displaying markdown content
- `Button`: Used in the toolbar
- `Tooltip`: For enhanced toolbar UX
