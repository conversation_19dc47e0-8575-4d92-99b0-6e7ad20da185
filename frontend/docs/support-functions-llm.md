# Support Functions LLM

## Overview

The Support Functions LLM feature allows system administrators to configure a centralized LLM provider that can be used for various supporting AI functions throughout the application. This provides better control over AI costs and model selection for specific operational functions.

## Location

**Settings Path:** General Settings → AI Providers → LLM for Support Functions

**URL:** `/general-settings/support-functions-llm`

**Access Level:** Admin only

## Features

### Central LLM Configuration

Administrators can select a dedicated LLM provider for support functions that is separate from the main chat LLM. This allows for:

- **Cost optimization** - Using different models for different purposes
- **Performance tuning** - Selecting appropriate models for specific tasks
- **Centralized management** - Single configuration point for all support functions

### Model Preference Support

The system supports provider-specific model preferences for support functions:

- **OpenAI Models** - Uses `OpenAiModelPref_SUPPORT` environment variable
- **Anthropic Models** - Uses `AnthropicModelPref_SUPPORT` environment variable
- **Gemini Models** - Uses `GeminiLLMModelPref_SUPPORT` environment variable

When a support function is enabled, it will use both the configured provider AND the specific model preference for that provider, ensuring precise control over which model is used for support tasks.

### Support Function Toggles

The system provides individual toggles for three key support functions:

1. **Prompt Upgrade**

   - When enabled, uses the support LLM for legal task prompt generation
   - When disabled, uses the existing `LLM_PROVIDER_PU` and `LLM_PROVIDER` fallback logic

2. **Validation**

   - When enabled, uses the support LLM for answer validation
   - When disabled, uses the existing validation LLM configuration (`LLMProvider_VA`)

3. **Manual Time Estimation**
   - When enabled, uses the support LLM for manual work time estimates
   - When disabled, uses the main system LLM (`LLM_PROVIDER`)

## Technical Implementation

### Frontend Components

- **Main Page:** `/frontend/src/pages/GeneralSettings/SupportFunctionsLLM/index.jsx`
- **Navigation:** Added to settings sidebar under AI Providers section
- **State Management:** Uses direct API calls with local state management
- **Model Selection:** Integrates with `BaseLLMPreference` component for model selection

### Backend Integration

- **Endpoint:** `POST /system/support-functions-llm`
- **Settings Storage:**
  - Environment variables: `LLM_PROVIDER_SUPPORT`, `OpenAiModelPref_SUPPORT`, `AnthropicModelPref_SUPPORT`, `GeminiLLMModelPref_SUPPORT`
  - Database settings: `supportFunctionPromptUpgrade`, `supportFunctionValidation`, `supportFunctionManualTime`
- **Utility Functions:** `/server/utils/helpers/supportFunctions.js`

### Function Integration

The feature integrates with three existing AI functions:

1. **generateLegalTaskPrompt** (`/server/endpoints/generateLegalTaskPrompt.js`)
2. **ValidateAnswer** (`/server/endpoints/system.js`)
3. **EstimateManualWork** (`/server/endpoints/system.js`)

Each function checks its corresponding toggle setting and uses the support LLM with the correct model preference when enabled.

## Configuration

### Prerequisites

- Admin user access
- At least one LLM provider configured in the system

### Setup Process

1. Navigate to General Settings → AI Providers → LLM for Support Functions
2. Select the desired LLM provider for support functions
3. Enable the toggles for the functions you want to use the support LLM
4. Save the configuration

### Fallback Behavior

When a support function toggle is disabled or the support LLM is unavailable:

- **Prompt Upgrade:** Falls back to `LLM_PROVIDER_PU` → `LLM_PROVIDER`
- **Validation:** Falls back to `LLMProvider_VA` setting or `LLM_PROVIDER`
- **Manual Time:** Falls back to `LLM_PROVIDER`

## Internationalization

The feature is fully internationalized with translation keys in:

- `frontend/src/locales/[lang]/supportFunctionsLLM.js`

Supported languages: English, French, Swedish, Kinyarwanda, German, Norwegian, Polish

## API Reference

### Get System Settings

```javascript
const settings = await System.keys();
// settings.LLMProvider_SUPPORT - The support LLM provider
// settings.OpenAiModelPref_SUPPORT - OpenAI model preference for support functions
// settings.AnthropicModelPref_SUPPORT - Anthropic model preference for support functions
// settings.GeminiLLMModelPref_SUPPORT - Gemini model preference for support functions
// settings.supportFunctionPromptUpgrade - Prompt upgrade toggle
// settings.supportFunctionValidation - Validation toggle
// settings.supportFunctionManualTime - Manual time toggle
```

### Update Support Functions

```javascript
const result = await System.setSupportFunctionsLLM({
  LLMProvider_SUPPORT: "openai",
  OpenAiModelPref_SUPPORT: "gpt-4o-mini", // Only sent when provider is "openai"
  supportFunctionPromptUpgrade: true,
  supportFunctionValidation: true,
  supportFunctionManualTime: false,
});
```

**Note:** Model preference parameters are only sent when they match the selected provider. For example, `OpenAiModelPref_SUPPORT` is only included when `LLMProvider_SUPPORT` is set to "openai".

## Security

- Admin-only access enforced by role-based middleware
- Settings validation through SystemSettings model
- Secure API endpoint with authentication requirements

## Monitoring

System logs include information about:

- Support function LLM selection decisions
- Fallback scenarios when support LLM is unavailable
- Configuration changes through event logging

## Troubleshooting

### Common Issues

1. **Support LLM not being used**

   - Verify the toggle is enabled for the specific function
   - Check that `LLM_PROVIDER_SUPPORT` is properly configured
   - Review server logs for initialization errors

2. **Settings not saving**

   - Ensure admin user permissions
   - Check network connectivity
   - Verify backend endpoint accessibility

3. **Fallback behavior unexpected**
   - Review the fallback chain documentation for each function
   - Check environment variable configuration
   - Verify existing LLM provider settings
