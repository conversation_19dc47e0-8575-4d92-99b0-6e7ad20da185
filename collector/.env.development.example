# Collector Environment Variables Example
# Copy this file to .env.development for local development with your actual API keys

# Node environment
NODE_ENV=development

# Storage directory for collector files
# For Docker/production: use server/storage/hotdir
# For development: use ../server/storage
STORAGE_DIR=../server/storage

# API Keys for Custom Chunking method Providers
# Jina AI API key for Jina provider
JINA_API_KEY=''

