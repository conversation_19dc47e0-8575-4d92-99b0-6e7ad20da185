/**
 * System News Items - Single Source of Truth
 *
 * This file contains all system news items and serves as the authoritative source
 * for both frontend and backend. The frontend fetches this data via API calls.
 */

const systemNewsItems = [
  {
    id: "system-welcome-2024",
    title: "Welcome to IST Legal Platform",
    content:
      "Welcome to our comprehensive legal platform designed to streamline your legal workflows. Explore our powerful features including document analysis, legal research, and AI-powered assistance to enhance your legal practice.",
    titleKey: "news-system-items.system-welcome-2024.title",
    contentKey: "news-system-items.system-welcome-2024.content",
    priority: "high",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    createdAt: "2024-01-01T00:00:00Z",
    expiresAt: null,
  },
  {
    id: "system-new-features-2025",
    title: "New System Functions Available",
    content:
      "We've added exciting new features to enhance your experience:\n\n- **Enhanced Document Analysis**: Improved AI-powered document processing\n- **Advanced Search**: Better search capabilities across your workspace\n- **Improved Performance**: Faster response times and better reliability\n- **New Templates**: Additional legal document templates\n\nExplore these features in your workspace settings.",
    titleKey: "news-system-items.system-new-features-2025.title",
    contentKey: "news-system-items.system-new-features-2025.content",
    priority: "medium",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    createdAt: "2025-01-01T00:00:00Z",
    expiresAt: null,
  },
  {
    id: "system-version-111-2025",
    title: "Platform Update 1.1.1 Available",
    content:
      "We've released version 1.1.1 with important improvements:\n\n- **Advanced Text Editing**: Enhanced text editing capabilities with improved formatting options and better user experience\n- **Updated AI Engine**: Upgraded to the latest Gemini AI version for better performance and more accurate responses\n- **Dark Mode Fixes**: Resolved various visual issues in dark mode for a more consistent and comfortable viewing experience\n\nThese improvements enhance the overall platform stability and user experience. All updates are automatically available to all users.",
    titleKey: "news-system-items.system-version-111-2025.title",
    contentKey: "news-system-items.system-version-111-2025.content",
    priority: "medium",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    createdAt: "2025-06-08T12:00:00Z",
    expiresAt: null,
  },
];

/**
 * Get all system news items
 * @returns {Array} All system news items
 */
function getAllSystemNews() {
  // Defensive copy – keep source-of-truth immutable from callers
  return [...systemNewsItems];
}

/**
 * Get only active system news items
 * @returns {Array} Active system news items
 */
function getActiveSystemNews() {
  const now = new Date();
  return systemNewsItems.filter((item) => {
    // Check if item is active
    if (!item.isActive) return false;

    // Check if item has expired
    if (item.expiresAt && new Date(item.expiresAt) < now) return false;

    return true;
  });
}

/**
 * Get system news item by ID
 * @param {string} id - News item ID
 * @returns {Object|null} News item or null if not found
 */
function getSystemNewsById(id) {
  return systemNewsItems.find((item) => item.id === id) || null;
}

/**
 * Get system news filtered by user roles
 * @param {Array} userRoles - Array of user roles
 * @returns {Array} Filtered system news items
 */
function getSystemNewsForRoles(userRoles = []) {
  return getActiveSystemNews().filter((item) => {
    // If no target roles specified, show to all users
    if (!item.targetRoles || item.targetRoles.length === 0) return true;

    // Check if user has any of the target roles
    return userRoles.some((role) => item.targetRoles.includes(role));
  });
}

/**
 * Get system news by priority
 * @param {string} priority - Priority level (urgent, high, medium, low)
 * @returns {Array} System news items with specified priority
 */
function getSystemNewsByPriority(priority) {
  return getActiveSystemNews().filter((item) => item.priority === priority);
}

/**
 * Get system news sorted by priority and date
 * @returns {Array} Sorted system news items
 */
function getSortedSystemNews() {
  const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };

  return getActiveSystemNews().sort((a, b) => {
    // Sort by priority first
    const priorityDiff =
      (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
    if (priorityDiff !== 0) return priorityDiff;

    // Then by creation date (newest first)
    return new Date(b.createdAt) - new Date(a.createdAt);
  });
}

/**
 * Resolve translation keys for system news items
 * @param {Object} newsItem - News item with translation keys
 * @param {Function} translationFunction - Translation function
 * @returns {Object} News item with resolved translations
 */
function resolveSystemNewsTranslations(newsItem, translationFunction) {
  return {
    ...newsItem,
    title: newsItem.titleKey
      ? translationFunction(newsItem.titleKey, newsItem.title)
      : newsItem.title,
    content: newsItem.contentKey
      ? translationFunction(newsItem.contentKey, newsItem.content)
      : newsItem.content,
  };
}

module.exports = {
  systemNewsItems,
  getAllSystemNews,
  getActiveSystemNews,
  getSystemNewsById,
  getSystemNewsForRoles,
  getSystemNewsByPriority,
  getSortedSystemNews,
  resolveSystemNewsTranslations,
};
