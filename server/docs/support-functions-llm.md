# Support Functions LLM - Server Implementation

## Overview

The Support Functions LLM feature provides a centralized way to configure LLM providers for specific AI support functions in the application. This server-side implementation manages the configuration, validation, and integration with existing AI functions.

## Architecture

### Configuration Management

The feature uses a hybrid approach for configuration storage:

1. **Environment Variable:** `LLM_PROVIDER_SUPPORT` - stores the selected LLM provider
2. **Model Preference Environment Variables:** Provider-specific model preferences with `_SUPPORT` suffix:
   - `OpenAiModelPref_SUPPORT` - OpenAI model preference for support functions
   - `AnthropicModelPref_SUPPORT` - Anthropic model preference for support functions
   - `GeminiLLMModelPref_SUPPORT` - Gemini model preference for support functions
3. **Database Settings:** Individual toggle settings stored in SystemSettings table
   - `supportFunctionPromptUpgrade`
   - `supportFunctionValidation`
   - `supportFunctionManualTime`

### Core Components

#### 1. SystemSettings Model Integration

**File:** `/server/models/systemSettings.js`

Added support for the new settings:

```javascript
// Supported fields
supportedFields: [
  // ... existing fields
  "supportFunctionPromptUpgrade",
  "supportFunctionValidation",
  "supportFunctionManualTime"
]

// Validation functions
validations: {
  supportFunctionPromptUpgrade: (value) => {
    return value === "true" || value === true ? "true" : "false";
  },
  supportFunctionValidation: (value) => {
    return value === "true" || value === true ? "true" : "false";
  },
  supportFunctionManualTime: (value) => {
    return value === "true" || value === true ? "true" : "false";
  }
}

// Current settings inclusion
currentSettings: {
  // ... existing settings
  supportFunctionPromptUpgrade: safeJsonParse(
    (await this.get({ label: "supportFunctionPromptUpgrade" }))?.value,
    "false"
  ),
  supportFunctionValidation: safeJsonParse(
    (await this.get({ label: "supportFunctionValidation" }))?.value,
    "false"
  ),
  supportFunctionManualTime: safeJsonParse(
    (await this.get({ label: "supportFunctionManualTime" }))?.value,
    "false"
  ),
  LLMProvider_SUPPORT: process.env.LLM_PROVIDER_SUPPORT
}
```

#### 2. API Endpoint

**File:** `/server/endpoints/system.js`

**Endpoint:** `POST /system/support-functions-llm`

**Access Control:** Admin only (`flexUserRoleValid([ROLES.admin])`)

**Request Body:**

```json
{
  "LLMProvider_SUPPORT": "openai",
  "OpenAiModelPref_SUPPORT": "gpt-4o-mini",
  "supportFunctionPromptUpgrade": true,
  "supportFunctionValidation": false,
  "supportFunctionManualTime": true
}
```

**Implementation:**

- Updates environment variables via `updateENV()` (provider and model preferences)
- Updates database settings via `SystemSettings.updateSettings()`
- Logs configuration changes via `EventLogs.logEvent()`
- Returns success/error response

**Model Preference Handling:**
The endpoint intelligently handles model preferences by only updating the model preference that matches the selected provider:

- When `LLMProvider_SUPPORT` is "openai", only `OpenAiModelPref_SUPPORT` is processed
- When `LLMProvider_SUPPORT` is "anthropic", only `AnthropicModelPref_SUPPORT` is processed
- When `LLMProvider_SUPPORT` is "gemini", only `GeminiLLMModelPref_SUPPORT` is processed

#### 3. Support Functions Utility

**File:** `/server/utils/helpers/supportFunctions.js`

Provides centralized logic for determining which LLM to use for support functions:

```javascript
/**
 * Get the appropriate LLM provider for a support function
 * @param {string} functionType - 'promptUpgrade', 'validation', 'manualTime'
 * @param {object|null} fallbackProvider - Fallback LLM provider instance
 * @returns {Promise<object|null>} - LLM provider instance or null
 */
async function getSupportFunctionLLM(functionType, fallbackProvider = null)
```

**Decision Logic:**

1. Check if the support function toggle is enabled in SystemSettings
2. If disabled, return the fallback provider
3. If enabled, try to initialize `LLM_PROVIDER_SUPPORT` with `_SUPPORT` settings suffix
4. If support provider fails, fall back to the provided fallback
5. Log decisions and warnings appropriately

**Key Implementation Detail:**
The support functions use the `settings_suffix: "_SUPPORT"` parameter when calling `getLLMProvider()`:

```javascript
const supportLLM = getLLMProvider({
  provider: supportProvider,
  settings_suffix: "_SUPPORT",
});
```

This ensures that the correct model preferences are used:

- `OpenAiModelPref_SUPPORT` for OpenAI providers
- `AnthropicModelPref_SUPPORT` for Anthropic providers
- `GeminiLLMModelPref_SUPPORT` for Gemini providers

**Specialized Functions:**

- `getPromptUpgradeLLM(fallbackProvider)`
- `getValidationLLM(fallbackProvider)`
- `getManualTimeLLM(fallbackProvider)`

## Function Integrations

### 1. Prompt Upgrade Integration

**File:** `/server/endpoints/generateLegalTaskPrompt.js`

**Modified Function:** `generateLegalTaskPrompt()`

**Integration Logic:**

```javascript
// Determine fallback using existing PU and default logic
let fallbackProvider = null;
// ... existing LLM_PROVIDER_PU and LLM_PROVIDER logic

// Check support function and use support LLM if enabled
llmProviderToUse = await getPromptUpgradeLLM(fallbackProvider);
```

**Fallback Chain:**

1. Support LLM (if toggle enabled and available)
2. `LLM_PROVIDER_PU` (existing logic)
3. `LLM_PROVIDER` (system default)

### 2. Validation Integration

**File:** `/server/endpoints/system.js`

**Modified Function:** `ValidateAnswer()`

**Integration Logic:**

```javascript
// Determine fallback using existing VA provider logic
let fallbackLLM = null;
// ... existing LLMProvider_VA setting logic

// Check support function and use support LLM if enabled
const LLMConnector = await getValidationLLM(fallbackLLM);
```

**Fallback Chain:**

1. Support LLM (if toggle enabled and available)
2. `LLMProvider_VA` setting with `_VA` suffix
3. System-standard provider (`LLM_PROVIDER`)

### 3. Manual Time Estimation Integration

**File:** `/server/endpoints/system.js`

**Modified Function:** `EstimateManualWork()`

**Integration Logic:**

```javascript
// Get fallback LLM using existing logic
const fallbackLLM = getLLMProvider({
  provider: process.env.LLM_PROVIDER || "openai",
});

// Check support function and use support LLM if enabled
const LLMConnector = await getManualTimeLLM(fallbackLLM);
```

**Fallback Chain:**

1. Support LLM (if toggle enabled and available)
2. `LLM_PROVIDER` (system default)

## Environment Variable Management

The `LLM_PROVIDER_SUPPORT` environment variable is managed through the existing `updateENV` system:

**Key Mapping:** Part of `KEY_MAPPING` in `/server/utils/helpers/updateENV.js`

**Validation:** Uses existing LLM provider validation logic

**Persistence:** Automatically persisted to `.env` file by the updateENV system

## Database Schema

The support function toggles are stored as individual settings in the `system_settings` table:

```sql
INSERT INTO system_settings (label, value) VALUES
('supportFunctionPromptUpgrade', 'false'),
('supportFunctionValidation', 'false'),
('supportFunctionManualTime', 'false');
```

**Data Type:** String values ("true"/"false")
**Default Value:** "false" (disabled by default)
**Validation:** Enforced by SystemSettings validation functions

## Event Logging

Configuration changes are logged through the EventLogs system:

```javascript
await EventLogs.logEvent(
  "support_functions_llm_updated",
  {
    llmProvider: LLMProvider_SUPPORT,
    promptUpgrade: supportFunctionPromptUpgrade,
    validation: supportFunctionValidation,
    manualTime: supportFunctionManualTime,
  },
  userId
);
```

## Error Handling

### Validation Errors

- Invalid LLM provider names are caught by existing validation
- Boolean toggle values are normalized to "true"/"false" strings
- Database constraints prevent invalid setting labels

### Runtime Errors

- LLM initialization failures are logged and trigger fallback behavior
- Missing environment variables are handled gracefully
- Database connection issues are caught and reported

### Logging

All decision points include appropriate logging:

- **Info:** When support LLM is successfully used
- **Warn:** When fallback occurs due to misconfiguration
- **Error:** When all LLM initialization attempts fail

## Security Considerations

### Access Control

- Admin-only endpoint access enforced by middleware
- User role validation before any configuration changes
- Request validation through `validatedRequest` middleware

### Input Validation

- LLM provider names validated against supported providers
- Boolean values normalized to prevent injection
- Setting labels validated against allowed list

### Environment Variable Security

- Environment variables managed through secure updateENV system
- No direct file system access in endpoint code
- Validation before environment updates

## Performance Considerations

### Caching

- SystemSettings values are cached where possible
- LLM provider instances are reused when appropriate
- Database queries minimized through efficient settings retrieval

### Initialization

- LLM providers are initialized lazily when needed
- Fallback chains prevent blocking on failed providers
- Async initialization prevents request blocking

### Resource Management

- Failed LLM connections are properly cleaned up
- Memory usage monitored for multiple provider instances
- Connection pooling handled by underlying LLM libraries

## Testing

### Unit Tests

Tests should cover:

- Support function helper logic
- Endpoint request/response handling
- SystemSettings integration
- Fallback behavior

### Integration Tests

Tests should verify:

- End-to-end configuration flow
- LLM provider switching
- Database persistence
- Environment variable updates

### Monitoring

Production monitoring should include:

- Support function usage metrics
- Fallback frequency
- LLM provider performance
- Configuration change audit trail

## Adding a New LLM Provider

To add a new LLM provider to the Support Functions feature, you need to update files in both the backend and frontend.

### 1. Backend Updates (`server/endpoints/system.js`)

In the `POST /system/support-functions-llm` endpoint:

- **Add the provider to `allowedProviders`**:
  ```javascript
  const allowedProviders = ["openai", "anthropic", "gemini", "new-provider"];
  ```
- **Handle the new provider's model preference**: Add an `else if` condition to process the new provider's model preference setting. This requires a new environment variable for the model name (e.g., `NewProviderModelPref_SUPPORT`).
  ```javascript
  // ... existing else if blocks
  } else if (
    LLMProvider_SUPPORT === "new-provider" &&
    NewProviderModelPref_SUPPORT !== undefined
  ) {
    envUpdates.NewProviderModelPref_SUPPORT = NewProviderModelPref_SUPPORT;
  }
  ```

### 2. Frontend Updates (`frontend/src/pages/GeneralSettings/SupportFunctionsLLM/index.jsx`)

- **Update `PROVIDER_MODEL_PREFERENCE_MAPPING`**: Add an entry for the new provider that maps it to its model preference key.
  ```javascript
  const PROVIDER_MODEL_PREFERENCE_MAPPING = {
    // ... existing providers
    "new-provider": "NewProviderModelPref_SUPPORT",
  };
  ```

### 3. Environment Variables (`.env.example`)

- **Add the new model preference variable**:
  ```
  # ...
  NewProviderModelPref_SUPPORT="default-model-for-new-provider"
  ```

## Error Handling

### Common Scenarios

// ... existing code ...
