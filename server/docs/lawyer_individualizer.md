# Lawyer Individualizer Feature Documentation

## Overview

The Lawyer Individualizer feature allows users to upload document samples to analyze their writing style and generate personalized style instructions. These instructions are then automatically applied to AI responses during document drafting and chat interactions, ensuring consistent writing style across all generated content.

## Table of Contents

1. [Feature Overview](#feature-overview)
2. [User Interface](#user-interface)
3. [Technical Architecture](#technical-architecture)
4. [API Endpoints](#api-endpoints)
5. [Database Schema](#database-schema)
6. [Configuration](#configuration)
7. [Customizing Style Generation Prompts](#customizing-style-generation-prompts)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)

## Feature Overview

### Core Functionality

1. **Document Upload**: Users can upload document samples (DOCX format) for style analysis
2. **Style Analysis**: LLM analyzes the document and generates personalized style instructions
3. **Profile Management**: Users can create, activate, and manage multiple style profiles
4. **Chat Integration**: Style instructions are automatically applied to AI responses when enabled
5. **Multi-language Support**: Full internationalization support for all UI elements

### User Workflow

1. User clicks "Personal Style Alignment" in the user dropdown menu
2. User uploads a document sample (DOCX format)
3. User clicks "Style Setting Generator" to analyze the document
4. System generates personalized style instructions using LLM
5. User can save the profile with a custom name
6. User can toggle style alignment on/off for chat responses
7. User can manage multiple profiles and set one as active

## User Interface

### StyleAlignmentModal Component

**Location**: `frontend/src/components/UserMenu/StyleAlignmentModal/index.jsx`

The main UI component provides:

- Document upload interface with drag-and-drop support
- Style generation button with loading states
- Profile management (create, activate, delete)
- Toggle for enabling/disabling style alignment
- List of existing style profiles with activation controls

### User Menu Integration

**Location**: `frontend/src/components/UserMenu/HeaderWorkspace/index.jsx`

The feature is accessible via:

- User dropdown menu → "Personal Style Alignment"
- Translation key: `user-menu.style-alignment`

### State Management

**Location**: `frontend/src/stores/userStore.js`

Zustand store manages:

```javascript
{
  styleAlignmentEnabled: boolean,
  styleProfiles: Array<StyleProfile>,
  activeStyleProfileId: string|null,
  // ... methods for profile management
}
```

## Technical Architecture

### Frontend Components

```
StyleAlignmentModal/
├── index.jsx              # Main modal component
└── __tests__/
    └── StyleAlignmentModal.test.jsx  # Component tests
```

### Backend Components

```
server/
├── endpoints/system.js    # Style generation endpoint
├── models/userStyleProfile.js  # Database model
└── docs/lawyer_individualizer.md  # This documentation
```

### Database Integration

- **Table**: `user_style_profiles`
- **Model**: `UserStyleProfile`
- **Relations**: Linked to users with CASCADE DELETE

### Chat Integration Points

1. **Frontend**: `frontend/src/models/workspace.js` - Sends style data with chat requests
2. **Backend**: `server/endpoints/chat.js` - Extracts style alignment data
3. **Chat Processing**: `server/utils/chats/` - Applies style instructions to prompts

## API Endpoints

### POST /system/generate-style-profile

Generates style instructions from uploaded document content.

**Request Body**:

```json
{
  "documentContent": "string" // Extracted text from uploaded document
}
```

**Response**:

```json
{
  "success": true,
  "styleInstructions": "string" // Generated style instructions
}
```

**Error Response**:

```json
{
  "success": false,
  "error": "string" // Error message
}
```

### UserStyleProfile Model Methods

**Location**: `server/models/userStyleProfile.js`

Available methods:

- `create(userId, profileData)` - Create new style profile
- `getByUserId(userId)` - Get all profiles for user
- `getActiveProfile(userId)` - Get user's active profile
- `update(profileId, updates)` - Update existing profile
- `delete(profileId)` - Delete profile
- `activate(userId, profileId)` - Set profile as active

## Database Schema

### user_style_profiles Table

```sql
CREATE TABLE user_style_profiles (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  instructions TEXT NOT NULL CHECK (LENGTH(instructions) <= 10000),
  is_active BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_user_style_profiles_user_id ON user_style_profiles(user_id);
CREATE INDEX idx_user_style_profiles_active ON user_style_profiles(user_id, is_active);
```

### Prisma Migration

The database schema is managed through Prisma migrations. The UserStyleProfile table is created by the migration:

**Migration File**: `server/prisma/migrations/20250528002620_add_user_style_profiles/migration.sql`

To apply the migration in a new environment:

```bash
cd server
npx prisma migrate deploy
```

To generate the Prisma client after schema changes:

```bash
cd server
npx prisma generate
```

### Constraints and Validation

- **Character Limit**: Style instructions limited to 10,000 characters
- **User Ownership**: All operations validate user ownership
- **Active Profile**: Only one profile can be active per user
- **Cascade Delete**: Profiles deleted when user is deleted

## Configuration

### Environment Variables

The feature uses the existing LLM configuration:

- `LLM_PROVIDER` - Primary LLM provider for style analysis
- `LLM_MODEL` - Model to use for style generation

### System Settings

No additional system settings required. The feature uses existing LLM infrastructure.

### File Upload Configuration

Document upload handled by existing DOCX processing pipeline:

- **Supported Formats**: DOCX
- **File Size Limits**: Configured in collector service
- **Processing**: Uses existing document extraction utilities

## Customizing Style Generation Prompts

### Current Prompt Location

The style generation prompt is defined in:
**File**: `server/endpoints/system.js`
**Function**: `/system/generate-style-profile` endpoint handler

### Default Prompt Structure

```javascript
const styleAnalysisPrompt = `You are a legal writing style analyzer. Analyze the provided document and create detailed style instructions that can be used to emulate the writing style in future documents.

Focus on:
1. Tone and formality level
2. Sentence structure and length patterns
3. Vocabulary choices and legal terminology usage
4. Paragraph organization and flow
5. Use of citations and references
6. Specific phrases or expressions commonly used
7. Document structure preferences

Provide clear, actionable instructions that another AI can follow to write in a similar style.

Document to analyze:
${documentContent}

Generate comprehensive style instructions:`;
```

### Customization Options

#### Option 1: Direct Code Modification

**File**: `server/endpoints/system.js`
**Location**: Around line 4520 in the `/system/generate-style-profile` endpoint

```javascript
// Modify this section to customize the prompt
const styleAnalysisPrompt = `Your custom prompt here...

Document to analyze:
${documentContent}

Generate comprehensive style instructions:`;
```

#### Option 2: Environment Variable Configuration

Add environment variable support by modifying the endpoint:

```javascript
// Add to server/endpoints/system.js
const styleAnalysisPrompt =
  process.env.STYLE_ANALYSIS_PROMPT || `Default prompt here...`;
```

Then set in your `.env` file:

```bash
STYLE_ANALYSIS_PROMPT="Your custom style analysis prompt..."
```

#### Option 3: System Settings Integration

For dynamic configuration through the admin interface:

1. **Add System Setting**:

```javascript
// In the endpoint handler
const customPrompt = await SystemSettings.getValueOrFallback(
  { label: "style_analysis_prompt" },
  defaultStyleAnalysisPrompt
);
```

2. **Create Admin Interface**:
   Add to system settings page for administrators to modify the prompt.

### Prompt Customization Guidelines

#### Effective Prompt Elements

1. **Clear Role Definition**: Define the AI's role as a style analyzer
2. **Specific Focus Areas**: List what aspects of style to analyze
3. **Output Format**: Specify how instructions should be structured
4. **Context Preservation**: Include the document content appropriately

#### Example Custom Prompts

**For Legal Documents**:

```
You are a legal writing style expert. Analyze this document and create style instructions focusing on:
1. Legal citation format and frequency
2. Use of legal terminology and Latin phrases
3. Sentence complexity and clause structure
4. Paragraph organization and logical flow
5. Tone (formal, persuasive, analytical)
6. Use of headings and document structure

Create actionable style guidelines that will help maintain consistency in legal writing.
```

**For Business Documents**:

```
Analyze this business document's writing style and create instructions covering:
1. Professional tone and formality level
2. Use of business terminology and jargon
3. Sentence length and readability
4. Bullet point and list formatting preferences
5. Executive summary and conclusion styles
6. Data presentation and analysis approach

Provide clear guidelines for maintaining this professional writing style.
```

### LLM Configuration for Style Analysis

#### Model Selection

The style generation uses the system's default LLM provider. For optimal results:

- **Recommended Models**: GPT-4, Claude-3, or other advanced language models
- **Temperature**: Set to 0.3 for consistent analysis (currently configured)
- **Max Tokens**: 2000 tokens (currently configured)

#### Provider-Specific Considerations

**OpenAI GPT Models**:

- Excellent at style analysis and instruction generation
- Good understanding of legal and business writing conventions

**Anthropic Claude**:

- Strong analytical capabilities
- Good at generating detailed, actionable instructions

**Local Models**:

- May require more detailed prompts
- Consider increasing max tokens for comprehensive analysis

## Testing

### Test Coverage

The feature includes comprehensive test suites:

#### Frontend Tests

**Location**: `frontend/src/components/UserMenu/StyleAlignmentModal/__tests__/`

- Component rendering and interaction tests
- State management validation
- Error handling scenarios

#### Backend Tests

**Location**: `server/tests/`

- **Unit Tests**: `unit/endpoints/system.test.js` - API endpoint testing
- **Model Tests**: `unit/models/userStyleProfile.test.js` - Database operations
- **Integration Tests**: `integration/chat-style-alignment.test.js` - End-to-end functionality

#### Running Tests

```bash
# Frontend tests
cd frontend
npm test -- --testPathPattern="StyleAlignmentModal"

# Backend tests
cd server
npm test -- --testPathPattern="(system|userStyleProfile|chat-style-alignment)"
```

### Test Data

Example test documents and expected style instructions are included in test files for validation.

## Troubleshooting

### Common Issues

#### 1. Style Generation Fails

**Symptoms**: Error message "Failed to generate style instructions"

**Possible Causes**:

- LLM provider not configured
- Document content too short or empty
- LLM API rate limits exceeded

**Solutions**:

- Verify LLM provider configuration in environment variables
- Ensure document has sufficient content (minimum 100 words recommended)
- Check LLM provider API status and rate limits

#### 2. Style Not Applied in Chat

**Symptoms**: Chat responses don't reflect the selected style

**Possible Causes**:

- Style alignment toggle disabled
- No active style profile selected
- Chat system not receiving style data

**Solutions**:

- Verify style alignment is enabled in user settings
- Ensure a style profile is set as active
- Check browser console for JavaScript errors

#### 3. Document Upload Issues

**Symptoms**: Cannot upload DOCX files

**Possible Causes**:

- Collector service offline
- File format not supported
- File size too large

**Solutions**:

- Check collector service status
- Verify file is in DOCX format
- Reduce file size if necessary

### Debug Information

#### Frontend Debugging

Enable debug logging in browser console:

```javascript
// In browser console
localStorage.setItem("debug", "style-alignment");
```

#### Backend Debugging

Check server logs for style generation requests:

```bash
# Look for log entries containing "style generation"
tail -f server/logs/application.log | grep "style"
```

### Performance Considerations

#### Style Generation Performance

- **Average Processing Time**: 10-30 seconds depending on document size and LLM provider
- **Recommended Document Size**: 1-10 pages for optimal analysis
- **Concurrent Requests**: Limited by LLM provider rate limits

#### Database Performance

- Indexes on `user_id` and `is_active` columns optimize query performance
- Style instructions limited to 10,000 characters to prevent database bloat

## Security Considerations

### Data Protection

1. **User Isolation**: Users can only access their own style profiles
2. **Input Validation**: All inputs validated and sanitized
3. **File Processing**: Documents processed securely through existing pipeline

### Privacy

1. **Document Content**: Uploaded documents processed temporarily and not permanently stored
2. **Style Instructions**: Stored securely in user's profile data
3. **LLM Processing**: Document content sent to configured LLM provider for analysis

### Access Control

1. **Authentication Required**: All endpoints require valid user session
2. **Ownership Validation**: All profile operations validate user ownership
3. **Role-Based Access**: No special permissions required beyond standard user access

## Integration with Other Features

### Document Drafting

Style instructions are automatically applied during:

- Legal Q&A chat sessions
- Document drafting workflows
- Template-based document generation

### Workspace Integration

- Style profiles are user-specific, not workspace-specific
- Style alignment works across all user's workspaces
- Can be toggled on/off per conversation

### Multi-language Support

The feature supports all system languages:

- English, French, Swedish, German, Norwegian, Polish, Kinyarwanda
- Translation keys in `modalRelatedKeys.js`
- Style instructions generated in the language of the uploaded document

## Future Enhancements

### Potential Improvements

1. **Multiple Document Analysis**: Analyze multiple documents to create comprehensive style profiles
2. **Style Comparison**: Compare different writing styles and suggest improvements
3. **Team Style Profiles**: Share style profiles across organization members
4. **Style Templates**: Pre-built style profiles for common legal document types
5. **Advanced Analytics**: Detailed metrics on style consistency and application

### API Extensions

1. **Batch Processing**: Analyze multiple documents simultaneously
2. **Style Validation**: Validate generated content against style guidelines
3. **Style Metrics**: Quantitative analysis of style adherence

## Conclusion

The Lawyer Individualizer feature provides a powerful tool for maintaining consistent writing style across AI-generated content. With proper configuration and usage, it can significantly improve the quality and consistency of legal document generation.

For additional support or feature requests, please refer to the project's issue tracking system or contact the development team.
