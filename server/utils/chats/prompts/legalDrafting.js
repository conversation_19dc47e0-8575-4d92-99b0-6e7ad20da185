/**
 * Default prompts for legal document drafting
 *
 * This module centralizes all prompts used in the document drafting flows,
 * both for the main document flow and the no-main-document flow.
 *
 * DEVELOPER NOTE: When adding a new prompt that will be configurable via a system setting:
 * 1. Ensure the `systemSettingName` (e.g., "cdb_new_prompt_system_prompt") is added to
 *    `SystemSettings.protectedFields` (or `supportedFields` if appropriate) in `server/models/systemSettings.js`.
 * 2. If the setting requires specific validation (e.g., number, boolean), add a validator
 *    to `SystemSettings.validations` in `server/models/systemSettings.js`.
 * 3. Ensure the new setting key is retrievable via `SystemSettings.currentSettings()`
 *    (and other relevant settings aggregation methods like `llmPreferenceKeys` if applicable)
 *    in `server/models/systemSettings.js` so it can be managed in the UI.
 * 4. The `generateSystemSettingPKeyForLegalDrafting` helper can be used to create consistent
 *    `systemSettingName` values (e.g., `cdb_yourpromptkey_system_prompt`).
 *    The test suite in `server/utils/chats/prompts/__tests__/legalDrafting.test.js` will automatically
 *    verify that this `systemSettingName` is a known key in `SystemSettings`.
 *
 * INTERNATIONALIZATION (i18n) NOTE:
 * When adding or modifying entries in `exportedLegalPrompts` for the Document Builder page:
 * - For `GROUP_TITLE` and `GROUP_DESCRIPTION` entries:
 *   - The `label` field (e.g., "document-builder.prompts.group.document_summary.title") is the translation key.
 *   - The `defaultContent` field provides the English translation for this key.
 * - For `SYSTEM_PROMPT`, `USER_PROMPT`, and `PROMPT_TEMPLATE` entries:
 *   - The `label` field (e.g., "document-builder.prompts.document-summary-system-label") is the translation key for the prompt's title/label on the UI.
 *     Its English text should be the `defaultContent` of this entry.
 *   - The `description` field (e.g., "document-builder.prompts.document-summary-system-description") is the translation key for the prompt's help text/description on the UI.
 *     Its English text should be a human-readable explanation of the prompt.
 * - All these translation keys (derived from `label` and `description` fields) MUST be added to all frontend locale files
 *   (e.g., `frontend/src/locales/en/common.js`, `frontend/src/locales/sv/common.js`, etc.) with their respective translations.
 */

// ======================================================================
// SHARED PROMPTS - Used by both flows
// ======================================================================

// Document description/summary prompt
const DEFAULT_DOCUMENT_SUMMARY = {
  SYSTEM_PROMPT:
    "You are an expert at analyzing legal documents and providing concise descriptions. Make a detailed summary of the document content and how it is relevant to the legal task. Write the summary in the same language as the legal task.",
  USER_PROMPT: `Generate a detailed summary of the following document content in relation to the legal task "{{task}}". Focus on the main topics and key information:\n\n{{content}}`,
};

// Document relevance check prompt
const DEFAULT_DOCUMENT_RELEVANCE = {
  SYSTEM_PROMPT:
    "You are an expert at evaluating whether a document is relevant to a legal task. Answer strictly with true or false. Respond in the same language used in the legal task prompt.",
  USER_PROMPT: `For the legal task "{{task}}", is the following document content relevant? Answer strictly "true" or "false":\n\n{{content}}`,
};

// Section drafting prompt
const DEFAULT_SECTION_DRAFTING = {
  SYSTEM_PROMPT:
    "You are a complex document builder for legal tasks: generate one section at a time in professional legal style, using only the specified documents and respecting the flow of neighboring sections. Write in the same language as the legal task.",
  USER_PROMPT: `Generate section {{sectionNumber}} titled "{{title}}" for the legal task "{{task}}". Use content from these documents: {{docs}}. Also consider these other sections: {{neighborContext}}. Output only the content of this section.`,
};

// Section legal issues identification prompt
const DEFAULT_SECTION_LEGAL_ISSUES = {
  SYSTEM_PROMPT:
    'You are an expert legal analyst. Your task is to identify specific legal topics for which factual information should be retrieved to support drafting the given section. Focus on clarifying legal context, not solving the overall task. Use the same language as the legal task. For each topic, suggest the most relevant Legal-QA workspace slug. The topics should be relevant to the section and legal task and aim to keep memo retrieval concise, i.e. try to keep the number of topics at a necessary minimum.\n\nReturn your answer strictly as valid JSON. Example output:\n[\n  {"Issue": "Jurisdiction for the current case", "WORKSPACE_SLUG_FOR_LEGALDATA": "civil-procedure"},\n  {"Issue": "Contract formation", "WORKSPACE_SLUG_FOR_LEGALDATA": "contracts"}\n]',
  USER_PROMPT: `For section {{sectionNumber}} titled \"{{title}}\" of the legal task \"{{task}}\", and considering the relevant documents: {{docs}}, list the specific legal topics or data points for which neutral background information (e.g., definitions, statute summaries, key case principles) should be fetched from a Legal-QA database. Do not try to solve the task itself.\n\nReturn ONLY the JSON array described above (no markdown/code fences) where each element contains an \"Issue\" and a \"WORKSPACE_SLUG_FOR_LEGALDATA\" indicating the best-matching workspace slug from the list provided in the user prompt.`,
};

// Legal memo creation prompt
const DEFAULT_MEMO_CREATION = {
  PROMPT_TEMPLATE:
    'Create a legal memorandum addressing the legal issue "{{issue}}" in the same language as the legal task \"{{task}}\. In the analysis, consider the following documents (reference them by name where relevant): {{docs}}. Include extensive legal analysis, jurisprudence, references to relevant legal sections in applicable legislation analysis in a structured format. The purpose of the memo is to provide the local legal context for the continued drafting of part of a response stemming fromthe legal task',
};

// Document-to-section index mapping prompt
const DEFAULT_SECTION_INDEX = {
  SYSTEM_PROMPT: `You are a legal analyst. Decide which sections are supported by the given document content.
Return ONLY a JSON array of section index numbers (integers). No markdown fences.
Example output: [1,3,5]
Ensure any explanatory text (if unavoidable) is in the same language as the legal task.`,
};

// ======================================================================
// MAIN DOCUMENT FLOW PROMPTS - Used only when there's a main document
// ======================================================================

// Main document selection prompt
const DEFAULT_SELECT_MAIN_DOCUMENT = {
  SYSTEM_PROMPT:
    "You are an expert legal assistant who selects the primary document among a set of summaries. Provide only the exact document name. Respond in the same language as the legal task.",
  USER_PROMPT: `{{summaries}}\n\nGiven the summaries of documents for the legal task "{{task}}", which document is the main document? Return only the exact Doc Name.`,
};

// Section list generation from main document content
const DEFAULT_SECTION_LIST_FROM_MAIN = {
  SYSTEM_PROMPT: `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided main document content and legal task.
Return ONLY the JSON array (no markdown fences) where each element has:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_circumstances_to_comment_on": array<string>
  - "legal_issues_to_address": array<string>

Example:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_circumstances_to_comment_on": ["Court order", "Parties"],
    "legal_issues_to_address": ["Jurisdiction", "Timeliness"]
  }
]
Use the same language as the legal task for all free-text fields (title, description, circumstances, issues).`,
  USER_PROMPT: `Legal Task: {{task}}\n\nDocument Content:\n{{content}}`,
};

// ======================================================================
// NO MAIN DOCUMENT FLOW PROMPTS - Used when there's no main document
// ======================================================================

// Section list generation from summaries (specific to no-main flow)
const DEFAULT_SECTION_LIST_FROM_SUMMARIES = {
  SYSTEM_PROMPT: `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided document summaries and legal task.
Return ONLY the JSON array (no markdown fences) where each element has:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_documents": array<string>
  - "legal_issues_to_address": array<string>

Example:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_documents": ["document1.pdf", "document2.docx"],
    "legal_issues_to_address": ["Jurisdiction", "Timeliness"]
  }
]
Use the same language as the legal task for all free-text fields.`,
  USER_PROMPT: `Legal Task: {{task}}\n\nDocument Summaries:\n{{summaries}}\n\nBased on these document summaries, identify appropriate sections for a comprehensive legal document that addresses the task. Return only a JSON array with the structure specified.`,
};

// After the DEFAULT_SECTION_LIST_FROM_SUMMARIES constant, add new prompt constants
const DEFAULT_REFERENCE_FILES_DESCRIPTION = {
  SYSTEM_PROMPT:
    "You are a legal expert specialized in regulatory compliance. This is a reference file containing rules and regulations. Provide a concise summary of the rules in this file in relation to the legal task. Write the summary in the same language as the legal task.",
  USER_PROMPT:
    'For the legal task "{{task}}", summarize the following reference file content:\n\n{{content}}',
};

const DEFAULT_REVIEW_FILES_DESCRIPTION = {
  SYSTEM_PROMPT:
    "You are a legal expert specialized in compliance review. This is a review file from the provided corpus. Compare its content against the reference rules and indicate any potential breaches or compliance points. Write your analysis in the same language as the legal task.",
  USER_PROMPT:
    'For the legal task "{{task}}", analyze the following review file content in relation to the rules:\n\n{{content}}',
};

const DEFAULT_REFERENCE_REVIEW_SECTIONS = {
  SYSTEM_PROMPT: `You are an expert legal assistant. Draft a JSON-structured list of sections for a legal document based on the provided document summaries and legal task.
Return ONLY the JSON array (no markdown fences) where each element has:
  - "index_number": integer
  - "title": string
  - "description": string
  - "relevant_documents": array<string>

Example:
[
  {
    "index_number": 1,
    "title": "Introduction",
    "description": "Purpose of the submission and background.",
    "relevant_documents": ["document1.pdf", "document2.docx"],
  }
]

Use the same language as the legal task for all free-text fields (title, description). Each section's relevant_documents array must contain at least one reference file and any review files that contain violations. If no violations are found in review files, include relevant review files that provide context or demonstrate compliance with the reference standards.`,
  USER_PROMPT: "Legal Task: {{task}}\n\nDocument Summaries:\n{{summaries}}",
};

// Compliance section drafting prompt for reference files flow
const DEFAULT_REFERENCE_SECTION_DRAFTING = {
  SYSTEM_PROMPT:
    "You are a legal expert tasked with drafting a section of a compliance report. Generate one section at a time in professional legal style, comparing review documents against reference rules to identify breaches or compliance issues. Write in the same language as the legal task.",
  USER_PROMPT: `Draft section {{sectionNumber}} titled "{{title}}" for the legal task "{{task}}".

Section Description: {{description}}

Relevant Documents: {{docs}}

Previously Drafted Sections: {{previousSections}}

Instructions: Focus on comparing the review documents against the reference rules and highlighting any compliance issues or breaches, with specific references. Draft ONLY the content for this section. Do NOT include the title in your response, as it will be added automatically. Ensure it flows logically from the previous content and directly addresses the section's description and the overall legal task.`,
};

// Helper function to generate system setting PKeys
// Adapted from server/utils/chats/helpers/promptManager.js to avoid circular deps
function generateSystemSettingPKeyForLegalDrafting(defaultPromptKey, fieldKey) {
  const baseName = defaultPromptKey.replace(/^DEFAULT_/, "").toLowerCase();
  return `cdb_${baseName}_${fieldKey.toLowerCase()}`;
}

/**
 * @typedef {Object} ExportedLegalPrompt
 * @property {string} promptKey - Identifier for the prompt group (e.g., "DEFAULT_DOCUMENT_SUMMARY").
 * @property {'GROUP_TITLE'|'GROUP_DESCRIPTION'|'SYSTEM_PROMPT'|'USER_PROMPT'|'PROMPT_TEMPLATE'} promptField - The specific field this entry represents.
 * @property {string} label - Translation key for the UI label/title.
 * @property {string} defaultContent - Default English text. For GROUP_TITLE/GROUP_DESCRIPTION, this is the translation for `label`. For actual prompts, this is the prompt content itself.
 * @property {string} [systemSettingName] - Optional. The system setting name if this prompt is configurable.
 * @property {string} [description] - Optional. Translation key for the UI description/help text (primarily for SYSTEM_PROMPT, USER_PROMPT, PROMPT_TEMPLATE).
 */

/**
 * Array of prompt configurations exported for use in the Document Builder settings page and other parts of the system.
 *
 * When adding new entries:
 * - For `GROUP_TITLE` and `GROUP_DESCRIPTION`:
 *   - `label` is the translation key. `defaultContent` is its English text.
 * - For `SYSTEM_PROMPT`, `USER_PROMPT`, `PROMPT_TEMPLATE`:
 *   - `label` is the translation key for the UI title. `defaultContent` is the actual prompt text.
 *   - `description` is the translation key for the UI help text.
 * - ALL translation keys (from `label` and `description` fields) must be added to all frontend locale files.
 * @type {ExportedLegalPrompt[]}
 */
const exportedLegalPrompts = [
  {
    promptKey: "DEFAULT_DOCUMENT_SUMMARY",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.document_summary.title",
    defaultContent: "Document Summary Prompts",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_SUMMARY",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.document_summary.description",
    defaultContent: "Configure system and user prompts for Document Summary.",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_SUMMARY",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.document-summary-system-label",
    defaultContent: DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_DOCUMENT_SUMMARY",
      "SYSTEM_PROMPT"
    ),
    description: "document-builder.prompts.document-summary-system-description",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_SUMMARY",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.document-summary-user-label",
    defaultContent: DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_DOCUMENT_SUMMARY",
      "USER_PROMPT"
    ),
    description: "document-builder.prompts.document-summary-user-description",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_RELEVANCE",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.document_relevance.title",
    defaultContent: "Document Relevance Prompts",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_RELEVANCE",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.document_relevance.description",
    defaultContent: "Configure system and user prompts for Document Relevance.",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_RELEVANCE",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.document-relevance-system-label",
    defaultContent: DEFAULT_DOCUMENT_RELEVANCE.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_DOCUMENT_RELEVANCE",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.document-relevance-system-description",
  },
  {
    promptKey: "DEFAULT_DOCUMENT_RELEVANCE",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.document-relevance-user-label",
    defaultContent: DEFAULT_DOCUMENT_RELEVANCE.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_DOCUMENT_RELEVANCE",
      "USER_PROMPT"
    ),
    description: "document-builder.prompts.document-relevance-user-description",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFTING",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_drafting.title",
    defaultContent: "Section Drafting Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFTING",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.section_drafting.description",
    defaultContent: "Configure system and user prompts for Section Drafting.",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFTING",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-drafting-system-label",
    defaultContent: DEFAULT_SECTION_DRAFTING.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_DRAFTING",
      "SYSTEM_PROMPT"
    ),
    description: "document-builder.prompts.section-drafting-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_DRAFTING",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-drafting-user-label",
    defaultContent: DEFAULT_SECTION_DRAFTING.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_DRAFTING",
      "USER_PROMPT"
    ),
    description: "document-builder.prompts.section-drafting-user-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LEGAL_ISSUES",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_legal_issues.title",
    defaultContent: "Section Legal Issues Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_LEGAL_ISSUES",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.section_legal_issues.description",
    defaultContent:
      "Configure system and user prompts for Section Legal Issues.",
  },
  {
    promptKey: "DEFAULT_SECTION_LEGAL_ISSUES",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-legal-issues-system-label",
    defaultContent: DEFAULT_SECTION_LEGAL_ISSUES.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LEGAL_ISSUES",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.section-legal-issues-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LEGAL_ISSUES",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-legal-issues-user-label",
    defaultContent: DEFAULT_SECTION_LEGAL_ISSUES.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LEGAL_ISSUES",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.section-legal-issues-user-description",
  },
  {
    promptKey: "DEFAULT_MEMO_CREATION",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.memo_creation.title",
    defaultContent: "Memo Creation Prompts",
  },
  {
    promptKey: "DEFAULT_MEMO_CREATION",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.memo_creation.description",
    defaultContent: "Configure prompts for Memo Creation.",
  },
  {
    promptKey: "DEFAULT_MEMO_CREATION",
    promptField: "PROMPT_TEMPLATE",
    label: "document-builder.prompts.memo-creation-template-label",
    defaultContent: DEFAULT_MEMO_CREATION.PROMPT_TEMPLATE,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_MEMO_CREATION",
      "PROMPT_TEMPLATE"
    ),
    description: "document-builder.prompts.memo-creation-template-description",
  },
  {
    promptKey: "DEFAULT_SECTION_INDEX",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_index.title",
    defaultContent: "Section Index Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_INDEX",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.section_index.description",
    defaultContent: "Configure prompts for Section Index.",
  },
  {
    promptKey: "DEFAULT_SECTION_INDEX",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-index-system-label",
    defaultContent: DEFAULT_SECTION_INDEX.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_INDEX",
      "SYSTEM_PROMPT"
    ),
    description: "document-builder.prompts.section-index-system-description",
  },
  // Main Document Flow Prompts
  {
    promptKey: "DEFAULT_SELECT_MAIN_DOCUMENT",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.select_main_document.title",
    defaultContent: "Select Main Document Prompts",
  },
  {
    promptKey: "DEFAULT_SELECT_MAIN_DOCUMENT",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.select_main_document.description",
    defaultContent:
      "Configure system and user prompts for Select Main Document.",
  },
  {
    promptKey: "DEFAULT_SELECT_MAIN_DOCUMENT",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.select-main-document-system-label",
    defaultContent: DEFAULT_SELECT_MAIN_DOCUMENT.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SELECT_MAIN_DOCUMENT",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.select-main-document-system-description",
  },
  {
    promptKey: "DEFAULT_SELECT_MAIN_DOCUMENT",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.select-main-document-user-label",
    defaultContent: DEFAULT_SELECT_MAIN_DOCUMENT.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SELECT_MAIN_DOCUMENT",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.select-main-document-user-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_MAIN",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_list_from_main.title",
    defaultContent: "Section List From Main Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_MAIN",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.section_list_from_main.description",
    defaultContent:
      "Configure system and user prompts for Section List From Main.",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_MAIN",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-list-from-main-system-label",
    defaultContent: DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LIST_FROM_MAIN",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.section-list-from-main-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_MAIN",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-list-from-main-user-label",
    defaultContent: DEFAULT_SECTION_LIST_FROM_MAIN.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LIST_FROM_MAIN",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.section-list-from-main-user-description",
  },
  // No Main Document Flow Prompts
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.section_list_from_summaries.title",
    defaultContent: "Section List From Summaries Prompts",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.section_list_from_summaries.description",
    defaultContent:
      "Configure system and user prompts for Section List From Summaries.",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.section-list-from-summaries-system-label",
    defaultContent: DEFAULT_SECTION_LIST_FROM_SUMMARIES.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.section-list-from-summaries-system-description",
  },
  {
    promptKey: "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.section-list-from-summaries-user-label",
    defaultContent: DEFAULT_SECTION_LIST_FROM_SUMMARIES.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.section-list-from-summaries-user-description",
  },
  // Reference Files Description Prompts
  {
    promptKey: "DEFAULT_REFERENCE_FILES_DESCRIPTION",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.reference_files_description.title",
    defaultContent: "Reference Files Description Prompts",
  },
  {
    promptKey: "DEFAULT_REFERENCE_FILES_DESCRIPTION",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.reference_files_description.description",
    defaultContent:
      "Configure system and user prompts for Reference Files Description.",
  },
  {
    promptKey: "DEFAULT_REFERENCE_FILES_DESCRIPTION",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.reference-files-description-system-label",
    defaultContent: DEFAULT_REFERENCE_FILES_DESCRIPTION.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_FILES_DESCRIPTION",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.reference-files-description-system-description",
  },
  {
    promptKey: "DEFAULT_REFERENCE_FILES_DESCRIPTION",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.reference-files-description-user-label",
    defaultContent: DEFAULT_REFERENCE_FILES_DESCRIPTION.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_FILES_DESCRIPTION",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.reference-files-description-user-description",
  },
  // Review Files Description Prompts
  {
    promptKey: "DEFAULT_REVIEW_FILES_DESCRIPTION",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.review_files_description.title",
    defaultContent: "Review Files Description Prompts",
  },
  {
    promptKey: "DEFAULT_REVIEW_FILES_DESCRIPTION",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.review_files_description.description",
    defaultContent:
      "Configure system and user prompts for Review Files Description.",
  },
  {
    promptKey: "DEFAULT_REVIEW_FILES_DESCRIPTION",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.review-files-description-system-label",
    defaultContent: DEFAULT_REVIEW_FILES_DESCRIPTION.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REVIEW_FILES_DESCRIPTION",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.review-files-description-system-description",
  },
  {
    promptKey: "DEFAULT_REVIEW_FILES_DESCRIPTION",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.review-files-description-user-label",
    defaultContent: DEFAULT_REVIEW_FILES_DESCRIPTION.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REVIEW_FILES_DESCRIPTION",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.review-files-description-user-description",
  },
  // Reference/Review Sections Prompts
  {
    promptKey: "DEFAULT_REFERENCE_REVIEW_SECTIONS",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.reference_review_sections.title",
    defaultContent: "Reference/Review Sections Prompts",
  },
  {
    promptKey: "DEFAULT_REFERENCE_REVIEW_SECTIONS",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.reference_review_sections.description",
    defaultContent:
      "Configure system and user prompts for Reference/Review Sections.",
  },
  {
    promptKey: "DEFAULT_REFERENCE_REVIEW_SECTIONS",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.reference-review-sections-system-label",
    defaultContent: DEFAULT_REFERENCE_REVIEW_SECTIONS.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_REVIEW_SECTIONS",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.reference-review-sections-system-description",
  },
  {
    promptKey: "DEFAULT_REFERENCE_REVIEW_SECTIONS",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.reference-review-sections-user-label",
    defaultContent: DEFAULT_REFERENCE_REVIEW_SECTIONS.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_REVIEW_SECTIONS",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.reference-review-sections-user-description",
  },
  // Compliance section drafting prompt for reference files flow
  {
    promptKey: "DEFAULT_REFERENCE_SECTION_DRAFTING",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.compliance_section_drafting.title",
    defaultContent: "Compliance Section Drafting Prompts",
  },
  {
    promptKey: "DEFAULT_REFERENCE_SECTION_DRAFTING",
    promptField: "GROUP_DESCRIPTION",
    label:
      "document-builder.prompts.group.compliance_section_drafting.description",
    defaultContent:
      "Configure system and user prompts for Compliance Section Drafting.",
  },
  {
    promptKey: "DEFAULT_REFERENCE_SECTION_DRAFTING",
    promptField: "SYSTEM_PROMPT",
    label: "document-builder.prompts.compliance-section-drafting-system-label",
    defaultContent: DEFAULT_REFERENCE_SECTION_DRAFTING.SYSTEM_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_SECTION_DRAFTING",
      "SYSTEM_PROMPT"
    ),
    description:
      "document-builder.prompts.compliance-section-drafting-system-description",
  },
  {
    promptKey: "DEFAULT_REFERENCE_SECTION_DRAFTING",
    promptField: "USER_PROMPT",
    label: "document-builder.prompts.compliance-section-drafting-user-label",
    defaultContent: DEFAULT_REFERENCE_SECTION_DRAFTING.USER_PROMPT,
    systemSettingName: generateSystemSettingPKeyForLegalDrafting(
      "DEFAULT_REFERENCE_SECTION_DRAFTING",
      "USER_PROMPT"
    ),
    description:
      "document-builder.prompts.compliance-section-drafting-user-description",
  },
];

module.exports = {
  // Shared prompts
  DEFAULT_DOCUMENT_SUMMARY,
  DEFAULT_DOCUMENT_RELEVANCE,
  DEFAULT_SECTION_DRAFTING,
  DEFAULT_SECTION_LEGAL_ISSUES,
  DEFAULT_MEMO_CREATION,
  DEFAULT_SECTION_INDEX,

  // Main document flow prompts
  DEFAULT_SELECT_MAIN_DOCUMENT,
  DEFAULT_SECTION_LIST_FROM_MAIN,

  // No main document flow prompts
  DEFAULT_SECTION_LIST_FROM_SUMMARIES,
  // New Document Builder prompt groups
  DEFAULT_REFERENCE_FILES_DESCRIPTION,
  DEFAULT_REVIEW_FILES_DESCRIPTION,
  DEFAULT_REFERENCE_REVIEW_SECTIONS,
  DEFAULT_REFERENCE_SECTION_DRAFTING,
  exportedLegalPrompts,
};
