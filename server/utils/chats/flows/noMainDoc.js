const fs = require("fs");
const path = require("path");
const { SystemSettings } = require("../../../models/systemSettings");
const { Workspace } = require("../../../models/workspace");
const { WorkspaceChats } = require("../../../models/workspaceChats");
const { getUserDocumentPathName } = require("../../../endpoints/document");
const { getLLMProvider } = require("../../helpers");
const { TokenManager } = require("../../helpers/tiktoken");
const { writeResponseChunk } = require("../../helpers/chat/responses");
const { generateLegalMemo } = require("../../helpers/legalMemo");
const { purgeDocumentBuilder } = require("../../files");

const { getResolvedPrompts } = require("../helpers/promptManager");

const {
  generateDocumentDescription,
  generateDocumentRelevance,
  generateDocumentDescriptionIterative,
  generateDocumentRelevanceIterative,
  processIterativeSectionDraftingList,
  selectMainDocument,
  saveDocumentDescriptions,
  generateSectionListFromSummaries,
  generateDocumentSectionIndices,
  fillTemplate,
  combineSectionOutputs,
  createAbortChecker,
  handleAbortSignal,
} = require("../helpers/documentProcessing");

const { ContextWindowManager } = require("../helpers/contextWindowManager");
const { TokenTracker } = require("../helpers/tokenTracker");

/**
 * No Main Document Flow
 *
 * This flow handles document drafting for tasks explicitly configured to have no main document.
 * It always generates sections from the summaries of all relevant documents.
 *
 * @param {Object} options - Flow options (see flowDispatcher.js for full list)
 * @returns {Promise<void>}
 */
async function getSnippetsForSection(
  documentNames,
  workspacePath,
  maxLengthPerDoc = 1000
) {
  let snippets = "";
  for (const docName of documentNames) {
    const docPath = path.join(
      workspacePath,
      docName.replace(/\.json$/, "") + ".json"
    );
    if (fs.existsSync(docPath)) {
      try {
        const raw = fs.readFileSync(docPath, "utf8");
        const parsed = JSON.parse(raw);
        snippets += `Document: ${docName}\n${(parsed.pageContent || "").substring(0, maxLengthPerDoc)}\n\n`;
      } catch (e) {
        console.warn(
          `Could not read/parse ${docName} for snippet: ${e.message}`
        );
      }
    }
  }
  return snippets.trim();
}

// Utility: sanitize potential slug strings from LLM
const sanitizeSlug = (slugStr) => {
  if (!slugStr || typeof slugStr !== "string") return null;
  return slugStr
    .trim()
    .toLowerCase()
    .replace(/\s+/g, "-")
    .replace(/[^a-z0-9-_]/g, "");
};

async function runNoMainDocFlow(options) {
  const { abortSignal = null, chatId, response } = options;

  const AllPrompts = await getResolvedPrompts();

  // Create robust abort checker using shared helper
  const checkAbort = createAbortChecker(
    abortSignal,
    chatId,
    response,
    "noMain"
  );

  // Wrap the entire no-main flow to handle aborts properly
  try {
    const {
      request,
      response,
      workspace,
      message: legalTask, // Renaming for clarity
      chatMode = "chat",
      user = null,
      thread = null,
      attachments = [],
      chatId,
      isCanvasChat = false,
      preventChatCreation = false,
      settings_suffix = "",
      invoice_ref,
      vectorSearchMode = "default",
      hasUploadedFile = false,
      displayMessage = null,
      useDeepSearch = false,
      cdbOptions = [], // [legalPrompt, customInstructions, _mainDocNameIgnored]
      // Note: flowType is already determined as "noMain" by the dispatcher
    } = options;

    const customInstructions = cdbOptions[1] || "";
    const metrics = {}; // For collecting token counts or other metrics

    console.log("[NO MAIN DOC FLOW] Started.");

    // Helper to emit progress chunks compatible with the frontend progress UI.
    // If only `status` is provided, convert it to a numeric `progress` value.
    const sendProgress = (data = {}) => {
      const translated = { ...data };

      if (
        translated.progress === undefined &&
        typeof translated.status === "string"
      ) {
        switch (translated.status) {
          case "starting":
          case "in_progress":
            translated.progress = -1; // loading state
            break;
          case "complete":
            translated.progress = 100; // done
            break;
          case "error":
            translated.progress = -2; // failed
            break;
          default:
            // keep undefined for unrecognised status
            break;
        }
      }

      try {
        writeResponseChunk(response, {
          uuid: chatId,
          type: "cdbProgress",
          flowType: "noMain",
          ...translated,
        });
      } catch (err) {
        console.error("[NO MAIN DOC FLOW] Failed to send progress chunk", err);
        // If writing fails, it might be due to a closed connection - check abort
        checkAbort();
      }
    };

    // Setup LLMConnector
    let LLMConnector;
    if (process.env.LLM_PROVIDER_CDB) {
      let modelPrefCDB;
      switch (process.env.LLM_PROVIDER_CDB.toLowerCase()) {
        case "openai":
          modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB;
          break;
        case "anthropic":
          modelPrefCDB = process.env.ANTHROPIC_MODEL_PREF_CDB;
          break;
        case "gemini":
          modelPrefCDB = process.env.GEMINI_LLM_MODEL_PREF_CDB;
          break;
        case "lmstudio":
          modelPrefCDB = process.env.LMSTUDIO_MODEL_PREF_CDB;
          break;
        // Add other cases for different CDB providers if supported
        default:
          modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB; // Fallback model preference for CDB
      }
      LLMConnector = getLLMProvider({
        provider: process.env.LLM_PROVIDER_CDB,
        model: modelPrefCDB,
      });
    } else {
      LLMConnector = getLLMProvider(); // Standard system LLM
    }
    const temperature = workspace?.openAiTemp ?? LLMConnector.defaultTemp;

    // Setup iterative context window management
    const contextWindowManager = new ContextWindowManager(LLMConnector, {
      maxIterations: 10,
      reservedOutputTokens: 4000,
      enableTokenTracking: true,
    });

    const tokenTracker = new TokenTracker(LLMConnector, {
      enableDetailedTracking: true,
      trackContentTypes: true,
    });

    console.log(
      `[NO MAIN DOC FLOW] Context window manager initialized. Available context window: ${contextWindowManager.getAvailableContextWindow()} tokens`
    );

    // Check abort after setup is complete
    checkAbort();

    // Temp files should live directly under the shared `document-builder` directory.
    // They are uniquely named with the chatId, so we no longer need a separate
    // sub-folder structure (e.g. "no-main/<chatId>").
    const documentBuilderBasePath = path.join(
      __dirname,
      "../../../storage/document-builder"
    );

    // Ensure the directory exists before we start writing files.
    if (!fs.existsSync(documentBuilderBasePath)) {
      fs.mkdirSync(documentBuilderBasePath, { recursive: true });
    }

    // Workspace path setup
    const folderName = getUserDocumentPathName(user, true, workspace.slug);
    const workspacePath = path.join(
      __dirname,
      "../../../storage/documents",
      folderName
    );

    if (!fs.existsSync(workspacePath)) {
      console.error(
        "[NO MAIN DOC FLOW] Workspace path not found:",
        workspacePath
      );
      sendProgress({
        step: 0,
        status: "error",
        message: "Workspace documents not found.",
      });
      writeResponseChunk(response, {
        uuid: chatId,
        type: "text",
        text: "Error: Workspace documents not found.",
        sources: [],
        close: true,
      });
      return;
    }

    sendProgress({
      step: 1,
      status: "starting",
      message: "Processing documents for no-main-document flow...",
    });

    const allDocFiles = fs
      .readdirSync(workspacePath)
      .filter((file) => file.endsWith(".json"));
    let processedDocuments = []; // Though content might not be used extensively later, good to have
    let docDescriptions = [];

    sendProgress({
      step: 1,
      status: "in_progress",
      total: allDocFiles.length,
    });

    for (const docFile of allDocFiles) {
      checkAbort(); // Check abort before processing each document

      const currentDocIndex = docDescriptions.length + 1;
      const docLabel = docFile.replace(/\.json$/, "");

      const filePath = path.join(workspacePath, docFile);
      let fileContent = "";

      // Stable identifier based on the source filename
      const docId = docFile.replace(/\.json$/i, "");

      // Human-readable name (falls back to the id)
      let displayName = docId;

      try {
        const raw = fs.readFileSync(filePath, "utf8");
        const parsed = JSON.parse(raw);
        fileContent = parsed.pageContent || "";

        if (parsed.metadata?.title) {
          displayName = parsed.metadata.title;
        }
      } catch (err) {
        console.error(
          `[NO MAIN DOC FLOW] Failed to read or parse document ${docFile}:`,
          err
        );
        metrics[`error_read_${docId}`] = err.message;
        continue;
      }

      if (!fileContent) {
        console.warn(
          `[NO MAIN DOC FLOW] Document ${displayName} has no content. Skipping.`
        );
        metrics[`skipped_empty_${docId}`] = true;
        continue;
      }

      // Check abort before starting description generation
      checkAbort();

      // Emit loading state for this document
      sendProgress({
        step: 1,
        subStep: currentDocIndex,
        total: allDocFiles.length,
        message: `Processing: ${displayName} - Generating description...`,
        progress: -1,
      });

      // Use iterative processing for document description
      const description = await generateDocumentDescriptionIterative(
        displayName,
        fileContent,
        legalTask,
        LLMConnector,
        {
          customSystemPrompt:
            AllPrompts.CURRENT_DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT,
          customUserPromptTemplate:
            AllPrompts.CURRENT_DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT,
          temperature,
        },
        contextWindowManager,
        tokenTracker,
        chatId,
        "noMain"
      );

      // Track tokens from iterative processing
      const descriptionTokens =
        tokenTracker.getTokenUsageForStage("description") ||
        LLMConnector.metrics?.lastCompletionTokens ||
        0;
      metrics[`desc_tokens_${docId}`] = descriptionTokens;

      // Check abort after description generation before proceeding
      checkAbort();

      // Relevance check might be optional or have different criteria for "noMain" flow.
      // For now, we assume all documents are potentially relevant if they have content.
      // If a specific relevance check is needed, it would be added here.
      // For simplicity, we consider all described documents as "relevant" for section generation.
      docDescriptions.push({
        "Doc Name": docId,
        DisplayName: displayName,
        Description: description,
      });
      processedDocuments.push({
        "Doc Name": docId,
        DisplayName: displayName,
        Description: description,
        Content: fileContent,
        IsRelevant: true,
      }); // Mark as relevant for consistency

      // Mark this document sub-step as completed
      sendProgress({
        step: 1,
        subStep: currentDocIndex,
        total: allDocFiles.length,
        message: `Processed: ${displayName} - Generating description`,
        progress: 100,
      });
    }

    if (docDescriptions.length === 0) {
      sendProgress({
        step: 1,
        status: "error",
        message: "No documents found or processed.",
      });
      writeResponseChunk(response, {
        uuid: chatId,
        type: "text",
        text: "Error: No documents found to proceed with the legal task.",
        sources: [],
        close: true,
      });
      return;
    }

    const descriptionsFilePath = saveDocumentDescriptions(
      docDescriptions,
      chatId
    );
    sendProgress({
      step: 1,
      progress: 100,
      status: "complete",
      message: "Document descriptions generated for all documents.",
      data: {
        descriptionsFile: descriptionsFilePath,
        processedDocCount: docDescriptions.length,
      },
    });

    // Check abort before Step 2
    checkAbort();

    // Step 2: Generate Section List (always from summaries)
    sendProgress({
      step: 2,
      status: "in_progress",
      message: "Generating section list from summaries...",
    });

    const sectionListFromSummaries = await generateSectionListFromSummaries(
      docDescriptions,
      legalTask,
      LLMConnector,
      customInstructions,
      {
        customSystemPrompt:
          AllPrompts.CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES.SYSTEM_PROMPT,
        customUserPromptTemplate:
          AllPrompts.CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES.USER_PROMPT,
        temperature,
      }
    );
    metrics[`section_list_summaries_tokens`] =
      LLMConnector.metrics?.lastCompletionTokens || 0;

    // Check abort after section list generation
    checkAbort();

    // Save section list
    // const sectionListFilePath = path.join(__dirname, "../../../storage/document-builder/no-main", `section-list-${chatId}.json`);
    // fs.writeFileSync(sectionListFilePath, JSON.stringify(sectionList, null, 2), "utf8");
    // console.log(`Section list saved to ${sectionListFilePath}`);

    sendProgress({
      step: 2,
      status: "complete",
      message: "Section list generated from summaries.",
      data: { sectionList: sectionListFromSummaries, source: "summaries" },
    });

    // Check abort before Step 3
    checkAbort();

    // Step 3: Finalize Section List with Document Mapping (Normalization)
    // In this flow, relevant_documents are already provided by generateSectionListFromSummaries.
    // We just ensure the property name is consistent (`relevantDocumentNames`).

    sendProgress({
      step: 3,
      status: "in_progress",
      message: "Allocating documents to sections...",
      total: 1,
    });
    const finalSectionList = sectionListFromSummaries.map((section) => ({
      ...section,
      relevantDocumentNames:
        section.relevant_documents || section.relevantDocumentNames || [], // Ensure consistency
    }));
    sendProgress({
      step: 3,
      progress: 100,
      status: "complete",
      message: "Section document mapping finalized.",
      data: { sectionList: finalSectionList },
    });

    // Check abort after Step 3 completion
    checkAbort();

    // Save the final section list
    const sectionListFilePath = path.join(
      documentBuilderBasePath,
      `section-list-final-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        sectionListFilePath,
        JSON.stringify(finalSectionList, null, 2),
        "utf8"
      );
      console.log(
        `[NO MAIN DOC FLOW] Final section list saved to ${sectionListFilePath}`
      );
    } catch (error) {
      console.error(
        `[NO MAIN DOC FLOW] Error writing final section list file: ${error.message}`
      );
    }

    // Check abort before Step 4
    checkAbort();

    // Step 4: Identify Legal Issues per Section
    sendProgress({
      step: 4,
      status: "in_progress",
      message: "Identifying legal issues for each section...",
      ...(finalSectionList.length > 0 && { total: finalSectionList.length }),
    });

    for (let i = 0; i < finalSectionList.length; i++) {
      checkAbort(); // Check abort before processing each section

      const section = finalSectionList[i];
      const sectionNumber = section.index_number || i + 1;
      const sectionTitle =
        section.title || section.Description || `Section ${sectionNumber}`;
      const relevantDocNamesString =
        (section.relevantDocumentNames || []).join(", ") || "(none specified)";

      sendProgress({
        step: 4,
        message: `Identifying issues for Section ${sectionNumber}: ${sectionTitle}...`,
        subStep: i + 1,
        ...(finalSectionList.length > 0 && { total: finalSectionList.length }),
        progress: -1,
      });

      const issuesPromptUser = fillTemplate(
        AllPrompts.CURRENT_DEFAULT_SECTION_LEGAL_ISSUES.USER_PROMPT,
        {
          sectionNumber: sectionNumber.toString(),
          title: sectionTitle,
          task: legalTask,
          docs: relevantDocNamesString,
        }
      );

      const issuesResult = await LLMConnector.getChatCompletion(
        await LLMConnector.compressMessages({
          systemPrompt:
            AllPrompts.CURRENT_DEFAULT_SECTION_LEGAL_ISSUES.SYSTEM_PROMPT,
          userPrompt: issuesPromptUser,
        }),
        { temperature }
      );
      metrics[`legal_issues_sec_${sectionNumber}_tokens`] =
        LLMConnector.metrics?.lastCompletionTokens || 0;

      // Check abort after issues generation before parsing
      checkAbort();

      try {
        const issuesText = issuesResult.textResponse.trim();
        // Helper to safely extract JSON even if surrounded by markdown fences
        const extractJson = (text) => {
          const match = text.match(/```(?:json)?\s*\n([\s\S]*?)\n*```/i);
          return (match ? match[1] : text).trim();
        };

        const issuesBody = extractJson(issuesText);
        const parsedIssues = JSON.parse(issuesBody);
        section.identifiedLegalIssues = parsedIssues.map(
          (issueObj) => issueObj.Issue || issueObj
        );
      } catch (err) {
        console.error(
          `[NO MAIN DOC FLOW] Failed to parse legal issues for Section ${sectionNumber}: ${sectionTitle}`,
          err
        );
        section.identifiedLegalIssues = [
          `Error parsing issues: ${err.message}`,
        ];
      }
      finalSectionList[i] = section; // Update the section in the array

      // mark sub-task finished
      sendProgress({
        step: 4,
        subStep: i + 1,
        ...(finalSectionList.length > 0 && { total: finalSectionList.length }),
        message: `Identified issues for Section ${sectionNumber}: ${sectionTitle}`,
        progress: 100,
      });
    }

    const sectionListWithIssuesPath = path.join(
      documentBuilderBasePath,
      `section-list-with-issues-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        sectionListWithIssuesPath,
        JSON.stringify(finalSectionList, null, 2),
        "utf8"
      );
      console.log(
        `[NO MAIN DOC FLOW] Section list with issues saved to ${sectionListWithIssuesPath}`
      );
    } catch (error) {
      console.error(
        `[NO MAIN DOC FLOW] Error writing section list with issues file: ${error.message}`
      );
    }

    sendProgress({
      step: 4,
      progress: 100,
      status: "complete",
      message: "Legal issue identification complete.",
      data: { sectionList: finalSectionList },
    });

    // Check abort before Step 5
    checkAbort();

    // Step 5: Generate Legal Memos for Issues
    sendProgress({
      step: 5,
      status: "in_progress",
      message: "Generating legal memos for identified issues...",
    });

    // Helper function to resolve workspace, similar to snippet and mainDoc.js logic
    async function resolveWorkspaceForIssueInternal(
      issueText,
      qnaWorkspaceMap, // Map of slug -> workspace object
      llmConnectorInstance,
      currentTemperature
    ) {
      if (!qnaWorkspaceMap || qnaWorkspaceMap.size === 0) return null;

      const wsInfos = Array.from(qnaWorkspaceMap.values()).map(
        (ws) => `${ws.name} (slug: ${ws.slug})`
      );

      if (wsInfos.length === 1) {
        return qnaWorkspaceMap.values().next().value; // Return the workspace object
      }

      const systemPrompt =
        "You are a legal routing assistant. From the provided list of Legal-QA workspaces, choose the single workspace whose focus best matches the legal issue. Return ONLY the workspace SLUG or 'NONE' if none are relevant.";
      const userPrompt = `Legal issue: "${issueText}"\nAvailable Legal-QA workspaces: ${wsInfos.join(
        ", "
      )}`;

      try {
        const compressed = await llmConnectorInstance.compressMessages({
          systemPrompt,
          userPrompt,
        });
        const res = await llmConnectorInstance.getChatCompletion(compressed, {
          temperature: 0, // Low temperature for classification
        });
        const slugRaw = res.textResponse.trim();
        if (slugRaw === "NONE") return null;
        const slug = sanitizeSlug(slugRaw);
        return qnaWorkspaceMap.get(slug) || null; // Return workspace object if slug is valid
      } catch (err) {
        console.error(
          `[NO MAIN DOC FLOW] Failed to resolve workspace for issue "${issueText}"`,
          err
        );
        return null;
      }
    }

    // Fetch Legal-QA workspaces
    const legalQnaWorkspaceMapBySlug = new Map();
    try {
      const lqaWorkspaces = await Workspace.where({ type: "legal-qa" });
      (lqaWorkspaces || []).forEach((ws) => {
        if (ws?.name && ws?.slug) {
          legalQnaWorkspaceMapBySlug.set(ws.slug, ws);
        }
      });
      if (legalQnaWorkspaceMapBySlug.size > 0) {
        console.log(
          `[NO MAIN DOC FLOW] Found ${
            legalQnaWorkspaceMapBySlug.size
          } Legal-QA workspaces for memo generation.`
        );
      }
    } catch (err) {
      console.error(
        "[NO MAIN DOC FLOW] Failed to fetch Legal-QA workspace list for Step 5",
        err
      );
      // Proceed without specific QnA workspaces, memos will use the main flow workspace or fail.
    }

    // Check abort after workspace setup
    checkAbort();

    const allIdentifiedIssues = new Map();
    finalSectionList.forEach((section, sectionIndex) => {
      (section.identifiedLegalIssues || []).forEach((issueText) => {
        if (
          typeof issueText === "string" &&
          !allIdentifiedIssues.has(issueText)
        ) {
          allIdentifiedIssues.set(issueText, {
            sectionIndex,
            relevantDocumentNames: section.relevantDocumentNames || [],
          });
        }
      });
    });

    let generatedMemosDetails = [];
    let memoCounter = 0;

    for (const [issueText, issueDetails] of allIdentifiedIssues.entries()) {
      checkAbort(); // Check abort before starting new memo generation

      memoCounter++;
      sendProgress({
        step: 5,
        subStep: memoCounter,
        total: allIdentifiedIssues.size,
        message: `Resolving workspace for: ${String(issueText)}...`,
        progress: -1,
      });

      let targetWs = await resolveWorkspaceForIssueInternal(
        issueText,
        legalQnaWorkspaceMapBySlug,
        LLMConnector,
        temperature
      );
      let usedWorkspaceSlug = null;

      if (targetWs) {
        usedWorkspaceSlug = targetWs.slug;
        console.log(
          `[NO MAIN DOC FLOW] Resolved workspace ${targetWs.slug} for issue: "${issueText}"`
        );
      } else if (legalQnaWorkspaceMapBySlug.size > 0) {
        targetWs = legalQnaWorkspaceMapBySlug.values().next().value;
        usedWorkspaceSlug = targetWs.slug;
        console.warn(
          `[NO MAIN DOC FLOW] Using fallback Legal-QA workspace (${usedWorkspaceSlug}) for issue: "${issueText}"`
        );
      } else {
        // If no Legal-QA workspaces, use the main flow's workspace object.
        // This maintains previous behavior if no specific QnA workspaces are found/configured.
        targetWs = workspace;
        usedWorkspaceSlug = targetWs.slug; // Slug of the main flow workspace
        console.warn(
          `[NO MAIN DOC FLOW] No Legal-QA workspace resolved or available. Using main flow workspace (${usedWorkspaceSlug}) for issue: "${issueText}"`
        );
      }

      if (!targetWs) {
        // This case should ideally not be hit if main 'workspace' is always valid.
        console.error(
          `[NO MAIN DOC FLOW] Critical: No target workspace available for memo generation on issue "${issueText}". Skipping.`
        );
        generatedMemosDetails.push({
          issueText,
          error: "Critical: No target workspace available.",
          memoFileName: null,
          workspaceSlug: null,
        });
        // Update section list to reflect skipped memo
        finalSectionList.forEach((sec) => {
          if (
            sec.identifiedLegalIssues &&
            sec.identifiedLegalIssues.includes(issueText)
          ) {
            if (!sec.relevantMemos) sec.relevantMemos = [];
            sec.relevantMemos.push({
              issue: issueText,
              error: "Skipped memo generation: Critical, no target workspace.",
            });
          }
        });
        sendProgress({
          step: 5,
          subStep: memoCounter,
          total: allIdentifiedIssues.size,
          message: `Error - No workspace for: ${String(issueText)}...`,
          progress: -2,
          status: "error",
        });
        continue;
      }

      // Send initial progress for memo generation
      sendProgress({
        step: 5,
        subStep: memoCounter,
        total: allIdentifiedIssues.size,
        message: `Generating memo for: ${String(issueText)}...`,
        progress: -1,
        status: "in_progress",
      });

      // Add a small delay to ensure the progress event is sent before starting heavy processing
      await new Promise((resolve) => setTimeout(resolve, 100));

      const docsForMemo =
        issueDetails.relevantDocumentNames.join(", ") ||
        "(general legal principles)";
      const memoSystemPrompt = fillTemplate(
        AllPrompts.CURRENT_DEFAULT_MEMO_CREATION.PROMPT_TEMPLATE,
        { docs: docsForMemo }
      );

      // --- ENHANCED: Memo generation with retry logic and keepalive events ---
      const MAX_RETRIES = 3;
      const RETRY_DELAY = 1000; // 1 second
      const KEEPALIVE_INTERVAL = 15000; // 15 seconds

      let memoResult = null;
      let lastError = null;
      let keepaliveInterval = null;

      try {
        // Start keepalive interval to prevent frontend timeout
        keepaliveInterval = setInterval(() => {
          // Check abort signal before sending keepalive
          try {
            checkAbort();
          } catch (e) {
            // Abort detected, clear interval and stop
            clearInterval(keepaliveInterval);
            return;
          }

          sendProgress({
            step: 5,
            subStep: memoCounter,
            total: allIdentifiedIssues.size,
            message: `Generating memo for: ${String(issueText)}...`,
            progress: -1,
            status: "in_progress",
            keepalive: true, // Flag to indicate this is a keepalive update
          });
          console.log(
            `[NO MAIN DOC FLOW] Keepalive: Memo ${memoCounter} for issue "${String(issueText).substring(0, 40)}" is still being generated...`
          );
        }, KEEPALIVE_INTERVAL);

        for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
          checkAbort(); // Check abort before each retry attempt

          try {
            console.log(
              `[NO MAIN DOC FLOW] Memo generation attempt ${attempt}/${MAX_RETRIES} for issue: ${String(issueText).substring(0, 60)} using workspace: ${targetWs.slug}`
            );

            const startTime = Date.now();
            memoResult = await generateLegalMemo({
              workspace: targetWs, // Use the resolved or fallback Legal-QA workspace (or main if none)
              systemPrompt: memoSystemPrompt,
              userPrompt: issueText,
              LLMConnector,
              temperature,
              tokenLimit: null, // As per snippet
              settings: { skipContext: true }, // As per snippet
            });
            const endTime = Date.now();
            const memoTime = Math.round((endTime - startTime) / 1000);

            // Validate memo result
            if (
              !memoResult ||
              !memoResult.memo ||
              typeof memoResult.memo !== "string"
            ) {
              throw new Error(
                `Invalid memo result: ${JSON.stringify(memoResult)}`
              );
            }

            if (memoResult.memo.length < 10) {
              throw new Error(
                `Memo too short: ${memoResult.memo.length} characters`
              );
            }

            console.log(
              `[NO MAIN DOC FLOW] Memo generation successful on attempt ${attempt} in ${memoTime}s`
            );
            lastError = null;
            break; // Success, exit retry loop
          } catch (attemptError) {
            lastError = attemptError;
            console.error(
              `[NO MAIN DOC FLOW] Memo generation attempt ${attempt}/${MAX_RETRIES} failed:`,
              attemptError.message
            );

            if (attempt < MAX_RETRIES) {
              console.log(
                `[NO MAIN DOC FLOW] Retrying memo generation in ${RETRY_DELAY}ms...`
              );

              // Check abort before delay
              try {
                checkAbort();
              } catch (e) {
                console.log(
                  `[NO MAIN DOC FLOW] Aborted before retry delay: ${e.message}`
                );
                throw e;
              }

              await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
            }
          }
        }

        // Clear keepalive interval
        if (keepaliveInterval) {
          clearInterval(keepaliveInterval);
          keepaliveInterval = null;
        }

        // Check if we have a successful result
        if (!memoResult || lastError) {
          // All attempts failed
          throw (
            lastError || new Error("Memo generation failed after all retries")
          );
        }

        const { memo, tokenCount } = memoResult;
        metrics[`memo_gen_${targetWs.slug}_${memoCounter}_tokens`] =
          tokenCount || LLMConnector.metrics?.lastCompletionTokens || 0;

        // Check abort after memo generation before file operations
        checkAbort();

        // Save the generated memo content with robust file handling
        const memoFileName = `legal-memo-${memoCounter}-${chatId}.md`;
        let memoFilePath = path.join(documentBuilderBasePath, memoFileName);

        // Validate file path and directory
        try {
          // Ensure directory exists with proper permissions
          if (!fs.existsSync(documentBuilderBasePath)) {
            fs.mkdirSync(documentBuilderBasePath, {
              recursive: true,
              mode: 0o755,
            });
            console.log(
              `[NO MAIN DOC FLOW] Created directory: ${documentBuilderBasePath}`
            );
          }

          // Write memo with error handling
          fs.writeFileSync(memoFilePath, memo, "utf8");
          console.log(
            `[NO MAIN DOC FLOW] Memo saved successfully: ${memoFileName}`
          );

          // In test environment, skip file verification to avoid test issues
          if (process.env.NODE_ENV !== "test" && !process.env.JEST_WORKER_ID) {
            // Validate file was written correctly (only in production)
            if (!fs.existsSync(memoFilePath)) {
              throw new Error(`File not found after write: ${memoFilePath}`);
            }

            const fileStats = fs.statSync(memoFilePath);
            if (fileStats.size === 0) {
              throw new Error(`Empty file written: ${memoFilePath}`);
            }
          }
        } catch (fileError) {
          console.error(
            `[NO MAIN DOC FLOW] File writing error for ${memoFileName}:`,
            fileError.message
          );

          // Only use backup location in production environment, not in tests
          if (process.env.NODE_ENV !== "test" && !process.env.JEST_WORKER_ID) {
            // Try alternative location
            const backupPath = path.join(
              __dirname,
              "../../../storage/document-builder-backup"
            );
            if (!fs.existsSync(backupPath)) {
              fs.mkdirSync(backupPath, { recursive: true, mode: 0o755 });
            }

            memoFilePath = path.join(backupPath, memoFileName);
            fs.writeFileSync(memoFilePath, memo, "utf8");
            console.log(
              `[NO MAIN DOC FLOW] Memo saved to backup location: ${memoFilePath}`
            );
          } else {
            // In test environment, re-throw the original error
            throw fileError;
          }
        }

        const relevantSectionIndicesForMemo = [];
        finalSectionList.forEach((sec, idx) => {
          if (
            sec.identifiedLegalIssues &&
            sec.identifiedLegalIssues.includes(issueText)
          ) {
            relevantSectionIndicesForMemo.push(sec.index_number || idx + 1);
            if (!sec.relevantMemos) sec.relevantMemos = [];
            sec.relevantMemos.push({
              issue: issueText,
              memoFileName,
              memoFilePath,
            });
          }
        });

        generatedMemosDetails.push({
          issueText,
          memoFileName,
          memoFilePath,
          relevantSectionIndices: relevantSectionIndicesForMemo,
          workspaceSlug: targetWs.slug, // Store the slug of the workspace used
        });
      } catch (error) {
        console.error(
          `[NO MAIN DOC FLOW] Failed to generate or save memo for issue: "${issueText}" using workspace ${targetWs.slug}`,
          error
        );

        // Clear keepalive interval on error
        if (keepaliveInterval) {
          clearInterval(keepaliveInterval);
          keepaliveInterval = null;
        }

        metrics[`memo_gen_error_${targetWs.slug}_${memoCounter}`] =
          error.message;
        generatedMemosDetails.push({
          issueText,
          error: `Failed using ${targetWs.slug}: ${error.message}`,
          memoFileName: null,
          workspaceSlug: targetWs.slug,
        });
        finalSectionList.forEach((sec) => {
          if (
            sec.identifiedLegalIssues &&
            sec.identifiedLegalIssues.includes(issueText)
          ) {
            if (!sec.relevantMemos) sec.relevantMemos = [];
            sec.relevantMemos.push({
              issue: issueText,
              error: `Failed using ${targetWs.slug}: ${error.message}`,
            });
          }
        });
      }

      sendProgress({
        step: 5,
        subStep: memoCounter,
        total: allIdentifiedIssues.size,
        message: `Generated memo for: ${String(issueText)}`,
        progress: 100,
        status: "complete",
      });
    }

    const memosIndexPath = path.join(
      documentBuilderBasePath,
      `legal-memos-index-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        memosIndexPath,
        JSON.stringify(generatedMemosDetails, null, 2),
        "utf8"
      );
      console.log(
        `[NO MAIN DOC FLOW] Legal memos index saved to ${memosIndexPath}`
      );
    } catch (error) {
      console.error(
        `[NO MAIN DOC FLOW] Error writing legal memos index file: ${error.message}`
      );
    }

    const sectionListWithMemosPath = path.join(
      documentBuilderBasePath,
      `section-list-with-memos-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        sectionListWithMemosPath,
        JSON.stringify(finalSectionList, null, 2),
        "utf8"
      );
      console.log(
        `[NO MAIN DOC FLOW] Section list with memo references saved to ${sectionListWithMemosPath}`
      );
    } catch (error) {
      console.error(
        `[NO MAIN DOC FLOW] Error writing section list with memo references file: ${error.message}`
      );
    }

    sendProgress({
      step: 5,
      status: "complete",
      message: "Legal memo generation complete.",
      data: {
        memosIndex: generatedMemosDetails,
        sectionList: finalSectionList,
      },
    });

    // Check abort before Step 6
    checkAbort();

    // Step 6: Draft Individual Sections (Iterative)
    // Uses iterative context window management for large sections with extensive documentation
    sendProgress({
      step: 6,
      status: "in_progress",
      message:
        "Drafting individual sections with iterative processing (no main document flow)...",
      // Only set total if we actually have sections to process
      ...(finalSectionList.length > 0 && { total: finalSectionList.length }),
    });

    // Process sections using iterative drafting
    let draftedSections;
    try {
      draftedSections = await processIterativeSectionDraftingList(
        finalSectionList,
        {
          legalTask,
          processedDocuments,
          workspacePath,
          mainDocNameInitial: null, // No main document in this flow
          temperature,
          AllPrompts,
          chatId,
          documentBuilderBasePath,
        },
        contextWindowManager,
        tokenTracker,
        {
          // Progress callback for UI updates
          onSectionProgress: (sectionIndex, sectionTitle, status) => {
            // Check abort during section processing
            try {
              checkAbort();
            } catch (e) {
              console.log(
                `[NO MAIN DOC FLOW] Aborted during section ${sectionIndex + 1} drafting: ${e.message}`
              );
              // Throw error to stop the processing
              throw new Error("Process aborted by user");
            }

            const isComplete = status === "Complete";
            const isDrafting = status.includes("Drafting");
            const isError = status === "Error";

            // Format message to match mainDoc flow patterns
            let message;
            if (isComplete) {
              message = `Drafted Section ${sectionIndex + 1}: ${sectionTitle}`;
            } else if (isDrafting) {
              message = `Drafting Section ${sectionIndex + 1}: ${sectionTitle}...`;
            } else if (isError) {
              message = `Error drafting Section ${sectionIndex + 1}: ${sectionTitle}`;
            } else {
              message = `Drafting Section ${sectionIndex + 1}: ${sectionTitle}...`;
            }

            sendProgress({
              step: 6,
              message: message,
              subStep: sectionIndex + 1,
              // Only set total if we actually have sections to process
              ...(finalSectionList.length > 0 && {
                total: finalSectionList.length,
              }),
              // Fix: Set proper status and progress for completed sections
              status: isComplete
                ? "complete"
                : isError
                  ? "error"
                  : "in_progress",
              progress: isComplete ? 100 : isDrafting ? -1 : 0,
            });
          },
          // Token usage callback for metrics
          onTokenUsage: (sectionIndex, tokenUsage) => {
            const sectionNumber = sectionIndex + 1;
            metrics[`draft_sec_${sectionNumber}_tokens`] =
              tokenUsage.totalTokens;
            metrics[`draft_sec_${sectionNumber}_iterations`] =
              tokenUsage.iterations;
            if (tokenUsage.isIterative) {
              metrics[`draft_sec_${sectionNumber}_iterative`] = true;
            }
          },
        }
      );
    } catch (error) {
      // Handle abort error
      if (error.message === "Process aborted by user") {
        console.log(`[NO MAIN DOC FLOW] Section drafting aborted by user`);
        return;
      }
      // Re-throw other errors
      throw error;
    }

    // Check abort after section drafting completes
    checkAbort();

    // Update finalSectionList with drafted content
    finalSectionList.splice(0, finalSectionList.length, ...draftedSections);

    const sectionListWithDraftsPath = path.join(
      documentBuilderBasePath,
      `section-list-with-drafts-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        sectionListWithDraftsPath,
        JSON.stringify(finalSectionList, null, 2),
        "utf8"
      );
      console.log(
        `[NO MAIN DOC FLOW] Section list with drafts saved to ${sectionListWithDraftsPath}`
      );
    } catch (error) {
      console.error(
        `[NO MAIN DOC FLOW] Error writing section list with drafts file: ${error.message}`
      );
    }

    sendProgress({
      step: 6,
      progress: 100,
      status: "complete",
      message: "Individual section drafting complete.",
      data: { sectionList: finalSectionList },
    });

    // Check abort before Step 7
    checkAbort();

    // Step 7: Combine Drafted Sections (Refined) -> (Direct Combination)
    sendProgress({
      step: 7,
      status: "in_progress",
      message:
        "Combining drafted sections into final document (no main doc flow)...",
    });

    // Use the helper function to combine sections
    const sectionOutputsForCombine = finalSectionList.map((s) => ({
      title: s.title || s.Description || `Section ${s.index_number || "N/A"}`,
      content: s.draftedContent || "(Content not available)",
    }));

    // Check abort before document combination
    checkAbort();

    const finalCombinedDocumentContent = combineSectionOutputs(
      // Directly assign to final variable
      sectionOutputsForCombine
    );

    // Add a check in case combining resulted in empty string
    if (!finalCombinedDocumentContent) {
      console.warn(
        "[NO MAIN DOC FLOW] combineSectionOutputs returned empty string in Step 7."
      );
      metrics[`combine_doc_error`] = "No drafted sections or empty content";
    }

    // Check abort before final document save
    checkAbort();

    const finalDocPath = path.join(
      documentBuilderBasePath,
      `final-document-${chatId}.md`
    );
    try {
      fs.writeFileSync(finalDocPath, finalCombinedDocumentContent, "utf8");
      console.log(
        `[NO MAIN DOC FLOW] Final combined document saved to ${finalDocPath}`
      );
    } catch (error) {
      console.error(
        `[NO MAIN DOC FLOW] Error writing final combined document: ${error.message}`
      );
      metrics[`final_doc_save_error`] = error.message;
    }

    sendProgress({
      step: 7,
      status: "complete",
      message: "Document combination complete.",
      data: {
        finalDocumentPath: finalDocPath,
        combinedPreview: finalCombinedDocumentContent.substring(0, 200) + "...", // Use the direct combined content
      },
    });

    // Final abort check before completing
    checkAbort();

    // Generate and log comprehensive token usage report
    const tokenReport = tokenTracker.generateReport();
    console.log(
      "[NO MAIN DOC FLOW] Token Usage Report:",
      JSON.stringify(tokenReport, null, 2)
    );

    // Add token usage to metrics
    metrics.tokenUsageReport = {
      summary: tokenReport.summary,
      utilizationRate: tokenReport.summary.budgetUtilization,
      totalIterations: tokenReport.iterativeMetrics.totalIterations,
      iterativeProcessingUsed:
        tokenReport.iterativeMetrics.totalIterations > finalSectionList.length,
    };

    console.log("[NO MAIN DOC FLOW] Processing fully finished.");
    console.log(
      "[NO MAIN DOC FLOW] Metrics:",
      JSON.stringify(metrics, null, 2)
    );
    return finalCombinedDocumentContent;
  } catch (error) {
    // Handle abort signals using shared helper
    return handleAbortSignal(error, purgeDocumentBuilder, writeResponseChunk);
  }
}

module.exports = {
  runNoMainDocFlow,
};
