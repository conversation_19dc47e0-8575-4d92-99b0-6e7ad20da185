const fs = require("fs");
const path = require("path");

const { SystemSettings } = require("../../../models/systemSettings");
const { Workspace } = require("../../../models/workspace");
const { WorkspaceChats } = require("../../../models/workspaceChats");
const { getUserDocumentPathName } = require("../../../endpoints/document");
const { getLLMProvider } = require("../../helpers");
const { TokenManager } = require("../../helpers/tiktoken");
const { writeResponseChunk } = require("../../helpers/chat/responses");
const { generateLegalMemo } = require("../../helpers/legalMemo");
const { purgeDocumentBuilder } = require("../../files");

const { getResolvedPrompts } = require("../helpers/promptManager");

const {
  generateDocumentDescription,
  generateDocumentRelevance,
  generateDocumentDescriptionIterative,
  generateDocumentRelevanceIterative,
  processIterativeSectionDraftingList,
  selectMainDocument,
  saveDocumentDescriptions,
  generateSectionListFromSummaries,
  generateDocumentSectionIndices,
  fillTemplate,
  combineSectionOutputs,
  createAbortChecker,
  handleAbortSignal,
} = require("../helpers/documentProcessing");

const { ContextWindowManager } = require("../helpers/contextWindowManager");
const { TokenTracker } = require("../helpers/tokenTracker");

/**
 * Main Document Flow (Adaptive)
 *
 * This flow handles document drafting when a "main" document is central to the task.
 * It can adapt if no explicit main document is provided by generating sections from summaries.
 *
 * @param {Object} options - Flow options (see flowDispatcher.js for full list)
 * @returns {Promise<void>}
 */
async function getSnippetsForSection(
  documentNames,
  workspacePath,
  maxLengthPerDoc = 1000
) {
  let snippets = "";
  for (const docName of documentNames) {
    const docPath = path.join(
      workspacePath,
      docName.replace(/\.json$/, "") + ".json"
    );
    if (fs.existsSync(docPath)) {
      try {
        const raw = fs.readFileSync(docPath, "utf8");
        const parsed = JSON.parse(raw);
        snippets += `Document: ${docName}\n${(parsed.pageContent || "").substring(0, maxLengthPerDoc)}\n\n`;
      } catch (e) {
        console.warn(
          `Could not read/parse ${docName} for snippet: ${e.message}`
        );
      }
    }
  }
  return snippets.trim();
}

// Utility: sanitize potential slug strings from LLM
const sanitizeSlug = (slugStr) => {
  if (!slugStr || typeof slugStr !== "string") return null;
  let cleaned = slugStr.trim().toLowerCase();
  if (["none", "null", "undefined"].includes(cleaned)) return null;
  cleaned = cleaned.replace(/\s+/g, "-").replace(/[^a-z0-9-_]/g, "");
  return cleaned.length > 0 ? cleaned : null;
};

// NEW: More fault-tolerant JSON parser for LLM outputs
const safeJsonParse = (jsonString) => {
  if (!jsonString || typeof jsonString !== "string") return null;
  try {
    return JSON.parse(jsonString);
  } catch (initialErr) {
    try {
      // 1) Remove trailing commas before closing brackets/braces
      let cleaned = jsonString.replace(/,\s*([}\]])/g, "$1");
      // 2) Convert single quotes to double quotes (naïve but covers common cases)
      cleaned = cleaned.replace(/'([^']*)'/g, '"$1"');
      // 3) Strip non-ASCII characters that occasionally sneak into the output
      cleaned = cleaned.replace(/[^\t\n\r\x20-\x7E]/g, "");
      // 4) Remove accidental empty strings left behind
      cleaned = cleaned.replace(/"\s*"/g, '"');
      return JSON.parse(cleaned);
    } catch (_ignoredErr) {
      // Re-throw the original error so calling code can handle/log it
      throw initialErr;
    }
  }
};

// Helper to capture token usage from provider responses
const updateLastTokens = (result, LLM) => {
  if (!result || !LLM) return;
  const tokens =
    result.metrics?.total_tokens ??
    (result.metrics?.prompt_tokens || 0) +
      (result.metrics?.completion_tokens || 0);
  LLM.metrics = { ...(LLM.metrics || {}), lastCompletionTokens: tokens };
};

async function runMainDocFlow(options) {
  const { abortSignal = null, chatId, response } = options;

  const AllPrompts = await getResolvedPrompts();

  // Create robust abort checker using shared helper
  const checkAbort = createAbortChecker(abortSignal, chatId, response, "main");

  // Wrap the entire main flow to handle aborts properly
  try {
    /*
     * Main Document Drafting Flow Overview:
     * STEP 1: Generate Section List from Main Document - Uses the primary document's content to create an initial structure for the output.
     * STEP 2: Process Documents (Descriptions & Relevance) - Summarizes all workspace documents and identifies those relevant to the task.
     * STEP 3: Map Documents to Sections - Associates relevant documents with the sections generated in Step 1.
     * STEP 4: Identify Legal Issues per Section - Analyzes each section to find potential legal questions.
     * STEP 5: Generate Legal Memos for Issues - Creates detailed legal memos for the identified issues using RAG.
     * STEP 6: Draft Individual Sections - Writes the content for each section, incorporating document snippets and memo analysis.
     * STEP 7: Combine Drafted Sections - Merges individual section drafts into the final coherent document.
     */

    const {
      request,
      response,
      workspace,
      message: legalTask, // Renaming for clarity within this flow
      chatMode = "chat",
      user = null,
      thread = null,
      attachments = [],
      chatId,
      isCanvasChat = false,
      preventChatCreation = false,
      settings_suffix = "",
      invoice_ref,
      vectorSearchMode = "default",
      hasUploadedFile = false,
      displayMessage = null,
      useDeepSearch = false,
      cdbOptions = [], // [legalPrompt, customInstructions, mainDocNameFromOptions]
      mainDocName: mainDocNameFromOuter, // Explicitly passed mainDocName
      // Note: flowType is already determined as "main" by the dispatcher
    } = options;

    const customInstructions = cdbOptions[1] || "";
    let mainDocNameInitial =
      mainDocNameFromOuter || (cdbOptions && cdbOptions[2]) || null;

    // Normalise the name so it aligns with the internal identifier (docId)
    if (typeof mainDocNameInitial === "string") {
      mainDocNameInitial = mainDocNameInitial.replace(/\.json$/i, "");
    }

    console.log(
      `[MAIN DOC FLOW] Started. Initial main document: ${mainDocNameInitial || "(none - will adapt)"}`
    );

    const metrics = {}; // For collecting token counts or other metrics

    // Helper to emit progress chunks in a way the frontend understands.
    // If the caller only passes a textual `status`, we automatically translate it
    // to a numeric `progress` key expected by the React UI:
    //   "starting" / "in_progress" → progress: -1  (loading)
    //   "complete"                 → progress: 100 (done)
    //   "error"                   → progress: -2  (failed)
    const sendProgress = (data = {}) => {
      const translated = { ...data };

      if (
        translated.progress === undefined &&
        typeof translated.status === "string"
      ) {
        switch (translated.status) {
          case "starting":
          case "in_progress":
            translated.progress = -1;
            break;
          case "complete":
            translated.progress = 100;
            break;
          case "error":
            translated.progress = -2;
            break;
          default:
            // leave progress undefined for unknown statuses
            break;
        }
      }

      try {
        writeResponseChunk(response, {
          uuid: chatId,
          type: "cdbProgress",
          flowType: "main",
          ...translated,
        });
      } catch (err) {
        console.error("[MAIN DOC FLOW] Failed to send progress chunk", err);
        // If writing fails, it might be due to a closed connection - check abort
        checkAbort();
      }
    };

    // Setup LLMConnector
    let LLMConnector;
    if (process.env.LLM_PROVIDER_CDB) {
      let modelPrefCDB;
      switch (process.env.LLM_PROVIDER_CDB.toLowerCase()) {
        case "openai":
          modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB;
          break;
        case "anthropic":
          modelPrefCDB = process.env.ANTHROPIC_MODEL_PREF_CDB;
          break;
        case "gemini":
          modelPrefCDB = process.env.GEMINI_LLM_MODEL_PREF_CDB;
          break;
        case "lmstudio":
          modelPrefCDB = process.env.LMSTUDIO_MODEL_PREF_CDB;
          break;
        // Add other cases for different CDB providers if supported
        default:
          modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB; // Fallback model preference for CDB
      }
      LLMConnector = getLLMProvider({
        provider: process.env.LLM_PROVIDER_CDB,
        model: modelPrefCDB,
      });
    } else {
      LLMConnector = getLLMProvider();
    }
    const temperature = workspace?.openAiTemp ?? LLMConnector.defaultTemp;

    // Setup iterative context window management
    const contextWindowManager = new ContextWindowManager(LLMConnector, {
      maxIterations: 10,
      reservedOutputTokens: 4000,
      enableTokenTracking: true,
    });

    const tokenTracker = new TokenTracker(LLMConnector, {
      enableDetailedTracking: true,
      trackContentTypes: true,
    });

    console.log(
      `[MAIN DOC FLOW] Context window manager initialized. Available context window: ${contextWindowManager.getAvailableContextWindow()} tokens`
    );

    // Temp files should live directly under the shared `document-builder` directory.
    // They are uniquely named with the chatId, so we no longer need a separate
    // sub-folder structure (e.g. "main/<chatId>").
    const documentBuilderBasePath = path.join(
      __dirname,
      "../../../storage/document-builder"
    );

    // Ensure the directory exists before we start writing files.
    if (!fs.existsSync(documentBuilderBasePath)) {
      fs.mkdirSync(documentBuilderBasePath, { recursive: true });
    }

    // Workspace path setup
    console.log("[MAIN DOC FLOW] User for path:", JSON.stringify(user));
    console.log("[MAIN DOC FLOW] Workspace slug for path:", workspace.slug);
    console.log("[MAIN DOC FLOW] isDocumentDrafting flag for path:", true);
    const folderName = getUserDocumentPathName(user, true, workspace.slug);
    console.log("[MAIN DOC FLOW] Generated folderName:", folderName);
    const workspacePath = path.join(
      __dirname,
      "../../../storage/documents",
      folderName
    );
    console.log(
      "[MAIN DOC FLOW] Constructed workspacePath for existence check:",
      workspacePath
    );

    if (!fs.existsSync(workspacePath)) {
      console.error("[MAIN DOC FLOW] Workspace path NOT FOUND:", workspacePath);
      sendProgress({
        step: 0,
        status: "error",
        message: "Workspace documents not found.",
      });
      writeResponseChunk(response, {
        uuid: chatId,
        type: "text",
        text: "Error: Workspace documents not found.",
        sources: [],
        close: true,
      });
      return;
    }

    /*
     * --------------------------------------------------
     * STEP 1: Generate Section List from Main Document (Renumbered from 0)
     * --------------------------------------------------
     * Uses the primary document's content to generate an initial list of sections
     * for the final output document.
     */
    checkAbort(); // Check for abort before starting Step 1

    // Variables that may be reused later in the flow
    let prebuiltSectionList = null;
    let prebuiltSectionListSource = null;

    if (mainDocNameInitial) {
      sendProgress({
        step: 1,
        status: "starting",
        message: `Generating section list using main document: ${mainDocNameInitial}...`,
      });

      try {
        const mainDocFilePath = path.join(
          workspacePath,
          `${mainDocNameInitial.replace(/\.json$/i, "")}.json`
        );

        const rawMainDoc = fs.readFileSync(mainDocFilePath, "utf8");
        const parsedMainDoc = JSON.parse(rawMainDoc);
        const mainDocPageContentEarly = parsedMainDoc.pageContent || "";

        if (!mainDocPageContentEarly) {
          throw new Error("Main document appears to have no content.");
        }

        const sectionListResultEarly = await LLMConnector.getChatCompletion(
          await LLMConnector.compressMessages({
            systemPrompt:
              AllPrompts.CURRENT_DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT,
            userPrompt: fillTemplate(
              AllPrompts.CURRENT_DEFAULT_SECTION_LIST_FROM_MAIN.USER_PROMPT,
              {
                task: legalTask,
                content: mainDocPageContentEarly,
              }
            ),
          }),
          { temperature }
        );
        updateLastTokens(sectionListResultEarly, LLMConnector);

        metrics[`section_list_main_tokens_step1`] =
          LLMConnector.metrics?.lastCompletionTokens || 0;

        try {
          prebuiltSectionList = JSON.parse(
            sectionListResultEarly.textResponse
              .trim()
              .match(/```(?:json)?\r?\n([\s\S]*?)```/i)?.[1] ||
              sectionListResultEarly.textResponse.trim() ||
              "[]"
          );
          prebuiltSectionListSource = "main_document_content";
        } catch (parseErr) {
          console.error(
            "[MAIN DOC FLOW] Failed to parse section list in Step 1 (was 0):",
            parseErr
          );
          throw parseErr;
        }

        sendProgress({
          step: 1,
          status: "complete",
          message: "Section list generated from main document.",
          data: { sectionList: prebuiltSectionList },
        });
      } catch (err) {
        console.error(
          "[MAIN DOC FLOW] Error during Step 1 (was 0) section generation:",
          err
        );
        sendProgress({
          step: 1,
          status: "error",
          message: `Failed to generate section list from main document: ${err.message}`,
        });
        writeResponseChunk(response, {
          uuid: chatId,
          type: "text",
          text: `Error: Unable to generate section list from main document. ${err.message}`,
          sources: [],
          close: true,
        });
        return;
      }
    } else {
      // No main document name supplied – this should not happen per new flow design.
      console.error(
        "[MAIN DOC FLOW] mainDocNameInitial is missing – cannot build sections first."
      );
    }

    /*
     * --------------------------------------------------
     * STEP 2: Process Documents (Descriptions & Relevance)
     * --------------------------------------------------
     */
    checkAbort(); // Check for abort before starting Step 2

    sendProgress({
      step: 2,
      status: "starting",
      message:
        "Processing documents: Generating descriptions and checking relevance...",
    });

    const allDocFiles = fs
      .readdirSync(workspacePath)
      .filter((file) => file.endsWith(".json"));
    let processedDocuments = [];
    let docDescriptions = [];

    // Announce total documents for sub-task tracking
    sendProgress({
      step: 2,
      status: "in_progress",
      total: allDocFiles.length,
    });

    for (const [docIndex, docFile] of allDocFiles.entries()) {
      checkAbort(); // Check abort before processing each document

      const filePath = path.join(workspacePath, docFile);
      let fileContent = "";

      // Use the raw filename (sans .json) as the **stable identifier** that will
      // be referenced throughout the flow (and by the UI).
      const docId = docFile.replace(/\.json$/i, "");

      // A separate, human-readable display name (falling back to the id).
      let displayName = docId;

      try {
        const raw = fs.readFileSync(filePath, "utf8");
        const parsed = JSON.parse(raw);
        fileContent = parsed.pageContent || "";

        // Prefer a title from the extracted metadata for readability, but keep
        // the internal identifier unchanged.
        if (parsed.metadata?.title) {
          displayName = parsed.metadata.title;
        }
      } catch (err) {
        console.error(
          `[MAIN DOC FLOW] Failed to read or parse document ${docFile}:`,
          err
        );
        metrics[`error_read_${docId}`] = err.message;
        continue; // Skip this document
      }

      if (!fileContent) {
        console.warn(
          `[MAIN DOC FLOW] Document ${displayName} has no content. Skipping.`
        );
        metrics[`skipped_empty_${docId}`] = true;
        continue;
      }

      const currentDocIndex = docIndex + 1;
      const docNameForLabel = displayName;
      // Emit loading state for this document
      sendProgress({
        step: 2,
        subStep: currentDocIndex,
        total: allDocFiles.length,
        label: docNameForLabel,
        progress: -1,
      });

      sendProgress({
        step: 2,
        message: `Processing: ${displayName} - Generating description...`,
      });

      // Use iterative processing for document description
      const description = await generateDocumentDescriptionIterative(
        displayName,
        fileContent,
        legalTask,
        LLMConnector,
        {
          customSystemPrompt:
            AllPrompts.CURRENT_DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT,
          customUserPromptTemplate:
            AllPrompts.CURRENT_DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT,
          temperature,
        }
      );

      // Track tokens from iterative processing
      const descriptionTokens =
        tokenTracker.getTokenUsageForStage("description") ||
        LLMConnector.metrics?.lastCompletionTokens ||
        0;
      metrics[`desc_tokens_${docId}`] = descriptionTokens;

      sendProgress({
        step: 2,
        message: `Processing: ${displayName} - Checking relevance...`,
      });

      // Skip relevance check for the main document – it is always relevant by definition
      let isRelevant;
      if (docId === mainDocNameInitial) {
        isRelevant = true;
        metrics[`relevance_skipped_${docId}`] = true; // track that we skipped
      } else {
        // Use iterative processing for document relevance
        isRelevant = await generateDocumentRelevanceIterative(
          displayName,
          fileContent, // Or use description for relevance check to save tokens, depending on strategy
          legalTask,
          LLMConnector,
          {
            customSystemPrompt:
              AllPrompts.CURRENT_DEFAULT_DOCUMENT_RELEVANCE.SYSTEM_PROMPT,
            customUserPromptTemplate:
              AllPrompts.CURRENT_DEFAULT_DOCUMENT_RELEVANCE.USER_PROMPT,
            temperature,
          }
        );

        // Track tokens from iterative processing
        const relevanceTokens =
          tokenTracker.getTokenUsageForStage("relevance") ||
          LLMConnector.metrics?.lastCompletionTokens ||
          0;
        metrics[`relevance_tokens_${docId}`] = relevanceTokens;
      }

      if (docId === mainDocNameInitial) {
        console.log(
          `[MAIN DOC DEBUG] Main document (${mainDocNameInitial}) auto-marked relevant.`
        );
      }

      if (isRelevant) {
        docDescriptions.push({
          "Doc Name": docId,
          DisplayName: displayName,
          Description: description,
        });

        processedDocuments.push({
          "Doc Name": docId,
          DisplayName: displayName,
          Description: description,
          Content: fileContent,
          IsRelevant: true,
        });
      } else {
        processedDocuments.push({
          "Doc Name": docId,
          DisplayName: displayName,
          Description: "Not relevant",
          Content: fileContent,
          IsRelevant: false,
        });
      }
      // Mark this document sub-step as completed
      sendProgress({
        step: 2,
        subStep: currentDocIndex,
        total: allDocFiles.length,
        label: docNameForLabel,
        progress: 100,
      });
    }

    if (docDescriptions.length === 0) {
      sendProgress({
        step: 2,
        status: "error",
        message: "No relevant documents found after processing.",
      });
      writeResponseChunk(response, {
        uuid: chatId,
        type: "text",
        text: "Error: No relevant documents found to proceed with the legal task.",
        sources: [],
        close: true,
      });
      return;
    }

    const descriptionsFilePath = saveDocumentDescriptions(
      docDescriptions,
      chatId
    );
    sendProgress({
      step: 2,
      progress: 100,
      status: "complete",
      message: "Document descriptions and relevance checks complete.",
      data: {
        descriptionsFile: descriptionsFilePath,
        relevantDocCount: docDescriptions.length,
      },
    });

    // --------------------------------------------------
    // STEP 3: Map Documents to Sections & Fallback Section Generation (Renumbered from 4)
    // --------------------------------------------------
    // Primarily maps documents to sections generated in Step 1.
    let sectionList = prebuiltSectionList || [];
    let sectionListSource =
      prebuiltSectionListSource || "main_document_content"; // Keep track of how it was generated
    let mainDocPageContent = "";

    if (mainDocNameInitial) {
      const mainDocData = processedDocuments.find(
        (doc) => doc["Doc Name"] === mainDocNameInitial && doc.IsRelevant
      );
      // If mainDocData exists, you might use its content later, but the block was empty
      // Add necessary logic here if needed based on mainDocData
    }

    // Proceed with mapping using the sectionList
    let finalSectionList = sectionList; // This will be our working copy

    // Mapping logic only applies if sections came from the main document initially
    if (
      sectionListSource === "main_document_content" &&
      finalSectionList &&
      finalSectionList.length > 0
    ) {
      const otherRelevantDocs = processedDocuments.filter(
        (doc) => doc.IsRelevant && doc["Doc Name"] !== mainDocNameInitial
      );

      sendProgress({
        step: 3,
        status: "in_progress",
        message: "Mapping other relevant documents to sections...",
        total: otherRelevantDocs.length,
      });

      // Initialize relevantDocumentNames for each section
      finalSectionList = finalSectionList.map((section) => ({
        ...section,
        relevantDocumentNames: mainDocNameInitial ? [mainDocNameInitial] : [],
      }));

      for (const otherDoc of otherRelevantDocs) {
        const currentIndex = otherRelevantDocs.indexOf(otherDoc) + 1;
        sendProgress({
          step: 3,
          message: `Mapping sections for: ${otherDoc["Doc Name"]}...`,
          subStep: currentIndex,
          total: otherRelevantDocs.length,
          label: otherDoc["Doc Name"],
        });
        const relevantIndices = await generateDocumentSectionIndices(
          otherDoc["Doc Name"],
          otherDoc.Content,
          finalSectionList, // Use the current state of finalSectionList for mapping
          LLMConnector,
          {
            customSystemPrompt:
              AllPrompts.CURRENT_DEFAULT_SECTION_INDEX.SYSTEM_PROMPT,
            // User prompt for generateDocumentSectionIndices is constructed internally by the helper
            temperature: 0, // Typically want deterministic mapping
          }
        );
        metrics[`map_doc_${otherDoc["Doc Name"]}_tokens`] =
          LLMConnector.metrics?.lastCompletionTokens || 0;

        relevantIndices.forEach((targetSectionIndex) => {
          const sectionToUpdate = finalSectionList.find(
            (s) => s.index_number === targetSectionIndex
          );
          if (sectionToUpdate) {
            if (
              !sectionToUpdate.relevantDocumentNames.includes(
                otherDoc["Doc Name"]
              )
            ) {
              sectionToUpdate.relevantDocumentNames.push(otherDoc["Doc Name"]);
            }
          }
        });

        // mark sub-task finished
        sendProgress({
          step: 3,
          subStep: currentIndex,
          total: otherRelevantDocs.length,
          label: otherDoc["Doc Name"],
        });
      }
      // Finalize step 3 mapping progress
      sendProgress({
        step: 3,
        progress: 100,
        status: "complete",
        message: "Document to section mapping complete.",
        data: { sectionList: finalSectionList },
      });
    } else if (finalSectionList && finalSectionList.length > 0) {
      // Sections came from summaries - mapping was likely done during generation
      // Ensure relevantDocumentNames is an array
      finalSectionList = finalSectionList.map((section) => ({
        ...section,
        relevantDocumentNames:
          section.relevant_documents || section.relevantDocumentNames || [],
      }));
      // Still emit a completion event so frontend moves past step 3 gracefully
      sendProgress({
        step: 3,
        progress: 100,
        status: "complete",
        message:
          "Document to section mapping included in summary-based section generation.",
        data: { sectionList: finalSectionList },
      });
    }

    // Save the final section list (now with comprehensive document mappings)
    const sectionListFilePath = path.join(
      documentBuilderBasePath,
      `section-list-final-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        sectionListFilePath,
        JSON.stringify(finalSectionList, null, 2),
        "utf8"
      );
      console.log(
        `[MAIN DOC FLOW] Final section list saved to ${sectionListFilePath}`
      );
    } catch (error) {
      console.error(
        `[MAIN DOC FLOW] Error writing final section list file: ${error.message}`
      );
    }

    /*
     * --------------------------------------------------
     * EXTRA OUTPUT: Document → Section Mapping Report
     * --------------------------------------------------
     * After completing the section-to-document mapping, also create a reverse
     * mapping (document → sections). This fulfils the requirement that the
     * *report file* should indicate for which sections each uploaded document
     * is relevant.
     */

    // 1. Build a map of docName -> [sectionIndex, ...]
    const docToSections = {};
    finalSectionList.forEach((section) => {
      (section.relevantDocumentNames || []).forEach((docName) => {
        if (!docToSections[docName]) {
          docToSections[docName] = [];
        }
        // Avoid duplicates
        if (!docToSections[docName].includes(section.index_number)) {
          docToSections[docName].push(section.index_number);
        }
      });
    });

    // 2. Augment the previously saved document descriptions with the new info
    docDescriptions = docDescriptions.map((desc) => {
      const sectionsForDoc = docToSections[desc["Doc Name"]] || [];
      return {
        ...desc,
        RelevantSections: sectionsForDoc,
      };
    });

    // 3. Persist the enhanced descriptions as a separate artefact
    const docSectionMappingPath = path.join(
      documentBuilderBasePath,
      `document-descriptions-with-sections-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        docSectionMappingPath,
        JSON.stringify(docDescriptions, null, 2),
        "utf8"
      );
      console.log(
        `[MAIN DOC FLOW] Document → section mapping saved to ${docSectionMappingPath}`
      );
    } catch (error) {
      console.error(
        `[MAIN DOC FLOW] Error writing document → section mapping file: ${error.message}`
      );
    }

    // 4. Include a reference to the mapping file in the progress payload so the
    //    frontend can display or download it if needed.
    sendProgress({
      step: 3,
      message: "Document → section mapping report generated.",
      data: { mappingFile: docSectionMappingPath },
    });

    // Step 4: Identify Legal Issues per Section
    checkAbort(); // Check for abort before starting Step 4

    sendProgress({
      step: 4,
      status: "in_progress",
      message: "Identifying legal issues for each section...",
      ...(finalSectionList.length > 0 && { total: finalSectionList.length }),
    });

    // --- Fetch Legal-QA workspaces for prompting in Step 4 ---
    // --- ENHANCED: Store map by slug with proper error handling and fallbacks ---
    const legalQnaWorkspaceMapBySlug = new Map();
    const workspaceInfoList = [];
    let hasFallbackWorkspace = false;

    try {
      console.log(
        "[MAIN DOC FLOW] Fetching Legal-QA workspaces for memo generation..."
      );
      const lqaWorkspaces = await Workspace.where({ type: "legal-qa" });

      if (!lqaWorkspaces || lqaWorkspaces.length === 0) {
        console.warn(
          "[MAIN DOC FLOW] No Legal-QA workspaces found, will use current workspace as fallback"
        );
        // Add current workspace as fallback
        if (workspace && workspace.slug) {
          legalQnaWorkspaceMapBySlug.set(workspace.slug, workspace);
          workspaceInfoList.push(
            `${workspace.name || "Current Workspace"} (slug: ${workspace.slug})`
          );
          hasFallbackWorkspace = true;
          console.log(
            `[MAIN DOC FLOW] Added current workspace as fallback: ${workspace.slug}`
          );
        }
      } else {
        (lqaWorkspaces || []).forEach((ws) => {
          if (ws?.name && ws?.slug) {
            legalQnaWorkspaceMapBySlug.set(ws.slug, ws);
            workspaceInfoList.push(`${ws.name} (slug: ${ws.slug})`);
          }
        });
        console.log(
          `[MAIN DOC FLOW] Found ${lqaWorkspaces.length} Legal-QA workspaces`
        );
      }
    } catch (err) {
      console.error(
        "[MAIN DOC FLOW] Failed to fetch Legal-QA workspace list, using current workspace as fallback",
        err
      );
      // Ensure we have at least the current workspace
      if (workspace && workspace.slug) {
        legalQnaWorkspaceMapBySlug.set(workspace.slug, workspace);
        workspaceInfoList.push(
          `${workspace.name || "Current Workspace"} (slug: ${workspace.slug})`
        );
        hasFallbackWorkspace = true;
        console.log(
          `[MAIN DOC FLOW] Added current workspace as emergency fallback: ${workspace.slug}`
        );
      } else {
        console.error(
          "[MAIN DOC FLOW] Critical: No valid workspace available for memo generation"
        );
        throw new Error(
          "No valid workspace available for memo generation - current workspace is invalid"
        );
      }
    }

    // Validate that we have at least one workspace for memo generation
    if (legalQnaWorkspaceMapBySlug.size === 0) {
      console.error(
        "[MAIN DOC FLOW] Critical: No workspaces available for memo generation after all fallback attempts"
      );
      throw new Error("No workspaces available for memo generation");
    }
    // --- MODIFIED: Create prompt string from info list and ask for SLUG ---
    const workspaceListString = workspaceInfoList.join(", ");
    const workspacePromptAddition =
      workspaceListString.length > 0
        ? `\n\nAvailable Legal-QA Workspaces: [${workspaceListString}]. For each issue, add a JSON property \"WORKSPACE_SLUG_FOR_LEGALDATA\" with the most relevant workspace *slug* from this list, or null if none seem suitable.` // Ask for SLUG
        : "";
    // --- END NEW ---

    for (let i = 0; i < finalSectionList.length; i++) {
      checkAbort(); // Check abort before processing each section

      const section = finalSectionList[i];
      const sectionNumber = section.index_number || i + 1;
      const sectionTitle =
        section.title || section.Description || `Section ${sectionNumber}`;
      const relevantDocNamesString =
        (section.relevantDocumentNames || []).join(", ") || "(none specified)";

      sendProgress({
        step: 4,
        message: `Identifying issues for Section ${sectionNumber}: ${sectionTitle}...`,
        subStep: i + 1,
        ...(finalSectionList.length > 0 && { total: finalSectionList.length }),
        label: sectionTitle,
        progress: -1,
      });

      // --- MODIFIED: Add workspace list to user prompt ---
      const issuesPromptUser =
        fillTemplate(
          AllPrompts.CURRENT_DEFAULT_SECTION_LEGAL_ISSUES.USER_PROMPT, // Assume this prompt asks for JSON with "Issue" field
          {
            sectionNumber: sectionNumber.toString(),
            title: sectionTitle,
            task: legalTask,
            docs: relevantDocNamesString,
          }
        ) + workspacePromptAddition; // Add the workspace suggestion part

      // --- MODIFIED: Use potentially updated system prompt if needed ---
      // Example: If system prompt also needs update to mention the new field
      const issuesSystemPrompt =
        AllPrompts.CURRENT_DEFAULT_SECTION_LEGAL_ISSUES.SYSTEM_PROMPT; // Potentially modified to mention WORKSPACE_SLUG_FOR_LEGALDATA

      console.log(
        `[MAIN DOC DEBUG] Step 4: Requesting legal issues for Section ${sectionNumber}: ${sectionTitle}`
      );
      const issuesResult = await LLMConnector.getChatCompletion(
        await LLMConnector.compressMessages({
          systemPrompt: issuesSystemPrompt,
          userPrompt: issuesPromptUser,
        }),
        { temperature } // Use a lower temperature? 0.3 used in streamCDB
      );
      updateLastTokens(issuesResult, LLMConnector);
      console.log(
        `[MAIN DOC DEBUG] Step 4: Received legal issues for Section ${sectionNumber}`
      );
      metrics[`legal_issues_sec_${sectionNumber}_tokens`] =
        LLMConnector.metrics?.lastCompletionTokens || 0;

      // --- MODIFIED: Parse Issue AND Suggested Workspace SLUG ---
      try {
        const issuesText = issuesResult.textResponse.trim();
        // Helper to safely extract JSON even if surrounded by markdown fences
        const extractJson = (text) => {
          const fenced = text.match(/```(?:json)?\s*\n([\s\S]*?)\n*```/i);
          const body = fenced ? fenced[1] : text;
          const start = body.indexOf("[");
          const end = body.lastIndexOf("]");
          if (start !== -1 && end !== -1 && end > start) {
            return body.slice(start, end + 1).trim();
          }
          return body.trim();
        };

        const issuesBody = extractJson(issuesText);
        const parsedIssues = safeJsonParse(issuesBody); // Use tolerant parser to handle minor JSON formatting issues

        // Store the full issue object, including the sanitized slug
        section.identifiedLegalIssues = Array.isArray(parsedIssues)
          ? parsedIssues.map((issueObj) => ({
              Issue: issueObj?.Issue || issueObj, // Handle cases where parsing might be imperfect
              WORKSPACE_SLUG_FOR_LEGALDATA: sanitizeSlug(
                issueObj?.WORKSPACE_SLUG_FOR_LEGALDATA
              ), // Extract & sanitize slug
            }))
          : [];
      } catch (err) {
        console.error(
          `[MAIN DOC FLOW] Failed to parse legal issues JSON for Section ${sectionNumber}: ${sectionTitle}`,
          err,
          `Raw Response: ${issuesResult.textResponse}` // Log raw response on error
        );
        section.identifiedLegalIssues = [
          `Error parsing issues: ${err.message}`,
        ]; // Store error message
        section.error = `Failed to parse legal issues: ${err.message}`; // Store error message on the section
      }
      // --- END MODIFICATION ---

      finalSectionList[i] = section; // Update the section in the array

      // sub-task done event - NOW INCLUDES ERROR HANDLING
      sendProgress({
        step: 4,
        subStep: i + 1,
        ...(finalSectionList.length > 0 && { total: finalSectionList.length }),
        label: sectionTitle,
        progress: section.error ? -2 : 100, // Set progress to -2 if error occurred, else 100
        error: section.error || null, // Include the error message if present
      });
    }

    const sectionListWithIssuesPath = path.join(
      documentBuilderBasePath,
      `section-list-with-issues-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        sectionListWithIssuesPath,
        JSON.stringify(finalSectionList, null, 2),
        "utf8"
      );
      console.log(
        `[MAIN DOC FLOW] Section list with issues saved to ${sectionListWithIssuesPath}`
      );
    } catch (error) {
      console.error(
        `[MAIN DOC FLOW] Error writing section list with issues file: ${error.message}`
      );
    }

    sendProgress({
      step: 4,
      progress: 100,
      status: "complete",
      message: "Legal issue identification complete.",
      data: { sectionList: finalSectionList },
    });
    await new Promise((r) => setImmediate(r)); // YIELD AFTER STEP 4 COMPLETE

    // Step 5: Generate Legal Memos for Issues using LegalQnA Workspaces
    checkAbort(); // Check for abort before starting Step 5

    // MOVED INITIALIZATION EARLIER: Populate allIdentifiedIssues map FIRST
    const allIdentifiedIssues = new Map(); // Map will now store issueText -> { issueObject, sectionIndex, relevantDocumentNames }
    finalSectionList.forEach((section, sectionIndex) => {
      (section.identifiedLegalIssues || []).forEach((issueObj) => {
        if (
          typeof issueObj === "object" &&
          issueObj !== null &&
          issueObj.Issue
        ) {
          const issueText = issueObj.Issue;
          if (!allIdentifiedIssues.has(issueText)) {
            allIdentifiedIssues.set(issueText, {
              issueObject: issueObj,
              sectionIndex,
              relevantDocumentNames: section.relevantDocumentNames || [],
            });
          }
        }
      });
    });

    // NOW it's safe to send the initial progress for Step 5, using the size of the populated map
    sendProgress({
      step: 5,
      status: "in_progress",
      message: "Generating legal memos for identified issues...",
      total: allIdentifiedIssues.size,
    });

    let generatedMemosDetails = []; // Renamed back
    let memoCounter = 0;

    // --- REVERTED: Removed legalQnAWorkspaces query here, moved before Step 4 ---
    // let legalQnAWorkspaces = []; ...

    for (const [issueText, issueDetails] of allIdentifiedIssues.entries()) {
      checkAbort(); // Check abort before starting new memo generation

      memoCounter++;
      // --- MODIFIED: Get the suggested SLUG ---
      const suggestedWorkspaceSlug = sanitizeSlug(
        issueDetails.issueObject?.WORKSPACE_SLUG_FOR_LEGALDATA
      );

      // Send initial progress for memo generation
      sendProgress({
        step: 5,
        subStep: memoCounter,
        total: allIdentifiedIssues.size,
        message: `Generating memo for: ${String(issueText)}...`,
        progress: -1,
        status: "in_progress",
      });

      // Add a small delay to ensure the progress event is sent before starting heavy processing
      await new Promise((resolve) => setTimeout(resolve, 100));

      // --- ENHANCED: Robust workspace selection with validation ---
      let targetWs = null;

      // Validate workspace function
      const validateWorkspace = (ws) => {
        return (
          ws && ws.slug && typeof ws.slug === "string" && ws.slug.length > 0
        );
      };

      // Try suggested workspace first
      if (
        suggestedWorkspaceSlug &&
        legalQnaWorkspaceMapBySlug.has(suggestedWorkspaceSlug)
      ) {
        const suggestedWs = legalQnaWorkspaceMapBySlug.get(
          suggestedWorkspaceSlug
        );
        if (validateWorkspace(suggestedWs)) {
          targetWs = suggestedWs;
          console.log(
            `[MAIN DOC FLOW] Using suggested workspace: ${targetWs.slug} for issue: ${String(issueText).substring(0, 60)}`
          );
        } else {
          console.warn(
            `[MAIN DOC FLOW] Suggested workspace ${suggestedWorkspaceSlug} is invalid, using fallback`
          );
        }
      }

      // Fallback to first valid workspace if suggestion failed
      if (!targetWs && legalQnaWorkspaceMapBySlug.size > 0) {
        for (const ws of legalQnaWorkspaceMapBySlug.values()) {
          if (validateWorkspace(ws)) {
            targetWs = ws;
            console.log(
              `[MAIN DOC FLOW] Using fallback workspace: ${targetWs.slug} for issue: ${String(issueText).substring(0, 60)}`
            );
            break;
          }
        }
      }

      // Critical error: no valid workspace found
      if (!targetWs) {
        console.error(
          `[MAIN DOC FLOW] Critical: No valid workspace found for memo generation on issue: "${String(issueText)}"`
        );
        metrics[`memo_gen_error_no_workspace_${memoCounter}`] = true;
        generatedMemosDetails.push({
          issueText,
          error: "No valid workspace found for memo generation",
          memoFileName: null,
          workspaceSlug: null,
        });

        // Update section list to reflect error
        finalSectionList.forEach((sec) => {
          if (sec.identifiedLegalIssues?.some((io) => io.Issue === issueText)) {
            if (!sec.relevantMemos) sec.relevantMemos = [];
            sec.relevantMemos.push({
              issue: issueText,
              error: "No valid workspace found for memo generation",
              generated: false,
            });
          }
        });

        sendProgress({
          step: 5,
          subStep: memoCounter,
          total: allIdentifiedIssues.size,
          message: `Error - No workspace for: ${String(issueText)}...`,
          progress: -2,
          status: "error",
        });
        continue; // Skip to the next issue
      }

      // Prepare for memo generation using the targetWs
      const docsForMemo =
        (issueDetails.relevantDocumentNames || []).join(", ") ||
        "(general legal principles)";
      const memoSystemPrompt = fillTemplate(
        AllPrompts.CURRENT_DEFAULT_MEMO_CREATION.PROMPT_TEMPLATE, // Reuse the memo creation prompt
        { docs: docsForMemo }
      );

      // --- ENHANCED: Memo generation with retry logic and validation ---
      const MAX_RETRIES = 3;
      const RETRY_DELAY = 1000; // 1 second

      let memoResult = null;
      let lastError = null;

      for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        checkAbort(); // Check abort before each retry attempt

        try {
          console.log(
            `[MAIN DOC FLOW] Memo generation attempt ${attempt}/${MAX_RETRIES} for issue: ${String(issueText).substring(0, 60)} using workspace: ${targetWs.slug}`
          );

          const startTime = Date.now();

          // Set up keepalive interval to maintain frontend progress connection
          let keepaliveInterval = null;
          const KEEPALIVE_INTERVAL = 5000; // Send progress update every 5 seconds
          let keepaliveCounter = 0;

          try {
            keepaliveInterval = setInterval(() => {
              // Check abort signal before sending keepalive
              try {
                checkAbort();
              } catch (e) {
                // Abort detected, clear interval and stop
                clearInterval(keepaliveInterval);
                return;
              }

              keepaliveCounter++;
              sendProgress({
                step: 5,
                subStep: memoCounter,
                total: allIdentifiedIssues.size,
                message: `Generating memo for: ${String(issueText)}...`,
                progress: -1, // Keep as in-progress
                keepalive: true, // Flag to indicate this is a keepalive update
              });
            }, KEEPALIVE_INTERVAL);

            memoResult = await generateLegalMemo({
              workspace: targetWs,
              systemPrompt: memoSystemPrompt,
              userPrompt: issueText,
              LLMConnector,
              temperature,
              tokenLimit: null,
              settings: { skipContext: true },
            });
          } finally {
            // Clear the keepalive interval regardless of success or failure
            if (keepaliveInterval) {
              clearInterval(keepaliveInterval);
              keepaliveInterval = null;
            }
          }

          const endTime = Date.now();

          // Validate memo result
          if (
            !memoResult ||
            !memoResult.memo ||
            typeof memoResult.memo !== "string"
          ) {
            throw new Error(
              `Invalid memo result structure: ${JSON.stringify(memoResult)}`
            );
          }

          if (memoResult.memo.length < 10) {
            throw new Error(
              `Memo too short: ${memoResult.memo.length} characters`
            );
          }

          console.log(
            `[MAIN DOC FLOW] Memo generation successful on attempt ${attempt} (${endTime - startTime}ms, ${memoResult.memo.length} chars)`
          );
          break; // Success, exit retry loop
        } catch (error) {
          lastError = error;
          console.error(
            `[MAIN DOC FLOW] Memo generation attempt ${attempt}/${MAX_RETRIES} failed for workspace ${targetWs.slug}:`,
            error.message
          );

          if (attempt < MAX_RETRIES) {
            console.log(
              `[MAIN DOC FLOW] Retrying memo generation in ${RETRY_DELAY}ms...`
            );

            checkAbort(); // Check abort before delay

            await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
          }
        }
      }

      // Check if all attempts failed
      if (!memoResult) {
        console.error(
          `[MAIN DOC FLOW] All ${MAX_RETRIES} memo generation attempts failed for issue: "${issueText}" using workspace: ${targetWs.slug}`
        );
        throw (
          lastError || new Error("Memo generation failed after all retries")
        );
      }

      const { memo, tokenCount } = memoResult;
      metrics[`memo_gen_${targetWs.slug}_${memoCounter}_tokens`] =
        tokenCount || LLMConnector.metrics?.lastCompletionTokens || 0;
      metrics[`memo_gen_${targetWs.slug}_${memoCounter}_attempts`] =
        MAX_RETRIES - (memoResult ? 0 : MAX_RETRIES); // Track successful attempt number

      try {
        // Save the generated memo content with robust file handling
        const memoFileName = `legal-memo-${memoCounter}-${chatId}.md`;
        let memoFilePath = path.join(documentBuilderBasePath, memoFileName);

        // Validate file path and directory
        try {
          // Ensure directory exists with proper permissions
          if (!fs.existsSync(documentBuilderBasePath)) {
            fs.mkdirSync(documentBuilderBasePath, {
              recursive: true,
              mode: 0o755,
            });
            console.log(
              `[MAIN DOC FLOW] Created directory: ${documentBuilderBasePath}`
            );
          }

          // Write memo with error handling
          fs.writeFileSync(memoFilePath, memo, {
            encoding: "utf8",
            mode: 0o644,
          });
          console.log(
            `[MAIN DOC FLOW] Memo saved successfully: ${memoFileName} (${memo.length} chars)`
          );

          // In test environment, skip file verification to avoid test issues
          if (process.env.NODE_ENV !== "test" && !process.env.JEST_WORKER_ID) {
            // Verify file was written correctly (only in production)
            if (!fs.existsSync(memoFilePath)) {
              throw new Error("File was not created successfully");
            }

            const writtenContent = fs.readFileSync(memoFilePath, "utf8");
            if (writtenContent.length !== memo.length) {
              throw new Error(
                `File content mismatch: expected ${memo.length} chars, got ${writtenContent.length} chars`
              );
            }
          }
        } catch (fileError) {
          console.error(
            `[MAIN DOC FLOW] Failed to save memo file ${memoFileName}:`,
            fileError.message
          );

          // Only use backup location in production environment, not in tests
          if (process.env.NODE_ENV !== "test" && !process.env.JEST_WORKER_ID) {
            try {
              const backupPath = path.join(process.cwd(), "temp", memoFileName);
              const backupDir = path.dirname(backupPath);
              if (!fs.existsSync(backupDir)) {
                fs.mkdirSync(backupDir, { recursive: true });
              }
              fs.writeFileSync(backupPath, memo, "utf8");
              console.log(
                `[MAIN DOC FLOW] Memo saved to backup location: ${backupPath}`
              );
              // Update file path to backup location
              memoFilePath = backupPath;
            } catch (backupError) {
              console.error(
                `[MAIN DOC FLOW] Failed to save memo to backup location:`,
                backupError.message
              );
              throw new Error(
                `Failed to save memo file: primary error: ${fileError.message}, backup error: ${backupError.message}`
              );
            }
          } else {
            // In test environment, re-throw the original error
            throw fileError;
          }
        }

        // Update tracking structures with generated memo details
        const relevantSectionIndicesForMemo = [];
        finalSectionList.forEach((sec, idx) => {
          // Match based on the issue text within the issue objects
          if (sec.identifiedLegalIssues?.some((io) => io.Issue === issueText)) {
            relevantSectionIndicesForMemo.push(sec.index_number || idx + 1);
            if (!sec.relevantMemos) sec.relevantMemos = [];
            sec.relevantMemos.push({
              issue: issueText,
              memoFileName, // Store filename
              memoFilePath, // Store full path
              generated: true, // Indicate it was generated
              targetWorkspaceSlug: targetWs.slug, // Record which workspace was used
            });
          }
        });

        generatedMemosDetails.push({
          issueText,
          memoFileName,
          memoFilePath,
          relevantSectionIndices: relevantSectionIndicesForMemo,
          workspaceSlug: targetWs.slug, // Ensure slug is stored
          error: null,
        });
      } catch (error) {
        console.error(
          `[MAIN DOC FLOW] Failed to generate or save memo using workspace ${targetWs.slug} for issue: \"${issueText}\"`,
          error
        );
        metrics[`memo_gen_error_${targetWs.slug}_${memoCounter}`] =
          error.message;
        generatedMemosDetails.push({
          issueText,
          error: `Failed using ${targetWs.slug}: ${error.message}`,
          memoFileName: null,
          workspaceSlug: targetWs.slug,
        });
        // Update section list with error info
        finalSectionList.forEach((sec) => {
          if (sec.identifiedLegalIssues?.some((io) => io.Issue === issueText)) {
            if (!sec.relevantMemos) sec.relevantMemos = [];
            sec.relevantMemos.push({
              issue: issueText,
              error: `Failed memo generation using ${targetWs.slug}: ${error.message}`,
              generated: false,
              targetWorkspaceSlug: targetWs.slug, // Ensure slug is stored
            });
          }
        });
      }
      // --- END NEW ---

      sendProgress({
        step: 5,
        subStep: memoCounter,
        total: allIdentifiedIssues.size,
        message: `Generated memo for: ${String(issueText)}`,
        progress: 100,
        status: "complete",
      });
    } // End loop over identified issues

    // Save the index of generated memos
    const memosIndexPath = path.join(
      documentBuilderBasePath,
      `legal-memos-index-${chatId}.json` // Renamed file back
    );
    try {
      fs.writeFileSync(
        memosIndexPath,
        JSON.stringify(generatedMemosDetails, null, 2), // Use updated details
        "utf8"
      );
      console.log(
        `[MAIN DOC FLOW] Generated legal memos index saved to ${memosIndexPath}`
      );
    } catch (error) {
      console.error(
        `[MAIN DOC FLOW] Error writing legal memos index file: ${error.message}`
      );
    }

    // Save the section list updated with generated memo references
    const sectionListWithMemosPath = path.join(
      documentBuilderBasePath,
      `section-list-with-memos-${chatId}.json` // Renamed file back
    );
    try {
      fs.writeFileSync(
        sectionListWithMemosPath,
        JSON.stringify(finalSectionList, null, 2),
        "utf8"
      );
      console.log(
        `[MAIN DOC FLOW] Section list with generated memo references saved to ${sectionListWithMemosPath}`
      );
    } catch (error) {
      console.error(
        `[MAIN DOC FLOW] Error writing section list with memo references file: ${error.message}`
      );
    }

    sendProgress({
      step: 5,
      status: "complete",
      progress: 100, // Ensure progress is set for completion
      message: "Legal memo generation complete using LegalQnA workspaces.",
      data: {
        memosIndex: generatedMemosDetails,
        sectionList: finalSectionList,
      },
    });

    await new Promise((r) => setImmediate(r)); // YIELD AFTER STEP 5 COMPLETE

    // Step 6: Draft Individual Sections (Iterative)
    checkAbort(); // Check for abort before starting Step 6

    // Uses iterative context window management for large sections with extensive documentation
    sendProgress({
      step: 6,
      status: "in_progress",
      message: "Drafting individual sections with iterative processing...",
      ...(finalSectionList.length > 0 && { total: finalSectionList.length }),
    });

    // Process sections using iterative drafting
    const draftedSections = await processIterativeSectionDraftingList(
      finalSectionList,
      {
        legalTask,
        processedDocuments,
        workspacePath,
        mainDocNameInitial,
        temperature,
        AllPrompts,
        chatId,
        documentBuilderBasePath,
      },
      contextWindowManager,
      tokenTracker,
      {
        // Progress callback for UI updates
        onSectionProgress: (sectionIndex, sectionTitle, status) => {
          const isComplete = status === "Complete";
          const isDrafting = status.includes("Drafting");
          const isError = status === "Error";

          // Format message to match expected translation patterns
          let message;
          if (isComplete) {
            message = `Drafted Section ${sectionIndex + 1}: ${sectionTitle}`;
          } else if (isDrafting) {
            message = `Drafting Section ${sectionIndex + 1}: ${sectionTitle}...`;
          } else {
            message = `Drafting Section ${sectionIndex + 1}: ${sectionTitle}...`;
          }

          sendProgress({
            step: 6,
            message: message,
            subStep: sectionIndex + 1,
            // Only set total if we actually have sections to process
            ...(finalSectionList.length > 0 && {
              total: finalSectionList.length,
            }),
            label: sectionTitle,
            // Fix: Set proper status and progress for completed sections
            status: isComplete ? "complete" : isError ? "error" : "in_progress",
            progress: isComplete ? 100 : isDrafting ? -1 : 0,
          });
        },
        // Token usage callback for metrics
        onTokenUsage: (sectionIndex, tokenUsage) => {
          const sectionNumber = sectionIndex + 1;
          metrics[`draft_sec_${sectionNumber}_tokens`] = tokenUsage.totalTokens;
          metrics[`draft_sec_${sectionNumber}_iterations`] =
            tokenUsage.iterations;
          if (tokenUsage.isIterative) {
            metrics[`draft_sec_${sectionNumber}_iterative`] = true;
          }
        },
      }
    );

    // Update finalSectionList with drafted content
    finalSectionList.splice(0, finalSectionList.length, ...draftedSections);

    const sectionListWithDraftsPath = path.join(
      documentBuilderBasePath,
      `section-list-with-drafts-${chatId}.json`
    );
    try {
      fs.writeFileSync(
        sectionListWithDraftsPath,
        JSON.stringify(finalSectionList, null, 2),
        "utf8"
      );
      console.log(
        `[MAIN DOC FLOW] Section list with drafts saved to ${sectionListWithDraftsPath}`
      );
    } catch (error) {
      console.error(
        `[MAIN DOC FLOW] Error writing section list with drafts file: ${error.message}`
      );
    }

    sendProgress({
      step: 6,
      progress: 100,
      status: "complete",
      message: "Individual section drafting complete.",
      data: { sectionList: finalSectionList },
    });

    await new Promise((r) => setImmediate(r)); // YIELD AFTER STEP 6 COMPLETE

    // Step 7: Combine Drafted Sections (Refined) -> (Direct Combination)
    checkAbort(); // Check for abort before starting Step 7

    sendProgress({
      step: 7,
      status: "in_progress",
      message: "Combining drafted sections into final document...",
    });

    // Write each drafted section directly to disk to avoid building a very large
    // string in memory. This helps reduce peak RAM usage on lower memory hosts.
    const finalDocPath = path.join(
      documentBuilderBasePath,
      `final-document-${chatId}.md`
    );

    let finalCombinedDocumentContent = "";

    try {
      // Use a Promise to properly handle the asynchronous stream operations
      await new Promise((resolve, reject) => {
        const finalDocStream = fs.createWriteStream(finalDocPath, {
          encoding: "utf8",
        });

        // Handle stream errors
        finalDocStream.on("error", (error) => {
          console.error(
            `[MAIN DOC FLOW] Error writing to stream: ${error.message}`
          );
          reject(error);
        });

        // Handle stream completion
        finalDocStream.on("finish", () => {
          console.log(
            `[MAIN DOC FLOW] Stream finished writing to ${finalDocPath}`
          );
          resolve();
        });

        // Write each section to the stream
        finalSectionList.forEach((section, idx) => {
          const title =
            section.title ||
            section.Description ||
            `Section ${section.index_number || idx + 1}`;
          const content = section.draftedContent || "(Content not available)";
          const separator =
            idx === finalSectionList.length - 1 ? "" : "\n\n---\n\n";
          finalDocStream.write(`## ${title}\n\n${content}${separator}`);
        });

        // End the stream and wait for completion
        finalDocStream.end();
      });

      // Read the completed file
      finalCombinedDocumentContent = fs.readFileSync(finalDocPath, "utf8");

      if (!finalCombinedDocumentContent) {
        console.warn(
          "[MAIN DOC FLOW] Final combined document came back empty in Step 7."
        );
        metrics[`combine_doc_error`] = "No drafted sections or empty content";
      } else {
        console.log(
          `[MAIN DOC FLOW] Final combined document saved to ${finalDocPath} (${finalCombinedDocumentContent.length} characters)`
        );
      }

      // Now that we've successfully read the file, free memory
      finalSectionList.forEach((section) => {
        section.draftedContent = null;
      });
    } catch (error) {
      console.error(
        `[MAIN DOC FLOW] Error during final document combination: ${error.message}`
      );
      metrics[`final_doc_save_error`] = error.message;

      // Fallback: try to use the combineSectionOutputs helper function
      // Note: We haven't cleared draftedContent yet, so it should still be available
      console.log("[MAIN DOC FLOW] Attempting fallback combination method...");
      try {
        const sectionOutputsForCombine = finalSectionList.map((s) => ({
          title:
            s.title || s.Description || `Section ${s.index_number || "N/A"}`,
          content: s.draftedContent || "(Content not available)",
        }));

        console.log(
          `[MAIN DOC FLOW] Attempting to combine ${sectionOutputsForCombine.length} sections using fallback method`
        );
        finalCombinedDocumentContent = combineSectionOutputs(
          sectionOutputsForCombine
        );

        if (finalCombinedDocumentContent) {
          fs.writeFileSync(finalDocPath, finalCombinedDocumentContent, "utf8");
          console.log(
            `[MAIN DOC FLOW] Fallback combination successful (${finalCombinedDocumentContent.length} characters)`
          );
        } else {
          console.warn(
            "[MAIN DOC FLOW] Fallback combination returned empty content"
          );
          metrics[`fallback_combine_empty`] =
            "combineSectionOutputs returned empty string";
        }
      } catch (fallbackError) {
        console.error(
          `[MAIN DOC FLOW] Fallback combination also failed: ${fallbackError.message}`
        );
        metrics[`fallback_combine_error`] = fallbackError.message;
      }

      // Now free memory regardless of fallback success/failure
      finalSectionList.forEach((section) => {
        section.draftedContent = null;
      });
    }

    sendProgress({
      step: 7,
      status: "complete",
      progress: 100, // Ensure progress is set for completion
      message: "Document combination complete.",
      data: {
        finalDocumentPath: finalDocPath,
        combinedPreview: finalCombinedDocumentContent.substring(0, 200) + "...",
      },
    });

    // Generate and log comprehensive token usage report
    const tokenReport = tokenTracker.generateReport();
    console.log(
      "[MAIN DOC FLOW] Token Usage Report:",
      JSON.stringify(tokenReport, null, 2)
    );

    // Add token usage to metrics
    metrics.tokenUsageReport = {
      summary: tokenReport.summary,
      utilizationRate: tokenReport.summary.budgetUtilization,
      totalIterations: tokenReport.iterativeMetrics.totalIterations,
      iterativeProcessingUsed:
        tokenReport.iterativeMetrics.totalIterations > finalSectionList.length,
    };

    console.log("[MAIN DOC FLOW] Processing fully finished.");
    console.log("[MAIN DOC FLOW] Metrics:", JSON.stringify(metrics, null, 2));
    return finalCombinedDocumentContent;
  } catch (error) {
    // Handle abort signals using shared helper
    return handleAbortSignal(error, purgeDocumentBuilder, writeResponseChunk);
  }
}

module.exports = {
  runMainDocFlow,
};
