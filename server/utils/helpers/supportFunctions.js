const { getLLMProvider } = require("./index");
const { SystemSettings } = require("../../models/systemSettings");

/**
 * Get the appropriate LLM provider for a support function
 * @param {string} functionType - Type of support function ('promptUpgrade', 'validation', 'manualTime')
 * @param {object|null} fallbackProvider - Optional fallback LLM provider instance
 * @returns {Promise<object|null>} - LLM provider instance or null
 */
async function getSupportFunctionLLM(functionType, fallbackProvider = null) {
  try {
    // Get the toggle setting for this function type
    const settingKey = `supportFunction${functionType.charAt(0).toUpperCase()}${functionType.slice(1)}`;
    const isEnabled = await SystemSettings.getValueOrFallback(
      { label: settingKey },
      "false"
    );

    // If the support function is not enabled, use fallback or default logic
    if (isEnabled !== "true" && isEnabled !== true) {
      return fallbackProvider;
    }

    // If enabled, try to use the support function LLM
    const supportProvider = process.env.LLM_PROVIDER_SUPPORT;
    if (
      supportProvider &&
      supportProvider.trim() !== "" &&
      supportProvider !== "undefined"
    ) {
      // Use the _SUPPORT suffix to get the correct model preferences
      const supportLLM = getLLMProvider({
        provider: supportProvider,
        settings_suffix: "_SUPPORT",
      });
      if (supportLLM) {
        console.log(
          `Using support function LLM (${supportProvider}) for ${functionType}`
        );
        return supportLLM;
      } else {
        console.warn(
          `Failed to initialize support function LLM (${supportProvider}), falling back`
        );
      }
    } else {
      console.warn(
        `Support function ${functionType} is enabled but LLM_PROVIDER_SUPPORT is not set, falling back`
      );
    }

    // Fall back to provided fallback or null
    return fallbackProvider;
  } catch (error) {
    console.error(
      `Error getting support function LLM for ${functionType}:`,
      error
    );
    return fallbackProvider;
  }
}

/**
 * Get LLM provider for prompt upgrade function
 * @param {object|null} fallbackProvider - Optional fallback LLM provider instance
 * @returns {Promise<object|null>} - LLM provider instance or null
 */
async function getPromptUpgradeLLM(fallbackProvider = null) {
  return getSupportFunctionLLM("promptUpgrade", fallbackProvider);
}

/**
 * Get LLM provider for validation function
 * @param {object|null} fallbackProvider - Optional fallback LLM provider instance
 * @returns {Promise<object|null>} - LLM provider instance or null
 */
async function getValidationLLM(fallbackProvider = null) {
  return getSupportFunctionLLM("validation", fallbackProvider);
}

/**
 * Get LLM provider for manual time estimation function
 * @param {object|null} fallbackProvider - Optional fallback LLM provider instance
 * @returns {Promise<object|null>} - LLM provider instance or null
 */
async function getManualTimeLLM(fallbackProvider = null) {
  return getSupportFunctionLLM("manualTime", fallbackProvider);
}

module.exports = {
  getSupportFunctionLLM,
  getPromptUpgradeLLM,
  getValidationLLM,
  getManualTimeLLM,
};
