// Simplified Integration Tests for streamCDB
const EventEmitter = require("events");

// Setup basic mocks
jest.mock("../utils/helpers/tiktoken", () => {
  class FakeTokenManager {
    countFromString(str = "") {
      return str.split(/\s+/).filter(Boolean).length;
    }
  }
  return { TokenManager: FakeTokenManager };
});

const mockGetChatCompletion = jest.fn();
jest.doMock("../utils/helpers", () => ({
  getLLMProvider: () => ({
    compressMessages: jest.fn(async (messages) => messages),
    getChatCompletion: mockGetChatCompletion,
    defaultTemp: 0.7,
    model: "mock-model",
    metrics: { lastCompletionTokens: 0 },
    promptWindowLimit: jest.fn(() => 128000),
  }),
}));

const mockSystemSettingsGet = jest.fn();
jest.doMock("../models/systemSettings", () => ({
  SystemSettings: {
    get: mockSystemSettingsGet,
    getValueOrFallback: jest.fn(async (clause, fallback) => {
      const result = await mockSystemSettingsGet(clause);
      return result?.value ?? fallback;
    }),
  },
}));

jest.doMock("../models/workspace", () => ({
  Workspace: {
    where: jest.fn().mockResolvedValue([]),
    get: jest.fn().mockResolvedValue(null),
  },
}));

jest.doMock("../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn().mockResolvedValue({ chat: { id: 1 } }),
  },
}));

const mockPurgeDocumentBuilder = jest.fn().mockReturnValue(0);
jest.doMock("../utils/files", () => ({
  purgeDocumentBuilder: mockPurgeDocumentBuilder,
}));

const mockGenerateLegalMemo = jest.fn().mockResolvedValue({
  memo: "mock memo",
  tokenCount: 5,
  sources: [],
});

jest.doMock("../utils/helpers/legalMemo", () => ({
  generateLegalMemo: mockGenerateLegalMemo,
}));

// Mock file operations
const mockFileOperations = {
  existsSync: jest.fn().mockReturnValue(true),
  readdirSync: jest.fn().mockReturnValue(["doc1.json", "doc2.json"]),
  readFileSync: jest.fn(),
  mkdirSync: jest.fn(),
  writeFileSync: jest.fn(),
  createWriteStream: jest.fn(),
  rmSync: jest.fn(),
};

jest.doMock("fs", () => {
  const actual = jest.requireActual("fs");
  return {
    ...actual,
    ...mockFileOperations,
  };
});

const { streamChatWithWorkspaceCDB } = require("../utils/chats/streamCDB");

jest.setTimeout(15000);

describe("StreamCDB Integration Tests", () => {
  let consoleSpy;

  beforeEach(() => {
    // Silence console logs
    consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});

    // Reset all mocks
    jest.clearAllMocks();

    // Setup default behaviors
    mockSystemSettingsGet.mockResolvedValue(null);
    mockGetChatCompletion.mockResolvedValue({
      textResponse: "Default response",
      metrics: { lastCompletionTokens: 5 },
    });
    mockGenerateLegalMemo.mockResolvedValue({
      memo: "Default memo",
      tokenCount: 5,
      sources: [],
    });

    // Setup file operations
    mockFileOperations.readFileSync.mockImplementation((filePath) => {
      if (filePath.includes("final-document-") && filePath.endsWith(".md")) {
        return `## Test Document\n\nThis is a test document.`;
      }
      return JSON.stringify({
        pageContent: "test content",
        token_count_estimate: 10,
        metadata: { title: "Test Document" },
      });
    });

    const mockStream = {
      write: jest.fn(),
      end: jest.fn(),
      on: jest.fn((event, callback) => {
        if (event === "finish") {
          setTimeout(callback, 10);
        }
      }),
    };
    mockFileOperations.createWriteStream.mockReturnValue(mockStream);
  });

  afterEach(() => {
    if (consoleSpy) {
      consoleSpy.mockRestore();
    }
    jest.clearAllTimers();
  });

  describe("Basic Functionality", () => {
    test("CDB function executes without throwing errors", async () => {
      const { request, response, chunks } = createMockRequestResponse();
      const workspace = createTestWorkspace("basic-test");
      const chatId = "basic-test-123";
      const cdbOptions = ["Basic Test", "Instructions", null];
      const legalTaskConfig = { flowType: "noMain" };

      // This should complete without throwing
      await expect(
        streamChatWithWorkspaceCDB(
          request,
          response,
          workspace,
          "Basic Test",
          "chat",
          { id: 1, role: "admin" },
          null,
          [],
          chatId,
          false,
          false,
          "",
          null,
          "default",
          false,
          null,
          false,
          cdbOptions,
          legalTaskConfig
        )
      ).resolves.not.toThrow();

      // Verify cleanup was called
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({ uuid: chatId });
    });

    test("Response chunks are generated", async () => {
      const { request, response, chunks } = createMockRequestResponse();
      const workspace = createTestWorkspace("response-test");
      const chatId = "response-test-456";
      const cdbOptions = ["Response Test", "", null];
      const legalTaskConfig = { flowType: "noMain" };

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Response Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        chatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        cdbOptions,
        legalTaskConfig
      );

      // Should generate some response chunks
      expect(chunks.length).toBeGreaterThan(0);
    });
  });

  describe("Error Handling", () => {
    test("Cleanup occurs even when LLM fails", async () => {
      const { request, response } = createMockRequestResponse();
      const workspace = createTestWorkspace("error-test");
      const chatId = "error-test-123";
      const cdbOptions = ["Error Test", "", null];
      const legalTaskConfig = { flowType: "noMain" };

      // Mock LLM to fail
      mockGetChatCompletion.mockRejectedValue(new Error("LLM Error"));

      // The function should handle the error gracefully
      await expect(
        streamChatWithWorkspaceCDB(
          request,
          response,
          workspace,
          "Error Test",
          "chat",
          { id: 1, role: "admin" },
          null,
          [],
          chatId,
          false,
          false,
          "",
          null,
          "default",
          false,
          null,
          false,
          cdbOptions,
          legalTaskConfig
        )
      ).resolves.not.toThrow();

      // Verify cleanup still occurred
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({ uuid: chatId });
    });

    test("Request abort triggers cleanup", async () => {
      const { request, response } = createMockRequestResponse();
      const workspace = createTestWorkspace("abort-test");
      const chatId = "abort-test-456";
      const cdbOptions = ["Abort Test", "", null];
      const legalTaskConfig = { flowType: "noMain" };

      const streamPromise = streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Abort Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        chatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        cdbOptions,
        legalTaskConfig
      );

      // Simulate request abort
      setTimeout(() => {
        request.emit("close");
        request.emit("aborted");
      }, 100);

      await streamPromise;

      // Verify cleanup occurred
      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({ uuid: chatId });
    });
  });

  describe("Different Flow Types", () => {
    test("Main flow type is handled", async () => {
      const { request, response } = createMockRequestResponse();
      const workspace = createTestWorkspace("main-flow-test");
      const chatId = "main-flow-test-123";
      const cdbOptions = ["Main Flow Test", "Instructions", "mainDoc.pdf"];
      const legalTaskConfig = { flowType: "main" };

      await expect(
        streamChatWithWorkspaceCDB(
          request,
          response,
          workspace,
          "Main Flow Test",
          "chat",
          { id: 1, role: "admin" },
          null,
          [],
          chatId,
          false,
          false,
          "",
          null,
          "default",
          false,
          null,
          false,
          cdbOptions,
          legalTaskConfig
        )
      ).resolves.not.toThrow();

      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({ uuid: chatId });
    });

    test("Reference flow type is handled", async () => {
      const { request, response } = createMockRequestResponse();
      const workspace = createTestWorkspace("ref-flow-test");
      const chatId = "ref-flow-test-789";
      const referenceFiles = ["policy.json"];
      const cdbOptions = [
        "Reference Flow Test",
        "Instructions",
        null,
        "referenceFiles",
        referenceFiles,
      ];
      const legalTaskConfig = { flowType: "referenceFiles" };

      await expect(
        streamChatWithWorkspaceCDB(
          request,
          response,
          workspace,
          "Reference Flow Test",
          "chat",
          { id: 1, role: "admin" },
          null,
          [],
          chatId,
          false,
          false,
          "",
          null,
          "default",
          false,
          null,
          false,
          cdbOptions,
          legalTaskConfig
        )
      ).resolves.not.toThrow();

      expect(mockPurgeDocumentBuilder).toHaveBeenCalledWith({ uuid: chatId });
    });
  });
});

// Helper functions
function createMockRequestResponse() {
  const request = new EventEmitter();
  const chunks = [];
  const response = {
    write: (data) => {
      const strData = data.toString();
      const cleanedStr = strData.startsWith("data: ")
        ? strData.substring(6).trim()
        : strData.trim();
      if (
        cleanedStr &&
        cleanedStr.startsWith("{") &&
        cleanedStr.endsWith("}")
      ) {
        try {
          chunks.push(JSON.parse(cleanedStr));
        } catch (e) {
          // Ignore malformed JSON
        }
      }
    },
    on: jest.fn(),
    removeListener: jest.fn(),
  };
  return { request, response, chunks };
}

function createTestWorkspace(slug) {
  return {
    id: Math.floor(Math.random() * 1000),
    slug,
    type: "document-drafting",
  };
}
