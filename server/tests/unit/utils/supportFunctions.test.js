const {
  getSupportFunctionLLM,
  getPromptUpgradeLLM,
  getValidationLLM,
  getManualTimeLLM,
} = require("../../../utils/helpers/supportFunctions");

// Mock dependencies
jest.mock("../../../models/systemSettings", () => ({
  SystemSettings: {
    getValueOrFallback: jest.fn(),
  },
}));

jest.mock("../../../utils/helpers", () => ({
  getLLMProvider: jest.fn(),
}));

const { SystemSettings } = require("../../../models/systemSettings");
const { getLLMProvider } = require("../../../utils/helpers");

describe("Support Functions Helper", () => {
  afterEach(() => {
    jest.clearAllMocks();
    // Clear environment variables properly
    delete process.env.LLM_PROVIDER_SUPPORT;
    delete process.env.LLM_PROVIDER_PU;
    delete process.env.LLMProvider_VA;
    delete process.env.LLM_PROVIDER;
  });

  describe("getSupportFunctionLLM", () => {
    beforeEach(() => {
      // Set up default mocks
      SystemSettings.getValueOrFallback.mockResolvedValue("false");
      getLLMProvider.mockReturnValue({
        provider: "openai",
        model: "gpt-4",
        config: { apiKey: "test-key" },
      });
    });

    test("should return null when function is disabled", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("false");

      const result = await getSupportFunctionLLM("PromptUpgrade");

      expect(SystemSettings.getValueOrFallback).toHaveBeenCalledWith(
        { label: "supportFunctionPromptUpgrade" },
        "false"
      );
      expect(result).toBeNull();
    });

    test("should return support LLM when function is enabled and support provider is set", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      process.env.LLM_PROVIDER_SUPPORT = "anthropic";

      const mockLLM = {
        provider: "anthropic",
        model: "claude-3",
        config: { apiKey: "test-key" },
      };
      getLLMProvider.mockReturnValue(mockLLM);

      const result = await getSupportFunctionLLM("PromptUpgrade");

      expect(getLLMProvider).toHaveBeenCalledWith({
        provider: "anthropic",
        settings_suffix: "_SUPPORT",
      });
      expect(result).toEqual(mockLLM);
    });

    test("should return fallback LLM when function is enabled but support provider not available", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      // Ensure LLM_PROVIDER_SUPPORT is not set
      delete process.env.LLM_PROVIDER_SUPPORT;
      // Clear the mock to ensure getLLMProvider is not called when no support provider is set
      getLLMProvider.mockClear();

      const fallbackLLM = {
        provider: "openai",
        model: "gpt-3.5-turbo",
        config: { apiKey: "fallback-key" },
      };

      const result = await getSupportFunctionLLM("PromptUpgrade", fallbackLLM);

      // Since LLM_PROVIDER_SUPPORT is not set, getLLMProvider should not be called
      expect(getLLMProvider).not.toHaveBeenCalled();
      expect(result).toEqual(fallbackLLM);
    });

    test("should handle getLLMProvider returning null gracefully", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      process.env.LLM_PROVIDER_SUPPORT = "invalid-provider";
      getLLMProvider.mockReturnValue(null);

      const result = await getSupportFunctionLLM("PromptUpgrade");

      expect(result).toBeNull();
    });

    test("should handle SystemSettings errors gracefully", async () => {
      SystemSettings.getValueOrFallback.mockRejectedValue(
        new Error("Database error")
      );

      const result = await getSupportFunctionLLM("PromptUpgrade");

      expect(result).toBeNull();
    });

    test("should handle undefined environment variables", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      // Ensure all environment variables are not set
      delete process.env.LLM_PROVIDER_SUPPORT;
      // Clear the mock to ensure getLLMProvider is not called when no support provider is set
      getLLMProvider.mockClear();

      const result = await getSupportFunctionLLM("PromptUpgrade");

      // Since LLM_PROVIDER_SUPPORT is not set, getLLMProvider should not be called
      expect(getLLMProvider).not.toHaveBeenCalled();
      expect(result).toBeNull();
    });
  });

  describe("getPromptUpgradeLLM", () => {
    beforeEach(() => {
      SystemSettings.getValueOrFallback.mockResolvedValue("false");
      getLLMProvider.mockReturnValue({
        provider: "openai",
        model: "gpt-4",
        config: { apiKey: "test-key" },
      });
    });

    test("should return support LLM when enabled", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      process.env.LLM_PROVIDER_SUPPORT = "anthropic";

      const mockLLM = {
        provider: "anthropic",
        model: "claude-3",
        config: { apiKey: "test-key" },
      };
      getLLMProvider.mockReturnValue(mockLLM);

      const result = await getPromptUpgradeLLM();

      expect(SystemSettings.getValueOrFallback).toHaveBeenCalledWith(
        { label: "supportFunctionPromptUpgrade" },
        "false"
      );
      expect(result).toEqual(mockLLM);
    });

    test("should use provided fallback when support function disabled", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("false");

      const providedFallback = {
        provider: "custom",
        model: "custom-model",
        config: { apiKey: "custom-key" },
      };

      const result = await getPromptUpgradeLLM(providedFallback);

      expect(result).toEqual(providedFallback);
    });
  });

  describe("getValidationLLM", () => {
    beforeEach(() => {
      SystemSettings.getValueOrFallback.mockResolvedValue("false");
      getLLMProvider.mockReturnValue({
        provider: "openai",
        model: "gpt-4",
        config: { apiKey: "test-key" },
      });
    });

    test("should return support LLM when enabled", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      process.env.LLM_PROVIDER_SUPPORT = "anthropic";

      const mockLLM = {
        provider: "anthropic",
        model: "claude-3",
        config: { apiKey: "test-key" },
      };
      getLLMProvider.mockReturnValue(mockLLM);

      const result = await getValidationLLM();

      expect(SystemSettings.getValueOrFallback).toHaveBeenCalledWith(
        { label: "supportFunctionValidation" },
        "false"
      );
      expect(result).toEqual(mockLLM);
    });

    test("should use provided fallback when support function disabled", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("false");

      const providedFallback = {
        provider: "gemini",
        model: "gemini-pro",
        config: { apiKey: "validation-key" },
      };

      const result = await getValidationLLM(providedFallback);

      expect(result).toEqual(providedFallback);
    });
  });

  describe("getManualTimeLLM", () => {
    beforeEach(() => {
      SystemSettings.getValueOrFallback.mockResolvedValue("false");
      getLLMProvider.mockReturnValue({
        provider: "openai",
        model: "gpt-4",
        config: { apiKey: "test-key" },
      });
    });

    test("should return support LLM when enabled", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      process.env.LLM_PROVIDER_SUPPORT = "anthropic";

      const mockLLM = {
        provider: "anthropic",
        model: "claude-3",
        config: { apiKey: "test-key" },
      };
      getLLMProvider.mockReturnValue(mockLLM);

      const result = await getManualTimeLLM();

      expect(SystemSettings.getValueOrFallback).toHaveBeenCalledWith(
        { label: "supportFunctionManualTime" },
        "false"
      );
      expect(result).toEqual(mockLLM);
    });

    test("should use provided fallback when support function disabled", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("false");

      const providedFallback = {
        provider: "custom",
        model: "custom-model",
        config: { apiKey: "custom-key" },
      };

      const result = await getManualTimeLLM(providedFallback);

      expect(result).toEqual(providedFallback);
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle empty string toggle values", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("");

      const result = await getSupportFunctionLLM("PromptUpgrade");

      expect(result).toBeNull();
    });

    test("should handle null toggle values", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue(null);

      const result = await getSupportFunctionLLM("PromptUpgrade");

      expect(result).toBeNull();
    });

    test("should handle empty string environment variables", async () => {
      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      process.env.LLM_PROVIDER_SUPPORT = "";

      const result = await getSupportFunctionLLM("PromptUpgrade");

      expect(result).toBeNull();
    });
  });

  describe("Integration with Multiple Functions", () => {
    test("should work independently for different functions", async () => {
      // Set up different states for each function
      SystemSettings.getValueOrFallback
        .mockResolvedValueOnce("true") // prompt upgrade enabled
        .mockResolvedValueOnce("false") // validation disabled
        .mockResolvedValueOnce("true"); // manual time enabled

      process.env.LLM_PROVIDER_SUPPORT = "anthropic";

      const supportLLM = {
        provider: "anthropic",
        model: "claude-3",
        config: { apiKey: "support-key" },
      };

      getLLMProvider.mockReturnValue(supportLLM);

      const promptUpgradeResult = await getPromptUpgradeLLM();
      const validationResult = await getValidationLLM();
      const manualTimeResult = await getManualTimeLLM();

      expect(promptUpgradeResult).toEqual(supportLLM);
      expect(validationResult).toBeNull(); // disabled, no fallback provided
      expect(manualTimeResult).toEqual(supportLLM);
    });

    test("should handle mixed enabled/disabled states correctly", async () => {
      // Enable only validation
      SystemSettings.getValueOrFallback.mockImplementation((query) => {
        if (query.label === "supportFunctionValidation") {
          return Promise.resolve("true");
        }
        return Promise.resolve("false");
      });

      process.env.LLM_PROVIDER_SUPPORT = "openai";

      const supportLLM = {
        provider: "openai",
        model: "gpt-4",
        config: { apiKey: "support-key" },
      };
      getLLMProvider.mockReturnValue(supportLLM);

      const promptUpgradeResult = await getPromptUpgradeLLM();
      const validationResult = await getValidationLLM();
      const manualTimeResult = await getManualTimeLLM();

      expect(promptUpgradeResult).toBeNull();
      expect(validationResult).toEqual(supportLLM);
      expect(manualTimeResult).toBeNull();
    });
  });
});
