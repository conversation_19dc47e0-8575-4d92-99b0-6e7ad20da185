const { systemEndpoints } = require("../../../endpoints/system");

// Mock dependencies
jest.mock("../../../models/systemSettings", () => ({
  SystemSettings: {
    updateSettings: jest.fn(),
  },
}));

jest.mock("../../../utils/helpers/updateENV", () => ({
  updateENV: jest.fn(),
}));

jest.mock("../../../models/eventLogs", () => ({
  EventLogs: {
    logEvent: jest.fn(),
  },
}));

// Mock other dependencies that system.js might require
jest.mock("../../../models/vectors", () => ({
  DocumentVectors: {
    find: jest.fn().mockResolvedValue([]),
    count: jest.fn().mockResolvedValue(0),
    delete: jest.fn().mockResolvedValue({ count: 0 }),
    createMany: jest.fn().mockResolvedValue({ count: 0 }),
    create: jest.fn().mockResolvedValue({}),
  },
}));

jest.mock("../../../models/organization", () => ({
  Organization: jest.fn(),
}));

const { SystemSettings } = require("../../../models/systemSettings");
const { updateENV } = require("../../../utils/helpers/updateENV");
const { EventLogs } = require("../../../models/eventLogs");

const mockRequest = (
  body = {},
  user = { id: 1, role: "admin", username: "testadmin" }
) => ({
  body,
  locals: { user },
  ip: "127.0.0.1",
  session: {},
  get: jest.fn(),
  header: jest.fn(),
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.sendStatus = jest.fn().mockReturnValue(res);
  res.end = jest.fn().mockReturnValue(res);
  res.locals = { user: { id: 1 } };
  return res;
};

describe("System Endpoints - Support Functions LLM", () => {
  let app;
  let routeHandler;

  beforeEach(() => {
    app = {
      get: jest.fn(),
      post: jest.fn(),
      delete: jest.fn(),
      put: jest.fn(),
    };
    systemEndpoints(app);

    // Find the POST route handler for support functions LLM
    const postCall = app.post.mock.calls.find(
      (call) => call[0] === "/system/support-functions-llm"
    );
    if (postCall && postCall.length > 1) {
      // Skip middleware and get the actual handler
      routeHandler = postCall[postCall.length - 1];
    }
    updateENV.mockResolvedValue({ error: false });
    SystemSettings.updateSettings.mockResolvedValue({ success: true });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /system/support-functions-llm", () => {
    test("should successfully update all settings", async () => {
      const req = mockRequest({
        LLMProvider_SUPPORT: "openai",
        OpenAiModelPref_SUPPORT: "gpt-4o-mini",
        supportFunctionPromptUpgrade: true,
        supportFunctionValidation: false,
        supportFunctionManualTime: true,
      });
      const res = mockResponse();
      await routeHandler(req, res);

      expect(updateENV).toHaveBeenCalledWith(
        {
          LLMProviderSupport: "openai",
          OpenAiModelPref_SUPPORT: "gpt-4o-mini",
        },
        false,
        1
      );
      expect(SystemSettings.updateSettings).toHaveBeenCalledWith({
        supportFunctionPromptUpgrade: true,
        supportFunctionValidation: false,
        supportFunctionManualTime: true,
      });
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: "Support functions LLM settings updated successfully.",
      });
    });

    test("should handle invalid provider", async () => {
      const req = mockRequest({ LLMProvider_SUPPORT: "invalid" });
      const res = mockResponse();
      await routeHandler(req, res);

      expect(updateENV).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error:
          "Invalid LLMProviderSupport. Must be one of openai, anthropic, gemini, system-standard.",
      });
    });

    test("should log events correctly", async () => {
      const req = mockRequest({
        LLMProvider_SUPPORT: "anthropic",
        AnthropicModelPref_SUPPORT: "claude-3-opus",
        supportFunctionPromptUpgrade: true,
      });
      const res = mockResponse();
      await routeHandler(req, res);

      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "support_functions_llm_updated",
        {
          llmProvider: "anthropic",
          anthropicModel: "claude-3-opus",
          promptUpgrade: true,
        },
        1
      );
    });
  });

  describe("Event Logging", () => {
    test("should log provider-only updates", async () => {
      const req = mockRequest({
        LLMProvider_SUPPORT: "openai",
      });
      const res = mockResponse();

      updateENV.mockResolvedValue({ success: true });

      await routeHandler(req, res);

      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "support_functions_llm_updated",
        {
          llmProvider: "openai",
          promptUpgrade: undefined,
          validation: undefined,
          manualTime: undefined,
        },
        req.locals.user.id
      );
    });

    test("should log toggle-only updates", async () => {
      const req = mockRequest({
        supportFunctionManualTime: true,
      });
      const res = mockResponse();

      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      await routeHandler(req, res);

      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "support_functions_llm_updated",
        {
          llmProvider: undefined,
          promptUpgrade: undefined,
          validation: undefined,
          manualTime: true,
        },
        req.locals.user.id
      );
    });
  });

  describe("Response Messages", () => {
    test("should provide detailed success message for full update", async () => {
      const req = mockRequest({
        LLMProvider_SUPPORT: "anthropic",
        supportFunctionPromptUpgrade: true,
      });
      const res = mockResponse();

      updateENV.mockResolvedValue({ success: true });
      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      await routeHandler(req, res);

      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: "Support functions LLM settings updated successfully.",
      });
    });

    test("should provide specific success message for provider-only update", async () => {
      const req = mockRequest({
        LLMProvider_SUPPORT: "anthropic",
      });
      const res = mockResponse();

      updateENV.mockResolvedValue({ success: true });

      await routeHandler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: "Support functions LLM settings updated successfully.",
      });
    });

    test("should provide specific success message for toggles-only update", async () => {
      const req = mockRequest({
        supportFunctionPromptUpgrade: true,
      });
      const res = mockResponse();

      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      await routeHandler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: "Support functions LLM settings updated successfully.",
      });
    });
  });

  describe("Validation and Security", () => {
    test("should require admin access", () => {
      // Check that the route uses proper middleware
      const postCall = app.post.mock.calls.find(
        (call) => call[0] === "/system/support-functions-llm"
      );

      expect(postCall).toBeDefined();
      expect(postCall.length).toBeGreaterThan(2); // Should have middleware before handler
    });

    test("should handle invalid provider", async () => {
      const req = mockRequest({
        LLMProvider_SUPPORT: "invalid-provider",
      });
      const res = mockResponse();

      await routeHandler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error:
          "Invalid LLMProviderSupport. Must be one of openai, anthropic, gemini, system-standard.",
      });
    });
  });
});
