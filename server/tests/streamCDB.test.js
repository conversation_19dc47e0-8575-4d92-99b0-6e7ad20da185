// Tests for streamChatWithWorkspaceCDB phases
const EventEmitter = require("events");

// Mock heavy dependencies up front with simpler, more reliable implementations
jest.mock("../utils/helpers/tiktoken", () => {
  class FakeTokenManager {
    countFromString(str = "") {
      return str.split(/\s+/).filter(Boolean).length;
    }
  }
  return { TokenManager: FakeTokenManager };
});

// Simplified LLM provider mock
const mockGetChatCompletion = jest.fn();
jest.doMock("../utils/helpers", () => ({
  getLLMProvider: () => ({
    compressMessages: jest.fn(async (messages) => messages),
    getChatCompletion: mockGetChatCompletion,
    defaultTemp: 0.7,
    model: "mock-model",
    metrics: { lastCompletionTokens: 0 },
    promptWindowLimit: jest.fn(() => 128000),
  }),
}));

// Simplified SystemSettings mock
const mockSystemSettingsGet = jest.fn();
jest.doMock("../models/systemSettings", () => ({
  SystemSettings: {
    get: mockSystemSettingsGet,
    getValueOrFallback: jest.fn(async (clause, fallback) => {
      const result = await mockSystemSettingsGet(clause);
      return result?.value ?? fallback;
    }),
  },
}));

// Simplified workspace mocks
jest.doMock("../models/workspace", () => ({
  Workspace: {
    where: jest.fn().mockResolvedValue([]),
    get: jest.fn().mockResolvedValue(null),
  },
}));

jest.doMock("../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn().mockResolvedValue({ chat: { id: 1 } }),
  },
}));

// Simplified file operations mock
const mockPurgeDocumentBuilder = jest.fn().mockReturnValue(0);
jest.doMock("../utils/files", () => ({
  purgeDocumentBuilder: mockPurgeDocumentBuilder,
}));

// Simplified legal memo mock
const mockGenerateLegalMemo = jest.fn();
jest.doMock("../utils/helpers/legalMemo", () => ({
  generateLegalMemo: mockGenerateLegalMemo,
}));

// Mock the flow functions directly to return expected content
const mockRunMainDocFlow = jest.fn();
const mockRunNoMainDocFlow = jest.fn();
const mockRunReferenceFilesFlow = jest.fn();

jest.doMock("../utils/chats/flows/mainDoc", () => ({
  runMainDocFlow: mockRunMainDocFlow,
}));

jest.doMock("../utils/chats/flows/noMainDoc", () => ({
  runNoMainDocFlow: mockRunNoMainDocFlow,
}));

jest.doMock("../utils/chats/flows/referenceFlow", () => ({
  runReferenceFilesFlow: mockRunReferenceFilesFlow,
}));

// Mock flow dispatcher to use our mocked flows
jest.doMock("../utils/chats/flowDispatcher", () => {
  const originalModule = jest.requireActual("../utils/chats/flowDispatcher");
  return {
    ...originalModule,
    runFlow: jest.fn(async (options) => {
      const { cdbOptions = [], legalTask = null } = options;

      // Use the same flow determination logic as the real dispatcher
      const mainDocName =
        options.mainDocName || (cdbOptions && cdbOptions[2]) || null;
      const explicitFlowType = cdbOptions?.[3];

      let flowType;

      // Case 1: Explicit flow type
      if (
        explicitFlowType &&
        ["main", "noMain", "referenceFiles"].includes(explicitFlowType)
      ) {
        flowType = explicitFlowType;
      }
      // Case 2: Legal task configuration
      else if (
        legalTask &&
        legalTask.flowType &&
        ["main", "noMain", "referenceFiles"].includes(legalTask.flowType)
      ) {
        flowType = legalTask.flowType;
      }
      // Case 3: Determine by mainDocName
      else if (mainDocName) {
        flowType = "main";
      } else {
        flowType = "noMain";
      }

      if (flowType === "main") {
        return await mockRunMainDocFlow(options);
      } else if (flowType === "noMain") {
        return await mockRunNoMainDocFlow(options);
      } else if (flowType === "referenceFiles") {
        return await mockRunReferenceFilesFlow(options);
      }

      return "Default mock document content";
    }),
  };
});

// Simplified file system mock
const mockFileOperations = {
  existsSync: jest.fn(),
  readdirSync: jest.fn(),
  readFileSync: jest.fn(),
  mkdirSync: jest.fn(),
  writeFileSync: jest.fn(),
  createWriteStream: jest.fn(),
  rmSync: jest.fn(),
};

jest.doMock("fs", () => ({
  ...jest.requireActual("fs"),
  ...mockFileOperations,
}));

// Import after mocks
const { streamChatWithWorkspaceCDB } = require("../utils/chats/streamCDB");

// Increase timeout for integration tests
jest.setTimeout(15000);

describe("streamChatWithWorkspaceCDB Integration Tests", () => {
  // Helper function to create a mock response collector
  const createMockResponse = () => {
    const chunks = [];
    const response = {
      write: jest.fn((data) => {
        const strData = data.toString();
        const cleanedStr = strData.startsWith("data: ")
          ? strData.substring(6).trim()
          : strData.trim();

        if (cleanedStr && cleanedStr !== "[DONE]") {
          try {
            const parsedChunk = JSON.parse(cleanedStr);
            chunks.push(parsedChunk);
          } catch (e) {
            // Ignore malformed chunks in tests
          }
        }
      }),
      on: jest.fn(),
      removeListener: jest.fn(),
      end: jest.fn(),
      writableEnded: false,
    };

    return { response, chunks };
  };

  // Helper function to setup default mocks
  const setupDefaultMocks = (flowType = "noMain") => {
    // Reset all mocks
    Object.values(mockFileOperations).forEach((mock) => mock.mockClear());
    mockGetChatCompletion.mockClear();
    mockSystemSettingsGet.mockClear();
    mockGenerateLegalMemo.mockClear();
    mockPurgeDocumentBuilder.mockClear();
    mockRunMainDocFlow.mockClear();
    mockRunNoMainDocFlow.mockClear();
    mockRunReferenceFilesFlow.mockClear();

    // Default file operations
    mockFileOperations.existsSync.mockReturnValue(true);
    mockFileOperations.readdirSync.mockReturnValue(["test-doc.json"]);
    mockFileOperations.readFileSync.mockReturnValue(
      JSON.stringify({
        pageContent: "Test document content",
        token_count_estimate: 10,
        metadata: { title: "Test Document" },
      })
    );
    mockFileOperations.mkdirSync.mockReturnValue(undefined);
    mockFileOperations.writeFileSync.mockReturnValue(undefined);

    const mockStream = {
      write: jest.fn(),
      end: jest.fn(),
      on: jest.fn((event, callback) => {
        if (event === "finish") setTimeout(callback, 5);
      }),
    };
    mockFileOperations.createWriteStream.mockReturnValue(mockStream);

    // Default SystemSettings (no custom prompts)
    mockSystemSettingsGet.mockResolvedValue(null);

    // Default legal memo
    mockGenerateLegalMemo.mockResolvedValue({
      memo: "Test legal memo",
      tokenCount: 50,
      sources: [],
    });

    // Setup flow-specific mocks
    if (flowType === "main") {
      setupMainFlowMocks();
    } else if (flowType === "noMain") {
      setupNoMainFlowMocks();
    } else if (flowType === "referenceFiles") {
      setupReferenceFlowMocks();
    }
  };

  // Helper for main flow mocks
  const setupMainFlowMocks = () => {
    mockRunMainDocFlow.mockResolvedValue(
      "## Test Section\n\nMain flow document content"
    );
  };

  // Helper for noMain flow mocks
  const setupNoMainFlowMocks = () => {
    mockRunNoMainDocFlow.mockResolvedValue(
      "## Test Section\n\nNo main document flow content"
    );
  };

  // Helper for reference flow mocks
  const setupReferenceFlowMocks = () => {
    mockRunReferenceFilesFlow.mockResolvedValue(
      "## Compliance Analysis\n\nCompliance analysis content"
    );
  };

  // Helper to create test workspace and options
  const createTestData = (flowType = "noMain") => {
    const workspace = {
      id: Math.floor(Math.random() * 1000),
      slug: `test-${flowType}-${Date.now()}`,
      type: "document-drafting",
    };

    const mockChatId = `test-${flowType}-${Date.now()}`;

    // Set up cdbOptions based on flow type
    let mockCdbOptions;
    if (flowType === "main") {
      mockCdbOptions = [
        `Test ${flowType} Task`, // [0]: legalPrompt
        "Test instructions", // [1]: customInstructions
        "main-doc.pdf", // [2]: mainDocNameFromOptions
        "main", // [3]: explicitFlowType
      ];
    } else if (flowType === "referenceFiles") {
      mockCdbOptions = [
        `Test ${flowType} Task`, // [0]: legalPrompt
        "Test instructions", // [1]: customInstructions
        null, // [2]: mainDocNameFromOptions
        "referenceFiles", // [3]: explicitFlowType
        ["reference1.json", "reference2.json"], // [4]: referenceFiles (if needed)
      ];
    } else {
      // noMain flow
      mockCdbOptions = [
        `Test ${flowType} Task`, // [0]: legalPrompt
        "Test instructions", // [1]: customInstructions
        null, // [2]: mainDocNameFromOptions
        "noMain", // [3]: explicitFlowType
      ];
    }

    const mockLegalTaskConfig = { flowType };

    return { workspace, mockChatId, mockCdbOptions, mockLegalTaskConfig };
  };

  beforeEach(() => {
    // Clear any remaining timers
    jest.clearAllTimers();
  });

  afterEach(() => {
    // Clean up any remaining timers
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  describe("Main Document Flow", () => {
    test("should complete main flow with all 7 steps", async () => {
      setupDefaultMocks("main");
      const { workspace, mockChatId, mockCdbOptions, mockLegalTaskConfig } =
        createTestData("main");
      const { response, chunks } = createMockResponse();
      const request = new EventEmitter();

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Test Main Flow Task",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Verify the flow was called
      expect(mockRunMainDocFlow).toHaveBeenCalled();

      // Verify final document content
      const finalContent = chunks.find(
        (c) => c.type === "textResponse" && c.textResponse && c.close === true
      );
      expect(finalContent).toBeDefined();
      expect(finalContent.textResponse).toContain("Main flow document content");

      // Note: Cleanup is handled by the flow dispatcher, which we've mocked
      // So we don't need to verify cleanup calls in this integration test
    });
  });

  describe("No Main Document Flow", () => {
    test("should complete noMain flow with all 7 steps", async () => {
      setupDefaultMocks("noMain");
      const { workspace, mockChatId, mockCdbOptions, mockLegalTaskConfig } =
        createTestData("noMain");
      const { response, chunks } = createMockResponse();
      const request = new EventEmitter();

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Test NoMain Flow Task",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Verify the flow was called
      expect(mockRunNoMainDocFlow).toHaveBeenCalled();

      // Verify final content
      const finalContent = chunks.find(
        (c) => c.type === "textResponse" && c.textResponse && c.close === true
      );
      expect(finalContent).toBeDefined();
      expect(finalContent.textResponse).toContain(
        "No main document flow content"
      );
    });
  });

  describe("Reference Files Flow", () => {
    test("should complete referenceFiles flow with 5 steps", async () => {
      setupReferenceFlowMocks();
      const { workspace, mockChatId, mockCdbOptions, mockLegalTaskConfig } =
        createTestData("referenceFiles");
      const { response, chunks } = createMockResponse();
      const request = new EventEmitter();

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Test Reference Flow Task",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Verify the flow was called
      expect(mockRunReferenceFilesFlow).toHaveBeenCalled();

      // Verify final content
      const finalContent = chunks.find(
        (c) => c.type === "textResponse" && c.textResponse && c.close === true
      );
      expect(finalContent).toBeDefined();
      expect(finalContent.textResponse).toContain("Compliance Analysis");
    });
  });

  describe("Error Handling", () => {
    test("should handle flow errors gracefully", async () => {
      setupDefaultMocks("noMain");

      // Make the flow fail
      mockRunNoMainDocFlow.mockRejectedValueOnce(new Error("Flow Error"));

      const { workspace, mockChatId, mockCdbOptions, mockLegalTaskConfig } =
        createTestData("noMain");
      const { response, chunks } = createMockResponse();
      const request = new EventEmitter();

      await expect(
        streamChatWithWorkspaceCDB(
          request,
          response,
          workspace,
          "Test Error Handling",
          "chat",
          { id: 1, role: "admin" },
          null,
          [],
          mockChatId,
          false,
          false,
          "",
          null,
          "default",
          false,
          null,
          false,
          mockCdbOptions,
          mockLegalTaskConfig
        )
      ).resolves.not.toThrow();

      // Should still attempt cleanup even on error - this happens in the flowDispatcher finally block
      // Since we mocked the flowDispatcher, we should check that our mocked flow was called
      expect(mockRunNoMainDocFlow).toHaveBeenCalled();
    });

    test("should handle file operation errors", async () => {
      setupDefaultMocks("noMain");

      // Make file operations fail
      mockFileOperations.writeFileSync.mockImplementationOnce(() => {
        throw new Error("File write error");
      });

      const { workspace, mockChatId, mockCdbOptions, mockLegalTaskConfig } =
        createTestData("noMain");
      const { response, chunks } = createMockResponse();
      const request = new EventEmitter();

      await expect(
        streamChatWithWorkspaceCDB(
          request,
          response,
          workspace,
          "Test File Error",
          "chat",
          { id: 1, role: "admin" },
          null,
          [],
          mockChatId,
          false,
          false,
          "",
          null,
          "default",
          false,
          null,
          false,
          mockCdbOptions,
          mockLegalTaskConfig
        )
      ).resolves.not.toThrow();
    });
  });

  describe("Custom Prompts", () => {
    test("should use custom system prompt when available", async () => {
      const customPrompt = "Custom test system prompt";

      // Setup custom prompt in SystemSettings
      mockSystemSettingsGet.mockImplementation(async ({ label }) => {
        if (label === "cdb_document_summary_system_prompt") {
          return { value: customPrompt };
        }
        return null;
      });

      setupDefaultMocks("noMain");

      const { workspace, mockChatId, mockCdbOptions, mockLegalTaskConfig } =
        createTestData("noMain");
      const { response, chunks } = createMockResponse();
      const request = new EventEmitter();

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Test Custom Prompt",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      expect(mockRunNoMainDocFlow).toHaveBeenCalled();
    });
  });

  describe("Frontend Message Compatibility", () => {
    test("should send textResponse for final content, not textResponseChunk", async () => {
      setupDefaultMocks("noMain");

      const { workspace, mockChatId, mockCdbOptions, mockLegalTaskConfig } =
        createTestData("noMain");
      const { response, chunks } = createMockResponse();
      const request = new EventEmitter();

      await streamChatWithWorkspaceCDB(
        request,
        response,
        workspace,
        "Frontend Compatibility Test",
        "chat",
        { id: 1, role: "admin" },
        null,
        [],
        mockChatId,
        false,
        false,
        "",
        null,
        "default",
        false,
        null,
        false,
        mockCdbOptions,
        mockLegalTaskConfig
      );

      // Critical test: final content should be textResponse, not textResponseChunk
      const finalContent = chunks.find(
        (c) => c.type === "textResponse" && c.textResponse && c.close === true
      );

      expect(finalContent).toBeDefined();
      expect(finalContent.type).toBe("textResponse");

      // Should not have any textResponseChunk messages for final content
      const chunkMessages = chunks.filter(
        (c) => c.type === "textResponseChunk"
      );
      expect(chunkMessages.length).toBe(0);
    });
  });
});
