const {
  generateLegalTaskPrompt,
} = require("../../endpoints/generateLegalTaskPrompt");
const { systemEndpoints } = require("../../endpoints/system");

// Mock dependencies for integration testing
jest.mock("../../models/systemSettings", () => ({
  SystemSettings: {
    updateSettings: jest.fn(),
    getValueOrFallback: jest.fn(),
  },
}));

jest.mock("../../utils/helpers", () => ({
  ...jest.requireActual("../../utils/helpers"),
  getLLMProvider: jest.fn(),
}));

jest.mock("../../models/eventLogs", () => ({
  EventLogs: {
    logEvent: jest.fn(),
  },
}));

jest.mock("../../utils/helpers/updateENV", () => ({
  updateENV: jest.fn(),
}));

// Mock other dependencies that might be needed
jest.mock("../../utils/middleware/validatedRequest", () =>
  jest.fn((req, res, next) => next())
);

jest.mock("../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: jest.fn(() => (req, res, next) => next()),
  ROLES: {
    admin: "admin",
    manager: "manager",
    superuser: "superuser",
    all: "all",
  },
}));

const { SystemSettings } = require("../../models/systemSettings");
const { getLLMProvider } = require("../../utils/helpers");
const { EventLogs } = require("../../models/eventLogs");
const { updateENV } = require("../../utils/helpers/updateENV");

// Mock Express app for endpoint testing
const mockApp = () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  listen: jest.fn(),
  use: jest.fn(),
});

const mockRequest = (body = {}, user = { id: 1, role: "admin" }) => ({
  body,
  locals: { user },
  ip: "127.0.0.1",
  session: {},
  get: jest.fn(),
  header: jest.fn(),
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.sendStatus = jest.fn().mockReturnValue(res);
  res.end = jest.fn().mockReturnValue(res);
  res.locals = { user: { id: 1 } };
  return res;
};

describe("Support Functions Integration Tests", () => {
  afterEach(() => {
    jest.clearAllMocks();
    // Clear environment variables
    delete process.env.LLM_PROVIDER_SUPPORT;
    delete process.env.LLM_PROVIDER_PU;
    delete process.env.LLMProvider_VA;
    delete process.env.LLM_PROVIDER;
  });

  describe("System Endpoint Integration", () => {
    let app;
    let routeHandler;

    beforeEach(() => {
      app = mockApp();
      systemEndpoints(app);

      // Find the support functions LLM route handler
      const postCall = app.post.mock.calls.find(
        (call) => call[0] === "/system/support-functions-llm"
      );
      if (postCall && postCall.length > 1) {
        routeHandler = postCall[postCall.length - 1];
      }
    });

    test("should handle full configuration update", async () => {
      const req = mockRequest({
        LLMProvider_SUPPORT: "anthropic",
        supportFunctionPromptUpgrade: true,
        supportFunctionValidation: false,
        supportFunctionManualTime: true,
      });
      const res = mockResponse();

      updateENV.mockResolvedValue({ error: false });
      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      await routeHandler(req, res);

      // Verify environment update includes cleared model preferences for other providers
      expect(updateENV).toHaveBeenCalledWith(
        {
          LLMProviderSupport: "anthropic",
          // Only the relevant model preference should be included
        },
        false,
        req.locals.user.id
      );
      expect(SystemSettings.updateSettings).toHaveBeenCalledWith({
        supportFunctionPromptUpgrade: true,
        supportFunctionValidation: false,
        supportFunctionManualTime: true,
      });
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "support_functions_llm_updated",
        {
          llmProvider: "anthropic",
          promptUpgrade: true,
          validation: false,
          manualTime: true,
        },
        req.locals.user.id
      );
      expect(res.status).toHaveBeenCalledWith(200);
    });

    test("should handle partial updates correctly", async () => {
      const req = mockRequest({
        supportFunctionPromptUpgrade: true,
      });
      const res = mockResponse();

      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      await routeHandler(req, res);

      expect(updateENV).not.toHaveBeenCalled();
      expect(SystemSettings.updateSettings).toHaveBeenCalledWith({
        supportFunctionPromptUpgrade: true,
      });
      expect(res.status).toHaveBeenCalledWith(200);
    });

    test("should handle error scenarios gracefully", async () => {
      const req = mockRequest({
        LLMProvider_SUPPORT: "invalid-provider",
      });
      const res = mockResponse();

      updateENV.mockResolvedValue({
        error: true,
        errorMessage: "Invalid provider",
      });

      await routeHandler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error:
          "Invalid LLMProviderSupport. Must be one of openai, anthropic, gemini, system-standard.",
      });
    });

    test("should handle complete support functions LLM configuration workflow", async () => {
      // Clear any previous mock calls
      jest.clearAllMocks();

      // Test successful configuration update
      const successReq = mockRequest({
        LLMProvider_SUPPORT: "anthropic",
        AnthropicModelPref_SUPPORT: "claude-3-opus",
        supportFunctionPromptUpgrade: true,
        supportFunctionValidation: false,
        supportFunctionManualTime: true,
      });
      const successRes = mockResponse();

      updateENV.mockResolvedValue({ error: false });
      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      await routeHandler(successReq, successRes);

      expect(successRes.status).toHaveBeenCalledWith(200);
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "support_functions_llm_updated",
        expect.objectContaining({
          llmProvider: "anthropic",
          anthropicModel: "claude-3-opus",
          promptUpgrade: true,
          validation: false,
          manualTime: true,
        }),
        1
      );

      // Verify environment update includes cleared model preferences for other providers
      expect(updateENV).toHaveBeenCalledWith(
        {
          LLMProviderSupport: "anthropic",
          AnthropicModelPref_SUPPORT: "claude-3-opus",
        },
        false,
        successReq.locals.user.id
      );
      expect(SystemSettings.updateSettings).toHaveBeenCalledWith({
        supportFunctionPromptUpgrade: true,
        supportFunctionValidation: false,
        supportFunctionManualTime: true,
      });
    });
  });

  describe("Support Functions Helper Integration", () => {
    test("should integrate with LLM provider selection", async () => {
      // Import the helper functions
      const {
        getSupportFunctionLLM,
        getPromptUpgradeLLM,
        getValidationLLM,
        getManualTimeLLM,
      } = require("../../utils/helpers/supportFunctions");

      SystemSettings.getValueOrFallback.mockResolvedValue("true");
      process.env.LLM_PROVIDER_SUPPORT = "anthropic";

      const mockLLM = {
        provider: "anthropic",
        model: "claude-3-sonnet",
        config: { apiKey: "test-key" },
      };
      getLLMProvider.mockReturnValue(mockLLM);

      const promptUpgradeResult = await getPromptUpgradeLLM();
      const validationResult = await getValidationLLM();
      const manualTimeResult = await getManualTimeLLM();

      expect(promptUpgradeResult).toEqual(mockLLM);
      expect(validationResult).toEqual(mockLLM);
      expect(manualTimeResult).toEqual(mockLLM);

      expect(SystemSettings.getValueOrFallback).toHaveBeenCalledTimes(3);
      expect(getLLMProvider).toHaveBeenCalledTimes(3);
      expect(getLLMProvider).toHaveBeenCalledWith({
        provider: "anthropic",
        settings_suffix: "_SUPPORT",
      });
    });

    test("should handle mixed enable/disable states", async () => {
      const {
        getPromptUpgradeLLM,
        getValidationLLM,
        getManualTimeLLM,
      } = require("../../utils/helpers/supportFunctions");

      // Enable only prompt upgrade
      SystemSettings.getValueOrFallback.mockImplementation((query) => {
        if (query.label === "supportFunctionPromptUpgrade") {
          return Promise.resolve("true");
        }
        return Promise.resolve("false");
      });

      process.env.LLM_PROVIDER_SUPPORT = "openai";

      const mockLLM = {
        provider: "openai",
        model: "gpt-4",
        config: { apiKey: "test-key" },
      };
      getLLMProvider.mockReturnValue(mockLLM);

      const promptUpgradeResult = await getPromptUpgradeLLM();
      const validationResult = await getValidationLLM();
      const manualTimeResult = await getManualTimeLLM();

      expect(promptUpgradeResult).toEqual(mockLLM);
      expect(validationResult).toBeNull(); // disabled, no fallback
      expect(manualTimeResult).toBeNull(); // disabled, no fallback
    });

    test("should handle fallback behavior", async () => {
      const {
        getPromptUpgradeLLM,
      } = require("../../utils/helpers/supportFunctions");

      SystemSettings.getValueOrFallback.mockResolvedValue("false");

      const fallbackLLM = {
        provider: "fallback",
        model: "fallback-model",
        config: { apiKey: "fallback-key" },
      };

      const result = await getPromptUpgradeLLM(fallbackLLM);

      expect(result).toEqual(fallbackLLM);
      expect(getLLMProvider).not.toHaveBeenCalled();
    });
  });

  describe("End-to-End Workflow", () => {
    test("should complete full support function configuration workflow", async () => {
      // Step 1: Configure support functions via endpoint
      const app = mockApp();
      systemEndpoints(app);

      const postCall = app.post.mock.calls.find(
        (call) => call[0] === "/system/support-functions-llm"
      );
      const routeHandler = postCall[postCall.length - 1];

      const configReq = mockRequest({
        LLMProvider_SUPPORT: "anthropic",
        supportFunctionPromptUpgrade: true,
        supportFunctionValidation: true,
        supportFunctionManualTime: false,
      });
      const configRes = mockResponse();

      updateENV.mockResolvedValue({ error: false });
      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      await routeHandler(configReq, configRes);

      expect(configRes.status).toHaveBeenCalledWith(200);

      // Step 2: Use support functions
      const {
        getPromptUpgradeLLM,
        getValidationLLM,
        getManualTimeLLM,
      } = require("../../utils/helpers/supportFunctions");

      SystemSettings.getValueOrFallback.mockImplementation((query) => {
        if (query.label === "supportFunctionPromptUpgrade") {
          return Promise.resolve("true");
        }
        if (query.label === "supportFunctionValidation") {
          return Promise.resolve("true");
        }
        if (query.label === "supportFunctionManualTime") {
          return Promise.resolve("false");
        }
        return Promise.resolve("false");
      });

      process.env.LLM_PROVIDER_SUPPORT = "anthropic";

      const supportLLM = {
        provider: "anthropic",
        model: "claude-3",
        config: { apiKey: "support-key" },
      };
      getLLMProvider.mockReturnValue(supportLLM);

      const promptUpgradeResult = await getPromptUpgradeLLM();
      const validationResult = await getValidationLLM();
      const manualTimeResult = await getManualTimeLLM();

      expect(promptUpgradeResult).toEqual(supportLLM);
      expect(validationResult).toEqual(supportLLM);
      expect(manualTimeResult).toBeNull(); // disabled

      // Verify event logging occurred
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "support_functions_llm_updated",
        {
          llmProvider: "anthropic",
          promptUpgrade: true,
          validation: true,
          manualTime: false,
        },
        configReq.locals.user.id
      );
    });

    test("should handle error recovery scenarios", async () => {
      // Test configuration failure followed by successful retry
      const app = mockApp();
      systemEndpoints(app);

      const postCall = app.post.mock.calls.find(
        (call) => call[0] === "/system/support-functions-llm"
      );
      const routeHandler = postCall[postCall.length - 1];

      // First attempt fails
      const failReq = mockRequest({
        LLMProvider_SUPPORT: "invalid",
      });
      const failRes = mockResponse();

      updateENV.mockResolvedValue({
        error: true,
        errorMessage: "Invalid provider",
      });

      await routeHandler(failReq, failRes);

      expect(failRes.status).toHaveBeenCalledWith(400);

      // Second attempt succeeds
      const successReq = mockRequest({
        LLMProvider_SUPPORT: "openai",
      });
      const successRes = mockResponse();

      updateENV.mockResolvedValue({ error: false });

      await routeHandler(successReq, successRes);

      expect(successRes.status).toHaveBeenCalledWith(200);
      expect(EventLogs.logEvent).toHaveBeenCalledWith(
        "support_functions_llm_updated",
        {
          llmProvider: "openai",
          promptUpgrade: undefined,
          validation: undefined,
          manualTime: undefined,
        },
        successReq.locals.user.id
      );
    });
  });

  describe("Performance and Reliability", () => {
    test("should handle concurrent requests", async () => {
      const app = mockApp();
      systemEndpoints(app);

      const postCall = app.post.mock.calls.find(
        (call) => call[0] === "/system/support-functions-llm"
      );
      const routeHandler = postCall[postCall.length - 1];

      const requests = Array.from({ length: 5 }, (_, i) =>
        mockRequest({
          LLMProvider_SUPPORT: "openai",
          OpenAiModelPref_SUPPORT: "gpt-4o-mini",
          supportFunctionPromptUpgrade: true,
        })
      );
      const responses = Array.from({ length: 5 }, () => mockResponse());

      updateENV.mockResolvedValue({ error: false });
      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      // Execute all requests concurrently
      await Promise.all(
        requests.map((req, i) => routeHandler(req, responses[i]))
      );

      // Verify all responses are successful
      responses.forEach((res) => {
        expect(res.status).toHaveBeenCalledWith(200);
      });

      expect(updateENV).toHaveBeenCalledTimes(5);
      expect(SystemSettings.updateSettings).toHaveBeenCalledTimes(5);
      expect(EventLogs.logEvent).toHaveBeenCalledTimes(5);
    });

    test("should handle edge case inputs", async () => {
      const app = mockApp();
      systemEndpoints(app);

      const postCall = app.post.mock.calls.find(
        (call) => call[0] === "/system/support-functions-llm"
      );
      const routeHandler = postCall[postCall.length - 1];

      const edgeCaseReq = mockRequest({
        LLMProvider_SUPPORT: "openai",
        supportFunctionPromptUpgrade: "",
        supportFunctionValidation: 0,
        supportFunctionManualTime: null,
      });
      const edgeCaseRes = mockResponse();

      updateENV.mockResolvedValue({ error: false });
      SystemSettings.updateSettings.mockResolvedValue({ success: true });

      await routeHandler(edgeCaseReq, edgeCaseRes);

      expect(edgeCaseRes.status).toHaveBeenCalledWith(200);
      expect(SystemSettings.updateSettings).toHaveBeenCalledWith({
        supportFunctionPromptUpgrade: "",
        supportFunctionValidation: 0,
        supportFunctionManualTime: null,
      });
    });
  });
});
