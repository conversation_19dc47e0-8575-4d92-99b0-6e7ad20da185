const request = require("supertest");
const express = require("express");
const { EventEmitter } = require("events");

// Simplified mock setup - only mock what's absolutely necessary
jest.mock("../../utils/http", () => ({
  ...jest.requireActual("../../utils/http"),
  userFromSession: jest.fn(),
  multiUserMode: jest.fn().mockReturnValue(false),
  reqBody: jest.fn((req) => req.body),
}));

jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: jest.fn((req, res, next) => next()),
}));

jest.mock("../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: jest.fn(() => (req, res, next) => next()),
  ROLES: { all: "<all>", admin: "admin" },
}));

// Mock workspace models
jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
    getWithUser: jest.fn(),
    where: jest.fn().mockResolvedValue([]),
  },
}));

jest.mock("../../models/workspaceThread", () => ({
  WorkspaceThread: {
    get: jest.fn(),
    where: jest.fn().mockResolvedValue([]),
  },
}));

jest.mock("../../models/workspaceShare", () => ({
  WorkspaceShare: {
    hasAccess: jest.fn().mockResolvedValue(false),
  },
}));

jest.mock("../../models/threadShare", () => ({
  ThreadShare: {
    hasAccess: jest.fn().mockResolvedValue(false),
  },
}));

jest.mock("../../utils/middleware/validWorkspace", () => ({
  validWorkspaceSlug: jest.fn(async (req, res, next) => {
    const workspace = {
      id: 1,
      slug: req.params.slug,
      type: "document-drafting",
      user_id: 1,
    };
    res.locals.workspace = workspace;
    res.locals.chatId = req.query.chatId || `test-chat-${Date.now()}`;
    next();
  }),
  validWorkspaceAndThreadSlug: jest.fn(async (req, res, next) => {
    const workspace = {
      id: 1,
      slug: req.params.slug,
      type: "document-drafting",
      user_id: 1,
    };
    const thread = {
      id: 1,
      slug: req.params.threadSlug,
      workspace_id: workspace.id,
      user_id: 1,
    };
    res.locals.workspace = workspace;
    res.locals.thread = thread;
    res.locals.chatId = req.query.chatId || `test-chat-${Date.now()}`;
    next();
  }),
}));

// Mock the core streamChatWithWorkspace function
const mockStreamChatWithWorkspace = jest.fn();
jest.mock("../../utils/chats/stream", () => ({
  streamChatWithWorkspace: mockStreamChatWithWorkspace,
}));

// Mock the writeResponseChunk function for testing rate limiting
const mockWriteResponseChunk = jest.fn();
jest.mock("../../utils/helpers/chat/responses", () => ({
  writeResponseChunk: mockWriteResponseChunk,
}));

// Mock user model
jest.mock("../../models/user", () => ({
  User: {
    get: jest.fn(),
  },
}));

// Mock system models with minimal setup
jest.mock("../../models/systemSettings", () => ({
  SystemSettings: {
    get: jest.fn().mockResolvedValue({ value: null }),
  },
}));

jest.mock("../../models/workspaceChats", () => ({
  WorkspaceChats: {
    count: jest.fn().mockResolvedValue(0),
  },
}));

jest.mock("../../models/telemetry", () => ({
  Telemetry: {
    sendTelemetry: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock("../../models/eventLogs", () => ({
  EventLogs: {
    logEvent: jest.fn().mockResolvedValue(undefined),
  },
}));

// Import after mocks
const { chatEndpoints } = require("../../endpoints/chat");

describe("Document Drafting API Endpoint", () => {
  let app;
  let mockUser;
  let mockWorkspace;

  beforeAll(() => {
    app = express();
    app.use(express.json());
    chatEndpoints(app);
  });

  beforeEach(() => {
    jest.clearAllMocks();

    // Set up default test data
    mockUser = { id: 1, username: "testuser", role: "admin" };
    mockWorkspace = {
      id: 1,
      slug: "test-workspace",
      type: "document-drafting",
      user_id: 1,
    };

    // Configure default mocks
    require("../../utils/http").userFromSession.mockResolvedValue(mockUser);

    // Reset the stream function mock
    mockStreamChatWithWorkspace.mockReset();
    mockWriteResponseChunk.mockReset();
  });

  describe("POST /workspace/:slug/stream-chat/:slugModule with CDB", () => {
    const createRequest = (workspaceSlug, options = {}) => {
      const defaultOptions = {
        message: "Test legal task",
        chatId: `test-chat-${Date.now()}`,
        cdbOptions: ["Legal Task", "Instructions", null],
      };

      return request(app)
        .post(
          `/workspace/${workspaceSlug}/stream-chat/document-drafting?cdb=true`
        )
        .send({ ...defaultOptions, ...options })
        .set("Accept", "text/event-stream");
    };

    test("should accept valid document drafting request", async () => {
      // Mock successful streaming
      mockStreamChatWithWorkspace.mockImplementation(async (req, res) => {
        // Simulate streaming response
        res.write(
          `data: ${JSON.stringify({ type: "cdbProgress", step: 1, status: "starting", flowType: "noMain" })}\n\n`
        );
        res.write(
          `data: ${JSON.stringify({ type: "textResponse", textResponse: "Test document content", close: true })}\n\n`
        );
      });

      const response = await createRequest("test-workspace");

      expect(response.status).toBe(200);
      expect(response.headers["content-type"]).toMatch(/text\/event-stream/);
      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);

      // Verify the call arguments structure
      const callArgs = mockStreamChatWithWorkspace.mock.calls[0];
      expect(callArgs[2]).toEqual(mockWorkspace); // workspace
      expect(callArgs[3]).toBe("Test legal task"); // message
      expect(callArgs[5]).toEqual(mockUser); // user
    });

    test("should handle main document flow when main doc specified", async () => {
      mockStreamChatWithWorkspace.mockImplementation(async (req, res) => {
        res.write(
          `data: ${JSON.stringify({ type: "cdbProgress", step: 1, status: "starting", flowType: "main" })}\n\n`
        );
        res.write(
          `data: ${JSON.stringify({ type: "textResponse", textResponse: "Main doc content", close: true })}\n\n`
        );
      });

      const response = await createRequest("test-workspace", {
        cdbOptions: ["Legal Task", "Instructions", "mainDoc.pdf"],
      });

      expect(response.status).toBe(200);
      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);

      // Verify cdbOptions are passed correctly
      const callArgs = mockStreamChatWithWorkspace.mock.calls[0];
      const cdbOptions = callArgs[18]; // Position based on function signature
      expect(cdbOptions).toEqual(["Legal Task", "Instructions", "mainDoc.pdf"]);
    });

    test("should handle no main document flow", async () => {
      mockStreamChatWithWorkspace.mockImplementation(async (req, res) => {
        res.write(
          `data: ${JSON.stringify({ type: "cdbProgress", step: 1, status: "starting", flowType: "noMain" })}\n\n`
        );
        res.write(
          `data: ${JSON.stringify({ type: "textResponse", textResponse: "No main doc content", close: true })}\n\n`
        );
      });

      const response = await createRequest("test-workspace", {
        cdbOptions: ["Legal Task", "Instructions", null],
      });

      expect(response.status).toBe(200);
      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);
    });

    test("should reject requests with empty messages", async () => {
      const response = await createRequest("test-workspace", {
        message: "",
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("Message is empty.");
      expect(mockStreamChatWithWorkspace).not.toHaveBeenCalled();
    });

    test("should reject requests with no message", async () => {
      const response = await createRequest("test-workspace", {
        message: undefined,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe("Message is empty.");
      expect(mockStreamChatWithWorkspace).not.toHaveBeenCalled();
    });

    test("should handle streaming errors gracefully", async () => {
      mockStreamChatWithWorkspace.mockImplementation(async (req, res) => {
        res.write(
          `data: ${JSON.stringify({ type: "cdbProgress", step: 1, status: "starting" })}\n\n`
        );
        throw new Error("Simulated streaming error");
      });

      const response = await createRequest("test-workspace");

      expect(response.status).toBe(200);
      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);
      // The endpoint should handle the error and send abort response
    });

    test("should set correct environment variables for document drafting", async () => {
      const originalLLMProvider = process.env.LLM_PROVIDER_DD;
      const originalEmbedding = process.env.EMBEDDING_ENGINE_DD;
      const originalVectorDB = process.env.VECTOR_DB_DD;

      // Set test environment variables
      process.env.LLM_PROVIDER_DD = "test-llm";
      process.env.EMBEDDING_ENGINE_DD = "test-embedding";
      process.env.VECTOR_DB_DD = "test-vectordb";

      mockStreamChatWithWorkspace.mockImplementation(async (req, res) => {
        res.write(
          `data: ${JSON.stringify({ type: "textResponse", textResponse: "Test", close: true })}\n\n`
        );
      });

      await createRequest("test-workspace");

      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);

      // Restore original values
      process.env.LLM_PROVIDER_DD = originalLLMProvider;
      process.env.EMBEDDING_ENGINE_DD = originalEmbedding;
      process.env.VECTOR_DB_DD = originalVectorDB;
    });

    test("should handle authentication properly", async () => {
      // Test with no user
      require("../../utils/http").userFromSession.mockResolvedValueOnce(null);

      mockStreamChatWithWorkspace.mockImplementation(async (req, res) => {
        res.write(
          `data: ${JSON.stringify({ type: "textResponse", textResponse: "Test", close: true })}\n\n`
        );
      });

      const response = await createRequest("test-workspace");

      // Should still proceed (ROLES.all allows all users)
      expect(response.status).toBe(200);
      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);
    });

    test("should pass through all required parameters", async () => {
      const testParams = {
        message: "Complex legal analysis",
        chatId: "custom-chat-id",
        cdbOptions: ["Task Name", "Custom Instructions", "mainDoc.pdf"],
        legalTaskConfig: { flowType: "main", name: "Contract Analysis" },
        attachments: [{ name: "file1.pdf" }],
        hasUploadedFile: true,
        useDeepSearch: true,
      };

      mockStreamChatWithWorkspace.mockImplementation(async (req, res) => {
        res.write(
          `data: ${JSON.stringify({ type: "textResponse", textResponse: "Complex analysis", close: true })}\n\n`
        );
      });

      const response = await createRequest("test-workspace", testParams);

      expect(response.status).toBe(200);
      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);

      const callArgs = mockStreamChatWithWorkspace.mock.calls[0];

      // Verify key parameters are passed through
      expect(callArgs[3]).toBe(testParams.message); // message
      expect(callArgs[7]).toEqual(testParams.attachments); // attachments
      expect(callArgs[8]).toBe(testParams.chatId); // chatId
      expect(callArgs[18]).toEqual(testParams.cdbOptions); // cdbOptions
      expect(callArgs[19]).toEqual(testParams.legalTaskConfig); // legalTaskConfig
    });
  });

  describe("Error Handling", () => {
    test("should handle internal server errors", async () => {
      // Mock an error in the streaming function
      mockStreamChatWithWorkspace.mockRejectedValue(
        new Error("Internal error")
      );

      const response = await request(app)
        .post(
          "/workspace/test-workspace/stream-chat/document-drafting?cdb=true"
        )
        .send({
          message: "Test message",
          cdbOptions: ["Task", "Instructions", null],
        })
        .set("Accept", "text/event-stream");

      expect(response.status).toBe(200); // SSE starts with 200, error is in stream
      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);
    });

    test("should handle malformed JSON in request body", async () => {
      const response = await request(app)
        .post(
          "/workspace/test-workspace/stream-chat/document-drafting?cdb=true"
        )
        .send("invalid json")
        .set("Content-Type", "application/json")
        .set("Accept", "text/event-stream");

      // Express should handle malformed JSON with 400
      expect(response.status).toBe(400);
      expect(mockStreamChatWithWorkspace).not.toHaveBeenCalled();
    });
  });

  describe("Rate Limiting", () => {
    beforeEach(() => {
      // Mock multi-user mode to return true from a fake response object
      require("../../utils/http").multiUserMode.mockReturnValue(true);

      // Set up user as non-admin
      mockUser.role = "default";
      require("../../utils/http").userFromSession.mockResolvedValue(mockUser);
    });

    test("should check rate limiting configuration", async () => {
      // Test that rate limiting models are accessible and can be called
      const SystemSettings =
        require("../../models/systemSettings").SystemSettings;
      const WorkspaceChats =
        require("../../models/workspaceChats").WorkspaceChats;

      // Verify that rate limiting dependencies are properly mocked
      expect(SystemSettings.get).toBeDefined();
      expect(WorkspaceChats.count).toBeDefined();

      // This is a simple integration test that ensures the endpoint
      // can handle rate limiting scenarios without complex mocking
      const response = await request(app)
        .post(
          "/workspace/test-workspace/stream-chat/document-drafting?cdb=true"
        )
        .send({
          message: "Test message",
          cdbOptions: ["Task", "Instructions", null],
        })
        .set("Accept", "text/event-stream");

      expect(response.status).toBe(200);
    });

    test("should allow requests under rate limit", async () => {
      require("../../models/systemSettings")
        .SystemSettings.get.mockResolvedValueOnce({ value: "true" }) // limit_user_messages
        .mockResolvedValueOnce({ value: "5" }); // message_limit

      // Mock current count under limit
      require("../../models/workspaceChats").WorkspaceChats.count.mockResolvedValue(
        3
      );

      mockStreamChatWithWorkspace.mockImplementation(async (req, res) => {
        res.write(
          `data: ${JSON.stringify({ type: "textResponse", textResponse: "Success", close: true })}\n\n`
        );
      });

      const response = await request(app)
        .post(
          "/workspace/test-workspace/stream-chat/document-drafting?cdb=true"
        )
        .send({
          message: "Test message",
          cdbOptions: ["Task", "Instructions", null],
        })
        .set("Accept", "text/event-stream");

      expect(response.status).toBe(200);
      expect(mockStreamChatWithWorkspace).toHaveBeenCalledTimes(1);
    });
  });
});
