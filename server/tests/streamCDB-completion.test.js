const { streamChatWithWorkspaceCDB } = require("../utils/chats/streamCDB");
const { EventEmitter } = require("events");

// Mock all the dependencies
jest.mock("../utils/chats/flowDispatcher", () => ({
  runFlow: jest.fn(),
}));

jest.mock("../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn(),
  },
}));

jest.mock("../utils/files", () => ({
  purgeDocumentBuilder: jest.fn(),
}));

const { runFlow } = require("../utils/chats/flowDispatcher");
const { WorkspaceChats } = require("../models/workspaceChats");

describe("StreamCDB Completion Handling", () => {
  let mockRequest;
  let mockResponse;
  let responseChunks;
  let workspace;
  let user;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    responseChunks = [];

    // Mock request
    mockRequest = new EventEmitter();

    // Mock response that captures written chunks
    mockResponse = {
      write: jest.fn((data) => {
        const strData = data.toString();
        const cleanedStr = strData.startsWith("data: ")
          ? strData.substring(6).trim()
          : strData.trim();
        if (
          cleanedStr &&
          cleanedStr.startsWith("{") &&
          cleanedStr.endsWith("}")
        ) {
          try {
            responseChunks.push(JSON.parse(cleanedStr));
          } catch (e) {
            // Ignore malformed JSON
          }
        }
      }),
      on: jest.fn(),
      removeListener: jest.fn(),
    };

    workspace = {
      id: 1,
      slug: "test-workspace",
      type: "document-drafting",
    };

    user = {
      id: 1,
      role: "admin",
    };
  });

  describe("Successful Flow Completion", () => {
    test("should send final document as textResponse with close: true", async () => {
      const mockFinalDocument =
        "# Legal Analysis\n\nThis is the final document content with multiple sections.\n\n## Section 1\nContent here...\n\n## Section 2\nMore content...";
      const mockChatId = "test-chat-123";

      // Mock successful flow execution
      runFlow.mockResolvedValue(mockFinalDocument);

      // Mock successful chat creation
      WorkspaceChats.new.mockResolvedValue({
        chat: { id: "saved-chat-456" },
      });

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        workspace,
        "Test legal task",
        "chat",
        user,
        null, // thread
        [], // attachments
        mockChatId,
        false, // isCanvasChat
        false, // preventChatCreation
        "", // settings_suffix
        null, // invoice_ref
        "default", // vectorSearchMode
        false, // hasUploadedFile
        null, // displayMessage
        false, // useDeepSearch
        ["Legal analysis prompt", "Custom instructions", null], // cdbOptions
        { flowType: "main" } // legalTaskConfig
      );

      // Verify that runFlow was called with correct options
      expect(runFlow).toHaveBeenCalledWith(
        expect.objectContaining({
          workspace,
          message: "Test legal task",
          chatId: mockChatId,
          legalTask: { flowType: "main" },
        })
      );

      // Verify that a chat was created with the final document
      expect(WorkspaceChats.new).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        prompt: "Test legal task",
        response: {
          text: mockFinalDocument,
          sources: [],
          type: "chat",
          attachments: [],
        },
        threadId: null,
        user,
        invoice_ref: null,
      });

      // Find the final document response
      const finalDocumentChunk = responseChunks.find(
        (chunk) =>
          chunk.type === "textResponse" &&
          chunk.textResponse === mockFinalDocument &&
          chunk.close === true
      );

      expect(finalDocumentChunk).toBeDefined();
      expect(finalDocumentChunk).toEqual({
        uuid: mockChatId,
        sources: [],
        type: "textResponse",
        textResponse: mockFinalDocument,
        close: true,
        error: false,
        chatId: "saved-chat-456",
      });
    });

    test("should handle flow completion with empty document", async () => {
      const mockChatId = "test-chat-empty";

      // Mock flow returning empty content
      runFlow.mockResolvedValue("");

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        workspace,
        "Test task",
        "chat",
        user,
        null,
        [],
        mockChatId
      );

      // Should not create a chat entry for empty content
      expect(WorkspaceChats.new).not.toHaveBeenCalled();

      // Should send finalizeResponseStream instead
      const finalizeChunk = responseChunks.find(
        (chunk) => chunk.type === "finalizeResponseStream"
      );

      expect(finalizeChunk).toBeDefined();
      expect(finalizeChunk.close).toBe(true);
      expect(finalizeChunk.error).toBe(true); // Empty content is treated as error
    });

    test("should include chatId when document is successfully saved", async () => {
      const mockFinalDocument = "# Test Document\n\nContent here.";
      const mockChatId = "test-chat-789";
      const savedChatId = "saved-chat-789";

      runFlow.mockResolvedValue(mockFinalDocument);
      WorkspaceChats.new.mockResolvedValue({
        chat: { id: savedChatId },
      });

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        workspace,
        "Test task",
        "chat",
        user,
        null,
        [],
        mockChatId
      );

      const finalDocumentChunk = responseChunks.find(
        (chunk) => chunk.type === "textResponse" && chunk.close === true
      );

      expect(finalDocumentChunk.chatId).toBe(savedChatId);
    });
  });

  describe("Error Handling", () => {
    test("should handle flow execution errors", async () => {
      const mockChatId = "test-chat-error";
      const mockError = new Error("Flow execution failed");

      runFlow.mockRejectedValue(mockError);

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        workspace,
        "Test task",
        "chat",
        user,
        null,
        [],
        mockChatId
      );

      // Should not create a chat entry when flow fails
      expect(WorkspaceChats.new).not.toHaveBeenCalled();

      // Should send finalizeResponseStream with error
      const finalizeChunk = responseChunks.find(
        (chunk) => chunk.type === "finalizeResponseStream"
      );

      expect(finalizeChunk).toBeDefined();
      expect(finalizeChunk.close).toBe(true);
      expect(finalizeChunk.error).toBeTruthy(); // Error object is truthy
    });

    test("should handle chat creation errors", async () => {
      const mockFinalDocument = "# Test Document\n\nContent here.";
      const mockChatId = "test-chat-save-error";
      const saveError = new Error("Failed to save chat");

      runFlow.mockResolvedValue(mockFinalDocument);
      WorkspaceChats.new.mockRejectedValue(saveError);

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        workspace,
        "Test task",
        "chat",
        user,
        null,
        [],
        mockChatId
      );

      // Should send abort response when save fails
      const abortChunk = responseChunks.find((chunk) => chunk.type === "abort");

      expect(abortChunk).toBeDefined();
      expect(abortChunk.close).toBe(true);
      expect(abortChunk.error).toBe("Failed to save chat"); // Error message from mock
    });
  });

  describe("Chat Creation Prevention", () => {
    test("should not create chat when preventChatCreation is true", async () => {
      const mockFinalDocument = "# Test Document\n\nContent here.";
      const mockChatId = "test-chat-prevent";

      runFlow.mockResolvedValue(mockFinalDocument);

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        workspace,
        "Test task",
        "chat",
        user,
        null,
        [],
        mockChatId,
        false, // isCanvasChat
        true // preventChatCreation
      );

      // Should not create chat when prevention flag is set
      expect(WorkspaceChats.new).not.toHaveBeenCalled();

      // But should still send the document content
      const finalDocumentChunk = responseChunks.find(
        (chunk) => chunk.type === "textResponse" && chunk.close === true
      );

      expect(finalDocumentChunk).toBeDefined();
      expect(finalDocumentChunk.textResponse).toBe(mockFinalDocument);
      expect(finalDocumentChunk.chatId).toBe(mockChatId); // Uses original chatId
    });
  });

  describe("Flow Integration", () => {
    test("should pass correct options to runFlow", async () => {
      const mockChatId = "test-flow-options";
      const mockCdbOptions = ["Legal prompt", "Instructions", "main-doc.pdf"];
      const mockLegalTaskConfig = { flowType: "main", name: "Test Task" };
      const mockAttachments = [{ name: "file1.pdf" }];
      const mockThread = { id: 123, slug: "test-thread" };

      runFlow.mockResolvedValue("Mock document");

      await streamChatWithWorkspaceCDB(
        mockRequest,
        mockResponse,
        workspace,
        "Test message",
        "document-drafting",
        user,
        mockThread,
        mockAttachments,
        mockChatId,
        true, // isCanvasChat
        false, // preventChatCreation
        "_test", // settings_suffix
        "INV-123", // invoice_ref
        "deep", // vectorSearchMode
        true, // hasUploadedFile
        "Display message", // displayMessage
        true, // useDeepSearch
        mockCdbOptions,
        mockLegalTaskConfig
      );

      expect(runFlow).toHaveBeenCalledWith({
        request: mockRequest,
        response: mockResponse,
        workspace,
        message: "Test message",
        chatMode: "document-drafting",
        user,
        thread: mockThread,
        attachments: mockAttachments,
        chatId: mockChatId,
        isCanvasChat: true,
        preventChatCreation: false,
        settings_suffix: "_test",
        invoice_ref: "INV-123",
        vectorSearchMode: "deep",
        hasUploadedFile: true,
        displayMessage: "Display message",
        useDeepSearch: true,
        cdbOptions: mockCdbOptions,
        mainDocName: "main-doc.pdf", // Extracted from cdbOptions[2]
        legalTask: mockLegalTaskConfig,
        abortSignal: expect.any(Object),
      });
    });
  });
});
