---
description: Guidelines for WCAG 2.1 AA accessibility compliance and implementation
globs:
  - "frontend/**/*.js"
  - "frontend/**/*.jsx"
alwaysApply: false

---

# WCAG Accessibility Compliance Guidelines

## Overview

This document outlines the Web Content Accessibility Guidelines (WCAG) compliance requirements for the IST Legal platform. All frontend components and features must meet WCAG 2.1 AA standards to ensure accessibility for users with disabilities.

## Core WCAG Principles

### 1. Perceivable
Information and user interface components must be presentable to users in ways they can perceive.

### 2. Operable
User interface components and navigation must be operable by all users.

### 3. Understandable
Information and the operation of user interface must be understandable.

### 4. Robust
Content must be robust enough to be interpreted reliably by a wide variety of user agents, including assistive technologies.

## Mandatory ARIA Attributes

### Avoiding ARIA Conflicts

**CRITICAL**: Never combine conflicting ARIA attributes that create semantic confusion:

#### Common ARIA Conflicts to Avoid

1. **`role="alert"` with `aria-live="polite"`**:
   ```jsx
   // ❌ WRONG: Conflicting live region priorities
   <div role="alert" aria-live="polite">
     Error message
   </div>

   // ✅ CORRECT: role="alert" implies aria-live="assertive"
   <div role="alert">
     Error message
   </div>
   ```

2. **Invalid i18next options**:
   ```jsx
   // ❌ WRONG: 'fallback' is not a valid i18next option
   aria-label={t("key", { fallback: "Default text" })}

   // ✅ CORRECT: Use 'defaultValue' for fallback text
   aria-label={t("key", { defaultValue: "Default text" })}
   ```

3. **`role="status"` vs `role="alert"`**:
   - Use `role="alert"` for critical errors that need immediate attention
   - Use `role="status"` with `aria-live="polite"` for non-critical status updates
   ```jsx
   // ✅ For critical errors
   <div role="alert">
     Form submission failed
   </div>

   // ✅ For status updates
   <div role="status" aria-live="polite">
     Document saved successfully
   </div>
   ```

### Alert and Error Messages
- **`role="alert"`**: For critical error messages and alerts
- **`aria-live="assertive"`**: For urgent announcements that should interrupt screen reader activity
- **`aria-live="polite"`**: For non-urgent status updates
- **`aria-atomic="true"`**: When the entire content should be read on updates

```jsx
// Example: Error alert component
<div
  role="alert"
  aria-live="assertive"
  aria-atomic="true"
  className="error-message"
>
  <h4>{t("error.title")}</h4>
  <p>{t("error.description")}</p>
</div>
```

### Form Controls
- **`aria-label`**: When visible label text is insufficient
- **`aria-labelledby`**: To reference other elements that label the control
- **`aria-describedby`**: To reference elements that provide additional description
- **`aria-required="true"`**: For required form fields
- **`aria-invalid="true"`**: For fields with validation errors

```jsx
// Example: Form input with proper ARIA
<div>
  <label htmlFor="email" id="email-label">
    {t("form.email")}
  </label>
  <input
    id="email"
    type="email"
    aria-labelledby="email-label"
    aria-describedby="email-help email-error"
    aria-required="true"
    aria-invalid={hasError}
    {...register("email")}
  />
  <div id="email-help">{t("form.emailHelp")}</div>
  {hasError && (
    <div id="email-error" role="alert">
      {t("form.emailError")}
    </div>
  )}
</div>
```

### Interactive Elements
- **`aria-expanded`**: For collapsible content (dropdowns, accordions)
- **`aria-controls`**: To identify elements controlled by the current element
- **`aria-haspopup`**: For elements that trigger popups or menus
- **`aria-pressed`**: For toggle buttons

```jsx
// Example: Collapsible section
<button
  aria-expanded={isExpanded}
  aria-controls="details-content"
  onClick={() => setIsExpanded(!isExpanded)}
>
  {isExpanded ? t("hideDetails") : t("showDetails")}
</button>
<div id="details-content" hidden={!isExpanded}>
  {/* Content */}
</div>
```

### Navigation and Landmarks
- **`role="navigation"`**: For navigation sections
- **`role="main"`**: For main content area
- **`role="banner"`**: For site header
- **`role="contentinfo"`**: For site footer
- **`aria-current="page"`**: For current page in navigation

## Semantic HTML Requirements

### Use Proper HTML Elements
- Use `<button>` for interactive actions, not `<div>` with click handlers
- Use `<a>` for navigation links
- Use heading hierarchy (`<h1>`, `<h2>`, `<h3>`, etc.) properly
- Use `<form>` elements for form controls
- Use `<fieldset>` and `<legend>` for grouped form controls

```jsx
// Good: Semantic button
<button onClick={handleSubmit} disabled={isLoading}>
  {t("submit")}
</button>

// Bad: Non-semantic clickable div
<div onClick={handleSubmit} className="button-like">
  {t("submit")}
</div>
```

### Form Labels and Structure
- Every form control must have an associated label
- Use `<fieldset>` and `<legend>` for radio button groups and checkboxes
- Provide clear error messages and validation feedback

```jsx
// Example: Radio button group
<fieldset>
  <legend>{t("form.preferredContact")}</legend>
  <div>
    <input type="radio" id="email" name="contact" value="email" />
    <label htmlFor="email">{t("form.email")}</label>
  </div>
  <div>
    <input type="radio" id="phone" name="contact" value="phone" />
    <label htmlFor="phone">{t("form.phone")}</label>
  </div>
</fieldset>
```

## Keyboard Navigation

### Focus Management
- All interactive elements must be keyboard accessible
- Provide visible focus indicators
- Implement logical tab order
- Handle focus trapping in modals and dialogs

```jsx
// Example: Modal with focus trapping
const Modal = ({ isOpen, onClose, children }) => {
  const modalRef = useRef();

  useEffect(() => {
    if (isOpen) {
      // Focus first focusable element
      const firstFocusable = modalRef.current?.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      firstFocusable?.focus();
    }
  }, [isOpen]);

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div
      ref={modalRef}
      role="dialog"
      aria-modal="true"
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      {children}
    </div>
  );
};
```

### Keyboard Shortcuts
- Implement standard keyboard shortcuts where appropriate
- Provide alternatives to mouse-only interactions
- Document keyboard shortcuts for complex interfaces

## Color and Contrast

### Color Contrast Requirements
- Normal text: Minimum 4.5:1 contrast ratio
- Large text (18pt+ or 14pt+ bold): Minimum 3:1 contrast ratio
- UI components and graphics: Minimum 3:1 contrast ratio

### Color Independence
- Never rely solely on color to convey information
- Provide additional visual cues (icons, text, patterns)
- Ensure functionality works in high contrast mode

```jsx
// Example: Status indicator with multiple cues
<div className="status-indicator">
  <Icon name={status === 'success' ? 'check' : 'warning'} />
  <span className={`status-text status-${status}`}>
    {t(`status.${status}`)}
  </span>
</div>
```

## Screen Reader Support

### Content Structure
- Use proper heading hierarchy for page structure
- Provide skip links for main navigation
- Use descriptive link text (avoid "click here")
- Provide alternative text for images

```jsx
// Example: Skip link
<a href="#main-content" className="skip-link">
  {t("accessibility.skipToMain")}
</a>

// Example: Descriptive image alt text
<img
  src="/chart.png"
  alt={t("chart.description", {
    type: "bar chart",
    data: "monthly revenue for 2024"
  })}
/>
```

### Dynamic Content
- Announce dynamic content changes to screen readers
- Use `aria-live` regions for status updates
- Provide context for dynamically inserted content

```jsx
// Example: Live region for status updates
<div aria-live="polite" aria-atomic="true" className="sr-only">
  {statusMessage}
</div>
```

## Component-Specific Guidelines

### Modals and Dialogs
- Use `role="dialog"` or `role="alertdialog"`
- Include `aria-modal="true"`
- Provide `aria-labelledby` and `aria-describedby`
- Implement focus trapping
- Handle Escape key to close

### Data Tables
- Use proper table markup (`<table>`, `<thead>`, `<tbody>`, `<th>`, `<td>`)
- Provide table captions and summaries
- Use `scope` attributes for complex tables
- Implement sortable column headers properly

```jsx
// Example: Accessible data table
<table>
  <caption>{t("table.userList")}</caption>
  <thead>
    <tr>
      <th scope="col">{t("table.name")}</th>
      <th scope="col">{t("table.email")}</th>
      <th scope="col">{t("table.role")}</th>
    </tr>
  </thead>
  <tbody>
    {users.map(user => (
      <tr key={user.id}>
        <td>{user.name}</td>
        <td>{user.email}</td>
        <td>{user.role}</td>
      </tr>
    ))}
  </tbody>
</table>
```

### Form Validation
- Provide clear, specific error messages
- Associate errors with form controls using `aria-describedby`
- Use `aria-invalid` to mark invalid fields
- Announce validation results to screen readers

## Testing Requirements

### Automated Testing
- Run accessibility linting tools (eslint-plugin-jsx-a11y)
- Use automated accessibility testing tools (axe-core, Lighthouse)
- Include accessibility tests in CI/CD pipeline

### Manual Testing
- Test with keyboard navigation only
- Test with screen readers (NVDA, JAWS, VoiceOver)
- Test in high contrast mode
- Test with browser zoom up to 200%
- Test with users who have disabilities

### Testing Checklist
- [ ] All interactive elements are keyboard accessible
- [ ] Focus indicators are visible and clear
- [ ] Screen reader announces all important content
- [ ] Color contrast meets WCAG AA standards
- [ ] Forms have proper labels and error handling
- [ ] Images have appropriate alternative text
- [ ] Page structure uses proper headings
- [ ] Dynamic content changes are announced
- [ ] Modals and dialogs work with assistive technology

## Implementation Checklist

When creating or modifying components:

- [ ] Use semantic HTML elements
- [ ] Add appropriate ARIA attributes
- [ ] Ensure keyboard accessibility
- [ ] Test color contrast ratios
- [ ] Provide alternative text for images
- [ ] Implement proper focus management
- [ ] Test with screen readers
- [ ] Validate with accessibility tools
- [ ] Document accessibility features
- [ ] Include accessibility tests

## Common Mistakes to Avoid

1. **Missing ARIA labels**: Every interactive element needs accessible names
2. **Improper heading hierarchy**: Don't skip heading levels
3. **Color-only information**: Always provide additional visual cues
4. **Keyboard traps**: Ensure users can navigate away from all elements
5. **Missing focus indicators**: All focusable elements need visible focus states
6. **Inadequate error messages**: Provide specific, actionable error information
7. **Inaccessible custom components**: Test all custom UI components thoroughly
8. **Missing alternative text**: All informative images need alt text
9. **Poor contrast**: Ensure sufficient color contrast for all text and UI elements
10. **Unlabeled form controls**: Every form input needs an associated label

## Resources and Tools

### Testing Tools
- **axe DevTools**: Browser extension for accessibility testing
- **Lighthouse**: Built-in Chrome accessibility audit
- **WAVE**: Web accessibility evaluation tool
- **Color Contrast Analyzers**: For checking contrast ratios

### Screen Readers
- **NVDA**: Free Windows screen reader
- **VoiceOver**: Built-in macOS/iOS screen reader
- **JAWS**: Popular Windows screen reader

### Documentation
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [ARIA Authoring Practices Guide](https://www.w3.org/WAI/ARIA/apg/)
- [MDN Accessibility Documentation](https://developer.mozilla.org/en-US/docs/Web/Accessibility)

## Enforcement

Accessibility compliance is **mandatory** for all frontend components. Pull requests that don't meet WCAG AA standards will be returned for revision. Regular accessibility audits will be conducted to ensure ongoing compliance.

Remember: Accessibility is not an afterthought—it should be considered from the beginning of the design and development process.
