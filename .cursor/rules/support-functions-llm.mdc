---
description: "Guidelines for the Support Functions LLM feature and configuration"
globs:
  - frontend/src/pages/GeneralSettings/SupportFunctionsLLM/**
  - server/endpoints/system.js
  - server/utils/helpers/supportFunctions.js
alwaysApply: false
---
# Support Functions LLM Guidelines

## Overview

The Support Functions LLM feature provides centralized configuration for LLM providers used by specific AI support functions in the application. This enables better cost control and model selection for different operational purposes.

## Feature Location

- **Frontend Page:** `/frontend/src/pages/GeneralSettings/SupportFunctionsLLM/index.jsx`
- **Backend Endpoint:** `/server/endpoints/system.js` - `POST /system/support-functions-llm`
- **Support Utility:** `/server/utils/helpers/supportFunctions.js`
- **Access Level:** Admin only

## Architecture

### Configuration Storage

The feature uses a hybrid approach:

1. **Environment Variable:** `LLM_PROVIDER_SUPPORT` - stores the selected LLM provider
2. **Model Preference Environment Variables:** Provider-specific model preferences with `_SUPPORT` suffix:
   - `OpenAiModelPref_SUPPORT` - OpenAI model preference for support functions
   - `AnthropicModelPref_SUPPORT` - Anthropic model preference for support functions
   - `GeminiLLMModelPref_SUPPORT` - Gemini model preference for support functions
3. **Database Settings:** Individual toggle settings in SystemSettings
   - `supportFunctionPromptUpgrade`
   - `supportFunctionValidation`
   - `supportFunctionManualTime`

### Integration Points

The feature integrates with three key AI functions:

1. **Prompt Upgrade** (`generateLegalTaskPrompt` in `/server/endpoints/generateLegalTaskPrompt.js`)
2. **Validation** (`ValidateAnswer` in `/server/endpoints/system.js`)
3. **Manual Time Estimation** (`EstimateManualWork` in `/server/endpoints/system.js`)

## Implementation Guidelines

### Backend Implementation

When modifying AI functions that could benefit from support function LLM selection:

1. **Import the helper:**
   ```javascript
   const { getSupportFunctionLLM } = require("../utils/helpers/supportFunctions");
   ```

2. **Use the appropriate specialized function:**
   - `getPromptUpgradeLLM(fallbackProvider)` for prompt generation
   - `getValidationLLM(fallbackProvider)` for validation tasks
   - `getManualTimeLLM(fallbackProvider)` for time estimation

3. **Provide proper fallback:**
   ```javascript
   // Determine fallback using existing logic
   const fallbackProvider = getLLMProvider({ provider: process.env.LLM_PROVIDER });

   // Check support function and use support LLM if enabled
   const llmProvider = await getSupportFunctionLLM('functionType', fallbackProvider);
   ```

4. **Handle errors gracefully:**
   - Log when support LLM is used vs fallback
   - Provide clear error messages if all providers fail
   - Maintain existing fallback chains

### Key Implementation Detail

The support functions use the `settings_suffix: "_SUPPORT"` parameter when calling `getLLMProvider()`:

```javascript
const supportLLM = getLLMProvider({
  provider: supportProvider,
  settings_suffix: "_SUPPORT",
});
```

This ensures that the correct model preferences are used:
- `OpenAiModelPref_SUPPORT` for OpenAI providers
- `AnthropicModelPref_SUPPORT` for Anthropic providers
- `GeminiLLMModelPref_SUPPORT` for Gemini providers

### Frontend Implementation

When adding UI for support function configuration:

1. **Use the Settings Store pattern** for future enhancements
2. **Include override notices** when support functions are enabled
3. **Provide clear feedback** about which LLM is being used
4. **Follow admin-only access patterns**
5. **Handle model preferences correctly** - only send model preferences that match the selected provider

### Database Schema

Support function toggles are stored as individual SystemSettings:

- **Label:** `supportFunction{FunctionName}` (e.g., `supportFunctionPromptUpgrade`)
- **Value:** String "true" or "false"
- **Default:** "false" (disabled by default)
- **Validation:** Boolean normalization in SystemSettings model

## Security Requirements

1. **Access Control:** Admin-only access enforced by middleware
2. **Input Validation:** All inputs validated through SystemSettings model
3. **Environment Security:** LLM provider names validated against supported providers
4. **Audit Logging:** Configuration changes logged through EventLogs

## Testing Guidelines

### Unit Tests

- Test support function helper logic with various toggle states
- Test fallback behavior when support provider is unavailable
- Test SystemSettings integration and validation
- Test API endpoint request/response handling

### Integration Tests

- Test end-to-end configuration flow
- Test LLM provider switching in actual AI functions
- Test database persistence and environment variable updates
- Test error handling and fallback scenarios

### Manual Testing

- Verify UI functionality with different admin scenarios
- Test LLM provider selection and fallback behavior
- Verify logging and audit trail functionality
- Test internationalization across all supported languages

## Error Handling

### Common Scenarios

1. **Support LLM unavailable:** Falls back to existing provider logic
2. **Invalid configuration:** Uses validation to normalize values
3. **Database errors:** Graceful degradation to defaults
4. **Provider initialization failures:** Logged and handled with fallbacks

### Logging Standards

- **Info:** When support LLM is successfully used
- **Warn:** When fallback occurs due to configuration issues
- **Error:** When all provider initialization attempts fail

## Future Enhancements

When extending this feature:

1. **Add new support functions:**
   - Create new toggle setting in SystemSettings
   - Add specialized helper function
   - Integrate with the target AI function
   - Update documentation

2. **Enhance UI:**
   - Consider migrating to Zustand settings store
   - Add override notices on affected pages
   - Provide usage metrics and monitoring

3. **Improve monitoring:**
   - Add metrics for support function usage
   - Track fallback frequency
   - Monitor LLM provider performance

## Dependencies

- **SystemSettings model:** For configuration storage and validation
- **updateENV system:** For environment variable management
- **getLLMProvider helper:** For LLM provider initialization
- **EventLogs system:** For audit trail
- **Role-based middleware:** For access control

## Documentation

- **Frontend docs:** `/frontend/docs/support-functions-llm.md`
- **Server docs:** `/server/docs/support-functions-llm.md`
- **Translation files:** `/frontend/src/locales/[lang]/supportFunctionsLLM.js`

## Adding a New LLM Provider

To add a new LLM provider to the Support Functions feature, you need to update files in both the backend and frontend.

### 1. Backend Updates (`server/endpoints/system.js`)

In the `POST /system/support-functions-llm` endpoint:

- **Add the provider to `allowedProviders`**:
  ```javascript
  const allowedProviders = ["openai", "anthropic", "gemini", "new-provider"];
  ```
- **Handle the new provider's model preference**: Add an `else if` condition to process the new provider's model preference setting. This requires a new environment variable for the model name (e.g., `NewProviderModelPref_SUPPORT`).
  ```javascript
  // ... existing else if blocks
  } else if (
    LLMProvider_SUPPORT === "new-provider" &&
    NewProviderModelPref_SUPPORT !== undefined
  ) {
    envUpdates.NewProviderModelPref_SUPPORT = NewProviderModelPref_SUPPORT;
  }
  ```

### 2. Frontend Updates (`frontend/src/pages/GeneralSettings/SupportFunctionsLLM/index.jsx`)

- **Update `PROVIDER_MODEL_PREFERENCE_MAPPING`**: Add an entry for the new provider that maps it to its model preference key.
  ```javascript
  const PROVIDER_MODEL_PREFERENCE_MAPPING = {
    // ... existing providers
    "new-provider": "NewProviderModelPref_SUPPORT",
  };
  ```

### 3. Environment Variables (`.env.example`)

- **Add the new model preference variable**:
  ```
  # ...
  NewProviderModelPref_SUPPORT="default-model-for-new-provider"
  ```
